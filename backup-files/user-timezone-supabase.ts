/**
 * Supabase-based Timezone Management
 * 
 * Handles user and organization timezone preferences using Supabase database
 * instead of Clerk metadata. Provides better performance and immediate updates.
 */

import { useUser, useOrganization } from '@clerk/nextjs'
import { useAuth } from '@clerk/nextjs'
import { useEffect, useState, useMemo } from 'react'
import { createClerkSupabaseClient } from './supabase'
import { getEffectiveTimezone, type TimezoneConfig, isValidTimezone } from './timezone'

// Database types
interface UserPreferences {
  id: string
  user_id: string
  org_id?: string
  timezone?: string
  timezone_auto_detected: boolean
  timezone_set_at?: string
  preferences: Record<string, any>
  created_at: string
  updated_at: string
}

interface OrganizationWithTimezone {
  id: string
  clerk_org_id: string
  name: string
  timezone_settings: {
    default_timezone: string
    timezone_policy: 'user_choice' | 'organization_standard' | 'flexible'
    allow_user_override: boolean
  }
}

/**
 * Custom hook to get timezone configuration from Supabase
 * Replaces the Clerk metadata-based approach
 */
export function useTimezoneConfig(): TimezoneConfig & {
  effectiveTimezone: string
  loading: boolean
  userCanOverride: boolean
  isAutoDetected: boolean
} {
  const { user, isLoaded: userLoaded } = useUser()
  const { organization, isLoaded: orgLoaded } = useOrganization()
  const { getToken } = useAuth()
  
  const [userPrefs, setUserPrefs] = useState<UserPreferences | null>(null)
  const [orgSettings, setOrgSettings] = useState<OrganizationWithTimezone | null>(null)
  const [loading, setLoading] = useState(true)
  
  // Create Clerk-authenticated Supabase client
  const supabase = useMemo(() => createClerkSupabaseClient(getToken), [getToken])
  
  // Load user preferences from Supabase
  useEffect(() => {
    if (!userLoaded || !user) return
    
    const loadUserPreferences = async () => {
      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single()
        
        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
          console.error('Error loading user preferences:', error)
          return
        }
        
        setUserPrefs(data)
      } catch (error) {
        console.error('Error loading user preferences:', error)
      }
    }
    
    loadUserPreferences()
  }, [user, userLoaded])
  
  // Load organization settings from Supabase
  useEffect(() => {
    if (!orgLoaded || !organization) return
    
    const loadOrgSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('organizations')
          .select('id, clerk_org_id, name, timezone_settings')
          .eq('clerk_org_id', organization.id)
          .single()
        
        if (error) {
          console.error('Error loading organization settings:', error)
          return
        }
        
        setOrgSettings(data)
      } catch (error) {
        console.error('Error loading organization settings:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadOrgSettings()
  }, [organization, orgLoaded])
  
  // Set loading false when both user and org are loaded
  useEffect(() => {
    if (userLoaded && orgLoaded) {
      setLoading(false)
    }
  }, [userLoaded, orgLoaded])
  
  // Extract values for timezone calculation
  const userTimezone = userPrefs?.timezone
  const isAutoDetected = userPrefs?.timezone_auto_detected ?? false
  const organizationTimezone = orgSettings?.timezone_settings?.default_timezone || 'UTC'
  const timezonePolicy = orgSettings?.timezone_settings?.timezone_policy || 'user_choice'
  const allowUserOverride = orgSettings?.timezone_settings?.allow_user_override ?? true
  
  // Determine if user can override organization timezone
  const userCanOverride = timezonePolicy !== 'organization_standard' && allowUserOverride
  
  // Build timezone config
  const config: TimezoneConfig = {
    userTimezone: userCanOverride ? userTimezone : null,
    organizationTimezone,
    fallbackTimezone: 'UTC'
  }
  
  const effectiveTimezone = getEffectiveTimezone(config)
  
  return {
    ...config,
    effectiveTimezone,
    loading,
    userCanOverride,
    isAutoDetected
  }
}

/**
 * Hook to get timezone update function
 * Updates timezone preference in Supabase database
 */
export function useTimezoneUpdate() {
  const { user } = useUser()
  const { organization } = useOrganization()
  const { getToken } = useAuth()
  
  // Create Clerk-authenticated Supabase client
  const supabase = useMemo(() => createClerkSupabaseClient(getToken), [getToken])
  
  const updateUserTimezone = async (
    timezone: string,
    options: {
      autoDetected?: boolean
    } = {}
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }
      
      if (!isValidTimezone(timezone)) {
        return { success: false, error: 'Invalid timezone' }
      }
      
      // Get organization ID if available
      let orgId: string | null = null
      if (organization) {
        const { data: orgData } = await supabase
          .from('organizations')
          .select('id')
          .eq('clerk_org_id', organization.id)
          .single()
        
        orgId = orgData?.id || null
      }
      
      // Upsert user preferences
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          org_id: orgId,
          timezone,
          timezone_auto_detected: options.autoDetected ?? false,
          timezone_set_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
      
      if (error) {
        console.error('Error updating timezone preference:', error)
        return { success: false, error: error.message }
      }
      
      return { success: true }
    } catch (error) {
      console.error('Error updating user timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return updateUserTimezone
}

/**
 * Hook to get auto-detect timezone function
 * Auto-detects and sets user timezone in Supabase
 */
export function useTimezoneAutoDetect() {
  const { user } = useUser()
  const { getToken } = useAuth()
  const updateUserTimezone = useTimezoneUpdate()
  
  // Create Clerk-authenticated Supabase client
  const supabase = useMemo(() => createClerkSupabaseClient(getToken), [getToken])
  
  const autoDetectUserTimezone = async (): Promise<{ 
    success: boolean 
    timezone?: string 
    error?: string 
  }> => {
    try {
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }
      
      // Check if user already has a manually set timezone
      const { data: userPrefs } = await supabase
        .from('user_preferences')
        .select('timezone, timezone_auto_detected')
        .eq('user_id', user.id)
        .single()
      
      if (userPrefs?.timezone && !userPrefs.timezone_auto_detected) {
        return { 
          success: true, 
          timezone: userPrefs.timezone,
          error: 'User already has manually set timezone'
        }
      }
      
      // Detect browser timezone
      const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      
      if (!isValidTimezone(detectedTimezone)) {
        return { success: false, error: 'Could not detect valid timezone' }
      }
      
      // Update user timezone with auto-detected value
      const result = await updateUserTimezone(detectedTimezone, { autoDetected: true })
      
      if (result.success) {
        return { success: true, timezone: detectedTimezone }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error) {
      console.error('Error auto-detecting timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return autoDetectUserTimezone
}

/**
 * Hook for organization timezone management
 * Provides admin functions for timezone policy (requires organization admin)
 */
export function useOrganizationTimezoneAdmin() {
  const { organization, membership } = useOrganization()
  const { getToken } = useAuth()
  const [orgSettings, setOrgSettings] = useState<OrganizationWithTimezone | null>(null)
  
  // Create Clerk-authenticated Supabase client
  const supabase = useMemo(() => createClerkSupabaseClient(getToken), [getToken])
  
  const isAdmin = membership?.role === 'admin' || membership?.role === 'basic_member'
  
  // Load organization settings
  useEffect(() => {
    if (!organization) return
    
    const loadOrgSettings = async () => {
      const { data } = await supabase
        .from('organizations')
        .select('id, clerk_org_id, name, timezone_settings')
        .eq('clerk_org_id', organization.id)
        .single()
      
      setOrgSettings(data)
    }
    
    loadOrgSettings()
  }, [organization])
  
  const updateOrganizationTimezone = async (
    settings: {
      default_timezone: string
      timezone_policy?: 'user_choice' | 'organization_standard' | 'flexible'
      allow_user_override?: boolean
    }
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!organization || !isAdmin) {
        return { success: false, error: 'Unauthorized or no organization' }
      }
      
      if (!isValidTimezone(settings.default_timezone)) {
        return { success: false, error: 'Invalid timezone' }
      }
      
      const updatedSettings = {
        default_timezone: settings.default_timezone,
        timezone_policy: settings.timezone_policy || 'user_choice',
        allow_user_override: settings.allow_user_override ?? true
      }
      
      const { error } = await supabase
        .from('organizations')
        .update({ timezone_settings: updatedSettings })
        .eq('clerk_org_id', organization.id)
      
      if (error) {
        console.error('Error updating organization timezone:', error)
        return { success: false, error: error.message }
      }
      
      return { success: true }
    } catch (error) {
      console.error('Error updating organization timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return {
    isAdmin,
    currentSettings: {
      defaultTimezone: orgSettings?.timezone_settings?.default_timezone || 'UTC',
      timezonePolicy: orgSettings?.timezone_settings?.timezone_policy || 'user_choice',
      allowUserOverride: orgSettings?.timezone_settings?.allow_user_override ?? true
    },
    updateSettings: isAdmin ? updateOrganizationTimezone : undefined
  }
}

/**
 * Get timezone info for displaying in UI
 */
export function useTimezoneInfo() {
  const config = useTimezoneConfig()
  
  return {
    ...config,
    timezoneDisplayName: getTimezoneDisplay(config.effectiveTimezone),
    canChangeTimezone: config.userCanOverride,
    needsTimezoneSetup: !config.userTimezone && !config.isAutoDetected
  }
}

// Helper function to get timezone display name
function getTimezoneDisplay(timezone: string): string {
  try {
    const cityName = timezone.split('/')[1]?.replace(/_/g, ' ') || timezone
    const now = new Date()
    
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short'
    })
    
    const parts = formatter.formatToParts(now)
    const abbreviation = parts.find(part => part.type === 'timeZoneName')?.value || ''
    
    return `${cityName} (${abbreviation})`
  } catch {
    return timezone
  }
}
