/**
 * Timezone Selector Component
 * 
 * Allows users to select their preferred timezone from a dropdown
 * with search functionality and common timezones highlighted
 */

import * as React from "react"
import { Check, Clock, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { COMMON_TIMEZONES, getTimezonePreferences, type TimezonePreference } from "@/lib/timezone"

interface TimezoneSelectorProps {
  value?: string
  onValueChange: (timezone: string) => void
  placeholder?: string
  disabled?: boolean
  showCurrentTime?: boolean
  className?: string
}

export function TimezoneSelector({
  value,
  onValueChange,
  placeholder = "Select timezone...",
  disabled = false,
  showCurrentTime = true,
  className
}: TimezoneSelectorProps) {
  const [open, setOpen] = React.useState(false)
  const [search, setSearch] = React.useState("")
  
  const timezonePreferences = getTimezonePreferences()
  
  // Filter timezones based on search
  const filteredTimezones = React.useMemo(() => {
    if (!search) return timezonePreferences
    
    const searchLower = search.toLowerCase()
    return timezonePreferences.filter(tz => 
      tz.timezone.toLowerCase().includes(searchLower) ||
      tz.displayName.toLowerCase().includes(searchLower) ||
      tz.abbreviation.toLowerCase().includes(searchLower)
    )
  }, [search, timezonePreferences])
  
  // Get selected timezone info
  const selectedTimezone = timezonePreferences.find(tz => tz.timezone === value)
  
  // Get current time in selected timezone
  const getCurrentTime = (timezone: string) => {
    try {
      const now = new Date()
      return new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).format(now)
    } catch {
      return ''
    }
  }
  
  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              !value && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span className="truncate">
                {selectedTimezone ? selectedTimezone.displayName : placeholder}
              </span>
            </div>
            {selectedTimezone && showCurrentTime && (
              <Badge variant="secondary" className="ml-2 shrink-0">
                {getCurrentTime(selectedTimezone.timezone)}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align="start">
          <Command>
            <CommandInput 
              placeholder="Search timezones..." 
              value={search}
              onValueChange={setSearch}
            />
            <CommandList>
              <CommandEmpty>No timezone found.</CommandEmpty>
              
              {/* Common Timezones */}
              <CommandGroup heading="Common Timezones">
                {filteredTimezones
                  .filter(tz => COMMON_TIMEZONES.includes(tz.timezone as any))
                  .map((timezone) => (
                    <CommandItem
                      key={timezone.timezone}
                      value={timezone.timezone}
                      onSelect={() => {
                        onValueChange(timezone.timezone)
                        setOpen(false)
                      }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <Check
                          className={cn(
                            "h-4 w-4",
                            value === timezone.timezone ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div>
                          <div className="font-medium">{timezone.displayName}</div>
                          <div className="text-xs text-muted-foreground">
                            {timezone.offset}
                          </div>
                        </div>
                      </div>
                      {showCurrentTime && (
                        <Badge variant="outline" className="ml-2">
                          {getCurrentTime(timezone.timezone)}
                        </Badge>
                      )}
                    </CommandItem>
                  ))}
              </CommandGroup>
              
              {/* All Timezones (if searching) */}
              {search && (
                <CommandGroup heading="All Results">
                  {filteredTimezones
                    .filter(tz => !COMMON_TIMEZONES.includes(tz.timezone as any))
                    .map((timezone) => (
                      <CommandItem
                        key={timezone.timezone}
                        value={timezone.timezone}
                        onSelect={() => {
                          onValueChange(timezone.timezone)
                          setOpen(false)
                        }}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <Check
                            className={cn(
                              "h-4 w-4",
                              value === timezone.timezone ? "opacity-100" : "opacity-0"
                            )}
                          />
                          <div>
                            <div className="font-medium">{timezone.displayName}</div>
                            <div className="text-xs text-muted-foreground">
                              {timezone.offset}
                            </div>
                          </div>
                        </div>
                        {showCurrentTime && (
                          <Badge variant="outline" className="ml-2">
                            {getCurrentTime(timezone.timezone)}
                          </Badge>
                        )}
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
