/**
 * Simplified Timezone Management with Proper Clerk + Supabase Integration
 * 
 * This version uses the proper Clerk JWT template for Supabase integration
 * and handles the timezone data correctly with RLS policies.
 */

import { useUser, useOrganization } from '@clerk/nextjs'
import { useAuth } from '@clerk/nextjs'
import { useEffect, useState } from 'react'
import { createClient } from '@supabase/supabase-js'
import { getEffectiveTimezone, type TimezoneConfig, isValidTimezone } from './timezone'

// Create Supabase client with Clerk session token
function createClerkSupabaseClient(session: any) {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: session?.getToken ? `Bearer ${session.getToken({ template: 'supabase' })}` : '',
        },
      },
    }
  )
}

// Database types
interface UserPreferences {
  id: string
  user_id: string
  org_id?: string
  timezone?: string
  timezone_auto_detected: boolean
  timezone_set_at?: string
  preferences: Record<string, any>
  created_at: string
  updated_at: string
}

interface OrganizationWithTimezone {
  id: string
  clerk_org_id: string
  name: string
  timezone_settings: {
    default_timezone: string
    timezone_policy: 'user_choice' | 'organization_standard' | 'flexible'
    allow_user_override: boolean
  }
}

/**
 * Simplified timezone configuration hook using Clerk session
 */
export function useTimezoneConfig(): TimezoneConfig & {
  effectiveTimezone: string
  loading: boolean
  userCanOverride: boolean
  isAutoDetected: boolean
} {
  const { user, isLoaded: userLoaded } = useUser()
  const { organization, isLoaded: orgLoaded } = useOrganization()
  const { isSignedIn, getToken } = useAuth()
  
  const [userPrefs, setUserPrefs] = useState<UserPreferences | null>(null)
  const [orgSettings, setOrgSettings] = useState<OrganizationWithTimezone | null>(null)
  const [loading, setLoading] = useState(true)
  
  // Load user preferences from Supabase
  useEffect(() => {
    if (!userLoaded || !user || !isSignedIn) {
      setLoading(false)
      return
    }
    
    const loadUserPreferences = async () => {
      try {
        // Get Clerk token for Supabase
        const token = await getToken({ template: 'supabase' })
        
        if (!token) {
          console.log('No Clerk token available for Supabase')
          setLoading(false)
          return
        }
        
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          {
            global: {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            },
          }
        )
        
        const { data, error } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single()
        
        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
          console.error('Error loading user preferences:', error)
          return
        }
        
        setUserPrefs(data)
      } catch (error) {
        console.error('Error loading user preferences:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadUserPreferences()
  }, [user, userLoaded, isSignedIn, getToken])
  
  // For now, return a simple implementation without complex org settings
  const userTimezone = userPrefs?.timezone
  const isAutoDetected = userPrefs?.timezone_auto_detected ?? false
  const organizationTimezone = 'UTC' // Default for now
  const userCanOverride = true // Allow user override by default
  
  // Build timezone config
  const config: TimezoneConfig = {
    userTimezone: userCanOverride ? userTimezone : null,
    organizationTimezone,
    fallbackTimezone: 'UTC'
  }
  
  const effectiveTimezone = getEffectiveTimezone(config)
  
  return {
    ...config,
    effectiveTimezone,
    loading,
    userCanOverride,
    isAutoDetected
  }
}

/**
 * Hook to update user timezone in Supabase
 */
export function useTimezoneUpdate() {
  const { user } = useUser()
  const { isSignedIn, getToken } = useAuth()
  
  const updateUserTimezone = async (
    timezone: string,
    options: {
      autoDetected?: boolean
    } = {}
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!user || !isSignedIn) {
        return { success: false, error: 'User not authenticated' }
      }
      
      if (!isValidTimezone(timezone)) {
        return { success: false, error: 'Invalid timezone' }
      }
      
      // Get Clerk token for Supabase
      const token = await getToken({ template: 'supabase' })
      
      if (!token) {
        return { success: false, error: 'No authentication token available' }
      }
      
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          global: {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        }
      )
      
      // Upsert user preferences
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          timezone,
          timezone_auto_detected: options.autoDetected ?? false,
          timezone_set_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
      
      if (error) {
        console.error('Error updating timezone preference:', error)
        return { success: false, error: error.message }
      }
      
      return { success: true }
    } catch (error) {
      console.error('Error updating user timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return updateUserTimezone
}

/**
 * Hook to auto-detect and set user timezone
 */
export function useTimezoneAutoDetect() {
  const { user } = useUser()
  const { isSignedIn, getToken } = useAuth()
  const updateUserTimezone = useTimezoneUpdate()
  
  const autoDetectUserTimezone = async (): Promise<{ 
    success: boolean 
    timezone?: string 
    error?: string 
  }> => {
    try {
      if (!user || !isSignedIn) {
        return { success: false, error: 'User not authenticated' }
      }
      
      // Check if user already has a manually set timezone
      const token = await getToken({ template: 'supabase' })
      
      if (!token) {
        return { success: false, error: 'No authentication token available' }
      }
      
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          global: {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        }
      )
      
      const { data: userPrefs } = await supabase
        .from('user_preferences')
        .select('timezone, timezone_auto_detected')
        .eq('user_id', user.id)
        .single()
      
      if (userPrefs?.timezone && !userPrefs.timezone_auto_detected) {
        return { 
          success: true, 
          timezone: userPrefs.timezone,
          error: 'User already has manually set timezone'
        }
      }
      
      // Detect browser timezone
      const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      
      if (!isValidTimezone(detectedTimezone)) {
        return { success: false, error: 'Could not detect valid timezone' }
      }
      
      // Update user timezone with auto-detected value
      const result = await updateUserTimezone(detectedTimezone, { autoDetected: true })
      
      if (result.success) {
        return { success: true, timezone: detectedTimezone }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error) {
      console.error('Error auto-detecting timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return autoDetectUserTimezone
}

/**
 * Get timezone info for displaying in UI
 */
export function useTimezoneInfo() {
  const config = useTimezoneConfig()
  
  return {
    ...config,
    timezoneDisplayName: getTimezoneDisplay(config.effectiveTimezone),
    canChangeTimezone: config.userCanOverride,
    needsTimezoneSetup: !config.userTimezone && !config.isAutoDetected
  }
}

// Helper function to get timezone display name
function getTimezoneDisplay(timezone: string): string {
  try {
    const cityName = timezone.split('/')[1]?.replace(/_/g, ' ') || timezone
    const now = new Date()
    
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short'
    })
    
    const parts = formatter.formatToParts(now)
    const abbreviation = parts.find(part => part.type === 'timeZoneName')?.value || ''
    
    return `${cityName} (${abbreviation})`
  } catch {
    return timezone
  }
}
