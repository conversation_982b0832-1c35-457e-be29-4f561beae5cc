/**
 * Timezone Time Display Component
 * 
 * Displays timestamps in user's preferred timezone with clear indicators
 * Supports relative time formatting for recent dates
 */

import * as React from "react"
import { Clock, Globe } from "lucide-react"
import { cn } from "@/lib/utils"
import { formatForUser, formatRelativeForUser } from "@/lib/timezone"
import { useTimezoneConfig } from "@/lib/user-timezone-production"
import { Badge } from "@/components/ui/badge"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface TimezoneTimeProps {
  timestamp: string | Date
  timezone?: string
  format?: string
  relative?: boolean
  showTimezone?: boolean
  showTooltip?: boolean
  className?: string
}

export function TimezoneTime({ 
  timestamp, 
  timezone,
  format = 'MMM DD, YYYY h:mm A',
  relative = false,
  showTimezone = true,
  showTooltip = true,
  className
}: TimezoneTimeProps) {
  const { effectiveTimezone, loading } = useTimezoneConfig()
  const displayTimezone = timezone || effectiveTimezone
  
  // Don't render anything while loading timezone config
  if (loading) {
    return (
      <span className={cn("inline-flex items-center gap-1 animate-pulse", className)}>
        <div className="h-4 w-16 bg-muted rounded" />
      </span>
    )
  }
  
  // Format the timestamp
  const formattedTime = React.useMemo(() => {
    try {
      if (relative) {
        return formatRelativeForUser(timestamp, displayTimezone)
      } else {
        return formatForUser(timestamp, displayTimezone, format)
      }
    } catch (error) {
      console.error('Error formatting timestamp:', error)
      return 'Invalid date'
    }
  }, [timestamp, displayTimezone, format, relative])
  
  // Get timezone abbreviation for display
  const timezoneAbbr = React.useMemo(() => {
    try {
      const now = new Date()
      const formatter = new Intl.DateTimeFormat('en', {
        timeZone: displayTimezone,
        timeZoneName: 'short'
      })
      const parts = formatter.formatToParts(now)
      return parts.find(part => part.type === 'timeZoneName')?.value || ''
    } catch {
      return ''
    }
  }, [displayTimezone])
  
  // Get full timezone info for tooltip
  const timezoneInfo = React.useMemo(() => {
    try {
      const date = new Date(timestamp)
      const utcTime = formatForUser(timestamp, 'UTC', 'MMM DD, YYYY h:mm A')
      const localTime = formatForUser(timestamp, displayTimezone, 'MMM DD, YYYY h:mm A z')
      
      return {
        utc: utcTime + ' UTC',
        local: localTime,
        timezone: displayTimezone
      }
    } catch {
      return null
    }
  }, [timestamp, displayTimezone])
  
  const TimeContent = (
    <span className={cn("inline-flex items-center gap-1", className)}>
      <Clock className="h-3 w-3 text-muted-foreground" />
      <time dateTime={new Date(timestamp).toISOString()}>
        {formattedTime}
      </time>
      {showTimezone && timezoneAbbr && (
        <Badge variant="outline" className="text-xs px-1 py-0">
          {timezoneAbbr}
        </Badge>
      )}
    </span>
  )
  
  if (showTooltip && timezoneInfo) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {TimeContent}
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                <Globe className="h-3 w-3" />
                <span className="font-medium">Timezone: {timezoneInfo.timezone}</span>
              </div>
              <div>Local: {timezoneInfo.local}</div>
              <div className="text-muted-foreground">UTC: {timezoneInfo.utc}</div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }
  
  return TimeContent
}

/**
 * Compact timezone time display for use in tables and lists
 */
export function CompactTimezoneTime({
  timestamp,
  timezone,
  relative = true,
  className
}: Pick<TimezoneTimeProps, 'timestamp' | 'timezone' | 'relative' | 'className'>) {
  return (
    <TimezoneTime
      timestamp={timestamp}
      timezone={timezone}
      relative={relative}
      showTimezone={false}
      showTooltip={true}
      className={cn("text-sm text-muted-foreground", className)}
    />
  )
}

/**
 * Multi-timezone display component
 * Shows time in multiple timezones for global coordination
 */
interface MultiTimezoneDisplayProps {
  timestamp: string | Date
  timezones: string[]
  primary?: string
  className?: string
}

export function MultiTimezoneDisplay({
  timestamp,
  timezones,
  primary,
  className
}: MultiTimezoneDisplayProps) {
  const { effectiveTimezone } = useTimezoneConfig()
  const primaryTimezone = primary || effectiveTimezone
  
  return (
    <div className={cn("space-y-2", className)}>
      {/* Primary timezone */}
      <div className="flex items-center gap-2">
        <Badge variant="default" className="shrink-0">
          Primary
        </Badge>
        <TimezoneTime 
          timestamp={timestamp} 
          timezone={primaryTimezone}
          showTooltip={false}
        />
      </div>
      
      {/* Additional timezones */}
      {timezones
        .filter(tz => tz !== primaryTimezone)
        .map((tz) => (
          <div key={tz} className="flex items-center gap-2 pl-2">
            <div className="w-2 h-2 rounded-full bg-muted-foreground/30" />
            <TimezoneTime 
              timestamp={timestamp} 
              timezone={tz}
              showTooltip={false}
              className="text-sm text-muted-foreground"
            />
          </div>
        ))}
    </div>
  )
}

/**
 * Meeting time coordinator component
 * Helps schedule meetings across timezones
 */
interface MeetingTimeCoordinatorProps {
  timestamp: string | Date
  attendeeTimezones: string[]
  className?: string
}

export function MeetingTimeCoordinator({
  timestamp,
  attendeeTimezones,
  className
}: MeetingTimeCoordinatorProps) {
  const uniqueTimezones = [...new Set(attendeeTimezones)]
  
  return (
    <div className={cn("border rounded-lg p-4 space-y-3", className)}>
      <div className="flex items-center gap-2 text-sm font-medium">
        <Globe className="h-4 w-4" />
        Meeting Time Coordination
      </div>
      
      <div className="grid gap-2">
        {uniqueTimezones.map((timezone) => {
          const cityName = timezone.split('/')[1]?.replace(/_/g, ' ') || timezone
          
          return (
            <div key={timezone} className="flex items-center justify-between">
              <span className="text-sm font-medium">{cityName}</span>
              <TimezoneTime 
                timestamp={timestamp}
                timezone={timezone}
                format="h:mm A"
                showTooltip={false}
                className="text-sm"
              />
            </div>
          )
        })}
      </div>
    </div>
  )
}
