"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LoadingState } from "@/components/ui/LoadingStates"

export default function DashboardProfilePage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to new profile location
    router.replace("/profile")
  }, [router])

  return (
    <div className="p-6">
      <LoadingState message="Redirecting to profile..." />
    </div>
  )
}
