"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LoadingState } from "@/components/ui/LoadingStates"

export default function NotificationsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to new profile location with notifications tab active
    router.replace("/profile?tab=notifications")
  }, [router])

  return (
    <div className="p-6">
      <LoadingState message="Loading notification settings..." />
    </div>
  )
}
