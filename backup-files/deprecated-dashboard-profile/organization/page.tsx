"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LoadingState } from "@/components/ui/LoadingStates"

export default function DashboardOrganizationPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to new organization profile location
    router.replace("/profile/organization")
  }, [router])

  return (
    <div className="p-6">
      <LoadingState message="Redirecting to organization settings..." />
    </div>
  )
}
