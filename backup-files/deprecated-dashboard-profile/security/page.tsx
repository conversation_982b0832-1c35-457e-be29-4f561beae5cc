"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LoadingState } from "@/components/ui/LoadingStates"

export default function SecurityPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to new profile location with security tab active
    router.replace("/profile?tab=security")
  }, [router])

  return (
    <div className="p-6">
      <LoadingState message="Loading security settings..." />
    </div>
  )
}
