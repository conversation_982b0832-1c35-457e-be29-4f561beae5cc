"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LoadingState } from "@/components/ui/LoadingStates"

export default function AccountPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to new profile location with account tab active
    router.replace("/profile?tab=account")
  }, [router])

  return (
    <div className="p-6">
      <LoadingState message="Loading account settings..." />
    </div>
  )
}
