/**
 * Database Timezone Helpers
 * 
 * Utility functions for working with timezone-aware data in Supabase
 * Handles conversion between UTC storage and user display timezones
 */

import { createClient } from '@supabase/supabase-js'
import { formatForUser, createTimestampForStorage } from './timezone'

// Re-export createClient from lib if you have it configured
// import { supabase } from './supabase'

/**
 * Create a job with timezone-aware timestamps
 */
export async function createJobWithTimezone(
  supabase: ReturnType<typeof createClient>,
  jobData: {
    title: string
    description?: string
    deadline?: Date
    org_id: string
    client_id?: string
    status?: string
    [key: string]: any
  }
) {
  try {
    // Ensure deadline is properly converted to UTC for storage
    const processedData = {
      ...jobData,
      deadline: jobData.deadline ? jobData.deadline.toISOString() : null,
      created_at: new Date().toISOString(), // Supabase handles this automatically, but being explicit
      updated_at: new Date().toISOString()
    }
    
    const { data, error } = await supabase
      .from('recruitment_jobs')
      .insert(processedData)
      .select()
    
    if (error) {
      console.error('Error creating job:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  } catch (err) {
    console.error('Unexpected error creating job:', err)
    return { 
      data: null, 
      error: { message: 'Failed to create job', details: err } 
    }
  }
}

/**
 * Get jobs with timezone-aware display formatting
 */
export async function getJobsWithTimezone(
  supabase: ReturnType<typeof createClient>,
  organizationId: string,
  userTimezone: string,
  options: {
    status?: string
    limit?: number
    offset?: number
  } = {}
) {
  try {
    let query = supabase
      .from('recruitment_jobs')
      .select('*')
      .eq('org_id', organizationId)
    
    // Add filters
    if (options.status) {
      query = query.eq('status', options.status)
    }
    
    // Add pagination
    if (options.limit) {
      query = query.limit(options.limit)
    }
    
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }
    
    // Order by most recent
    query = query.order('created_at', { ascending: false })
    
    const { data: jobs, error } = await query
    
    if (error) {
      console.error('Error fetching jobs:', error)
      return { data: null, error }
    }
    
    // Convert timestamps to user's timezone on the client side
    const jobsWithLocalTime = jobs?.map(job => ({
      ...job,
      // Keep original UTC timestamps for system use
      deadline_utc: job.deadline,
      created_at_utc: job.created_at,
      updated_at_utc: job.updated_at,
      
      // Add user-friendly formatted timestamps
      deadline_local: job.deadline 
        ? formatForUser(job.deadline, userTimezone, 'MMM DD, YYYY h:mm A')
        : null,
      created_at_local: formatForUser(job.created_at, userTimezone, 'MMM DD, YYYY h:mm A'),
      updated_at_local: formatForUser(job.updated_at, userTimezone, 'MMM DD, YYYY h:mm A'),
      
      // Add relative time formatting for recent items
      created_at_relative: formatRelativeForUser(job.created_at, userTimezone),
      updated_at_relative: formatRelativeForUser(job.updated_at, userTimezone)
    }))
    
    return { data: jobsWithLocalTime, error: null }
  } catch (err) {
    console.error('Unexpected error fetching jobs:', err)
    return { 
      data: null, 
      error: { message: 'Failed to fetch jobs', details: err } 
    }
  }
}

/**
 * Update organization timezone settings
 */
export async function updateOrganizationTimezone(
  supabase: ReturnType<typeof createClient>,
  organizationId: string,
  defaultTimezone: string
) {
  try {
    const { data, error } = await supabase
      .from('organizations')
      .update({ 
        default_timezone: defaultTimezone,
        updated_at: new Date().toISOString()
      })
      .eq('id', organizationId)
      .select()
    
    if (error) {
      console.error('Error updating organization timezone:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  } catch (err) {
    console.error('Unexpected error updating organization timezone:', err)
    return { 
      data: null, 
      error: { message: 'Failed to update timezone', details: err } 
    }
  }
}

/**
 * Update user timezone preference
 */
export async function updateUserTimezonePreference(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  organizationId: string,
  timezonePreference: string | null
) {
  try {
    const { data, error } = await supabase
      .from('user_process_roles')
      .update({ 
        timezone_preference: timezonePreference,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('org_id', organizationId)
      .select()
    
    if (error) {
      console.error('Error updating user timezone preference:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  } catch (err) {
    console.error('Unexpected error updating user timezone preference:', err)
    return { 
      data: null, 
      error: { message: 'Failed to update user timezone preference', details: err } 
    }
  }
}

/**
 * Get organization timezone settings
 */
export async function getOrganizationTimezone(
  supabase: ReturnType<typeof createClient>,
  organizationId: string
): Promise<{ timezone: string | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('organizations')
      .select('default_timezone')
      .eq('id', organizationId)
      .single()
    
    if (error) {
      console.error('Error fetching organization timezone:', error)
      return { timezone: null, error }
    }
    
    return { timezone: data?.default_timezone || 'UTC', error: null }
  } catch (err) {
    console.error('Unexpected error fetching organization timezone:', err)
    return { timezone: null, error: err }
  }
}

/**
 * Get user timezone preference
 */
export async function getUserTimezonePreference(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  organizationId: string
): Promise<{ timezone: string | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('user_process_roles')
      .select('timezone_preference')
      .eq('user_id', userId)
      .eq('org_id', organizationId)
      .single()
    
    if (error) {
      console.error('Error fetching user timezone preference:', error)
      return { timezone: null, error }
    }
    
    return { timezone: data?.timezone_preference || null, error: null }
  } catch (err) {
    console.error('Unexpected error fetching user timezone preference:', err)
    return { timezone: null, error: err }
  }
}

/**
 * Create interview/meeting with timezone awareness
 */
export async function createInterviewWithTimezone(
  supabase: ReturnType<typeof createClient>,
  interviewData: {
    job_id: string
    candidate_id: string
    interviewer_id: string
    scheduled_at: Date
    duration_minutes?: number
    timezone: string
    notes?: string
    [key: string]: any
  }
) {
  try {
    // Ensure the scheduled time is properly stored in UTC
    const processedData = {
      ...interviewData,
      scheduled_at: interviewData.scheduled_at.toISOString(),
      scheduled_timezone: interviewData.timezone, // Store the original timezone for reference
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    const { data, error } = await supabase
      .from('interviews')
      .insert(processedData)
      .select()
    
    if (error) {
      console.error('Error creating interview:', error)
      return { data: null, error }
    }
    
    return { data, error: null }
  } catch (err) {
    console.error('Unexpected error creating interview:', err)
    return { 
      data: null, 
      error: { message: 'Failed to create interview', details: err } 
    }
  }
}

/**
 * Helper function to format timestamps from database queries
 * Use this to consistently format any timestamp field
 */
export function formatDatabaseTimestamp(
  timestamp: string | null,
  userTimezone: string,
  format?: string
): string {
  if (!timestamp) {
    return 'Not set'
  }
  
  try {
    return formatForUser(timestamp, userTimezone, format)
  } catch (error) {
    console.error('Error formatting database timestamp:', error)
    return 'Invalid date'
  }
}

/**
 * Batch format multiple timestamps for efficient processing
 */
export function batchFormatTimestamps<T extends Record<string, any>>(
  items: T[],
  timestampFields: (keyof T)[],
  userTimezone: string,
  format?: string
): T[] {
  return items.map(item => {
    const formatted = { ...item }
    
    timestampFields.forEach(field => {
      const timestamp = item[field]
      if (timestamp) {
        // Add formatted version with _local suffix
        const localField = `${String(field)}_local` as keyof T
        formatted[localField] = formatDatabaseTimestamp(
          timestamp as string, 
          userTimezone, 
          format
        ) as T[keyof T]
      }
    })
    
    return formatted
  })
}

// Import formatRelativeForUser if not already imported
function formatRelativeForUser(timestamp: string, timezone: string): string {
  // This should be imported from timezone.ts
  // Including here for completeness if not imported above
  try {
    const now = new Date()
    const date = new Date(timestamp)
    const diffMs = now.getTime() - date.getTime()
    const diffHours = diffMs / (1000 * 60 * 60)
    
    if (diffHours > 0 && diffHours < 24) {
      const rtf = new Intl.RelativeTimeFormat('en', { numeric: 'auto' })
      
      if (diffHours < 1) {
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        return rtf.format(-diffMinutes, 'minute')
      } else {
        return rtf.format(-Math.floor(diffHours), 'hour')
      }
    }
    
    return formatForUser(timestamp, timezone, 'MMM DD, YYYY h:mm A')
  } catch (error) {
    console.error('Error formatting relative date:', error)
    return formatForUser(timestamp, timezone, 'MMM DD, YYYY h:mm A')
  }
}
