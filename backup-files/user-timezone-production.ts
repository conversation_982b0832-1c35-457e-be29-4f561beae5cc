/**
 * Production Clerk + Supabase Timezone Management
 * 
 * This implementation provides production-ready integration between Clerk authentication
 * and Supabase database with proper error handling, type safety, and performance optimization.
 */

import { useUser, useOrganization } from '@clerk/nextjs'
import { useAuth } from '@clerk/nextjs'
import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { getEffectiveTimezone, type TimezoneConfig, isValidTimezone } from './timezone'

// Database types
interface UserPreferences {
  id: string
  user_id: string
  org_id?: string
  timezone?: string
  timezone_auto_detected: boolean
  timezone_set_at?: string
  preferences: Record<string, any>
  created_at: string
  updated_at: string
}

interface OrganizationWithTimezone {
  id: string
  clerk_org_id: string
  name: string
  timezone_settings?: {
    default_timezone: string
    timezone_policy: 'user_choice' | 'organization_standard' | 'flexible'
    allow_user_override: boolean
  }
}

// Error types for better error handling
interface TimezoneError {
  code: 'AUTH_ERROR' | 'VALIDATION_ERROR' | 'DATABASE_ERROR' | 'JWT_ERROR'
  message: string
  details?: string
}

/**
 * <PERSON>reate authenticated Supabase client using Clerk JWT
 */
function createAuthenticatedSupabaseClient(
  getToken: (options?: { template?: string }) => Promise<string | null>
): SupabaseClient {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        fetch: async (url, options = {}) => {
          try {
            // Get Clerk token with Supabase template
            const clerkToken = await getToken({ template: 'supabase' })
            
            if (!clerkToken) {
              throw new Error('No Clerk token available. Ensure JWT template is configured.')
            }
            
            // Add Authorization header
            const headers = new Headers(options?.headers)
            headers.set('Authorization', `Bearer ${clerkToken}`)
            
            return fetch(url, {
              ...options,
              headers,
            })
          } catch (error) {
            console.error('Supabase fetch interceptor error:', error)
            throw error
          }
        },
      },
    }
  )
}

/**
 * Production timezone configuration hook
 */
export function useTimezoneConfig(): TimezoneConfig & {
  effectiveTimezone: string
  loading: boolean
  userCanOverride: boolean
  isAutoDetected: boolean
  error: TimezoneError | null
} {
  const { user, isLoaded: userLoaded } = useUser()
  const { organization, isLoaded: orgLoaded } = useOrganization()
  const { isSignedIn, getToken } = useAuth()
  
  const [userPrefs, setUserPrefs] = useState<UserPreferences | null>(null)
  const [orgSettings, setOrgSettings] = useState<OrganizationWithTimezone | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<TimezoneError | null>(null)
  
  // Create authenticated Supabase client
  const supabase = useMemo(() => {
    if (!isSignedIn) return null
    return createAuthenticatedSupabaseClient(getToken)
  }, [isSignedIn, getToken])
  
  // Load user preferences
  const loadUserPreferences = useCallback(async () => {
    if (!user || !isSignedIn || !supabase) {
      setLoading(false)
      return
    }
    
    try {
      setError(null)
      
      const { data, error: dbError } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single()
      
      if (dbError && dbError.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw {
          code: 'DATABASE_ERROR',
          message: 'Failed to load user preferences',
          details: dbError.message
        } as TimezoneError
      }
      
      setUserPrefs(data || null)
    } catch (err) {
      const error = err as TimezoneError
      console.error('Error loading user preferences:', error)
      setError(error)
    } finally {
      setLoading(false)
    }
  }, [user, isSignedIn, supabase])
  
  // Load organization settings
  const loadOrgSettings = useCallback(async () => {
    if (!organization || !supabase) return
    
    try {
      const { data, error: dbError } = await supabase
        .from('organizations')
        .select('id, clerk_org_id, name, timezone_settings')
        .eq('clerk_org_id', organization.id)
        .single()
      
      if (dbError && dbError.code !== 'PGRST116') {
        console.warn('Organization settings not found:', dbError.message)
        return
      }
      
      setOrgSettings(data || null)
    } catch (err) {
      console.warn('Error loading organization settings:', err)
      // Don't set error state for org settings as they're optional
    }
  }, [organization, supabase])
  
  // Load data when dependencies are ready
  useEffect(() => {
    if (userLoaded && orgLoaded) {
      loadUserPreferences()
      loadOrgSettings()
    }
  }, [userLoaded, orgLoaded, loadUserPreferences, loadOrgSettings])
  
  // Calculate timezone configuration
  const userTimezone = userPrefs?.timezone
  const isAutoDetected = userPrefs?.timezone_auto_detected ?? false
  const organizationTimezone = orgSettings?.timezone_settings?.default_timezone || 'UTC'
  const userCanOverride = orgSettings?.timezone_settings?.allow_user_override ?? true
  
  const config: TimezoneConfig = {
    userTimezone: userCanOverride ? userTimezone : null,
    organizationTimezone,
    fallbackTimezone: 'UTC'
  }
  
  const effectiveTimezone = getEffectiveTimezone(config)
  
  return {
    ...config,
    effectiveTimezone,
    loading,
    userCanOverride,
    isAutoDetected,
    error
  }
}

/**
 * Production timezone update hook
 */
export function useTimezoneUpdate() {
  const { user, isLoaded } = useUser()
  const { isSignedIn, getToken } = useAuth()
  
  const supabase = useMemo(() => {
    if (!isSignedIn) return null
    return createAuthenticatedSupabaseClient(getToken)
  }, [isSignedIn, getToken])
  
  const updateUserTimezone = useCallback(async (
    timezone: string,
    options: {
      autoDetected?: boolean
    } = {}
  ): Promise<{ success: boolean; error?: TimezoneError }> => {
    try {
      // Validation
      if (!user || !isSignedIn || !isLoaded) {
        return { 
          success: false, 
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        }
      }
      
      if (!isValidTimezone(timezone)) {
        return { 
          success: false, 
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid timezone provided',
            details: `Timezone "${timezone}" is not valid`
          }
        }
      }
      
      if (!supabase) {
        return { 
          success: false, 
          error: {
            code: 'JWT_ERROR',
            message: 'Unable to create authenticated database connection'
          }
        }
      }
      
      // Upsert user preferences
      const { error: dbError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          timezone,
          timezone_auto_detected: options.autoDetected ?? false,
          timezone_set_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
      
      if (dbError) {
        return { 
          success: false, 
          error: {
            code: 'DATABASE_ERROR',
            message: 'Failed to update timezone preference',
            details: dbError.message
          }
        }
      }
      
      return { success: true }
    } catch (err) {
      console.error('Error updating user timezone:', err)
      return { 
        success: false, 
        error: {
          code: 'DATABASE_ERROR',
          message: 'Unexpected error occurred',
          details: err instanceof Error ? err.message : 'Unknown error'
        }
      }
    }
  }, [user, isSignedIn, isLoaded, supabase])
  
  return updateUserTimezone
}

/**
 * Production auto-detect timezone hook
 */
export function useTimezoneAutoDetect() {
  const { user, isLoaded } = useUser()
  const { isSignedIn, getToken } = useAuth()
  const updateUserTimezone = useTimezoneUpdate()
  
  const supabase = useMemo(() => {
    if (!isSignedIn) return null
    return createAuthenticatedSupabaseClient(getToken)
  }, [isSignedIn, getToken])
  
  const autoDetectUserTimezone = useCallback(async (): Promise<{ 
    success: boolean 
    timezone?: string 
    error?: TimezoneError 
  }> => {
    try {
      if (!user || !isSignedIn || !isLoaded) {
        return { 
          success: false, 
          error: {
            code: 'AUTH_ERROR',
            message: 'User not authenticated'
          }
        }
      }
      
      if (!supabase) {
        return { 
          success: false, 
          error: {
            code: 'JWT_ERROR',
            message: 'Unable to create authenticated database connection'
          }
        }
      }
      
      // Check if user already has a manually set timezone
      const { data: userPrefs } = await supabase
        .from('user_preferences')
        .select('timezone, timezone_auto_detected')
        .eq('user_id', user.id)
        .single()
      
      if (userPrefs?.timezone && !userPrefs.timezone_auto_detected) {
        return { 
          success: true, 
          timezone: userPrefs.timezone 
        }
      }
      
      // Detect browser timezone
      const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      
      if (!isValidTimezone(detectedTimezone)) {
        return { 
          success: false, 
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Could not detect valid timezone from browser'
          }
        }
      }
      
      // Update user timezone with auto-detected value
      const result = await updateUserTimezone(detectedTimezone, { autoDetected: true })
      
      if (result.success) {
        return { success: true, timezone: detectedTimezone }
      } else {
        return { success: false, error: result.error }
      }
    } catch (err) {
      console.error('Error auto-detecting timezone:', err)
      return { 
        success: false, 
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to auto-detect timezone',
          details: err instanceof Error ? err.message : 'Unknown error'
        }
      }
    }
  }, [user, isSignedIn, isLoaded, supabase, updateUserTimezone])
  
  return autoDetectUserTimezone
}

/**
 * Get timezone display information
 */
export function useTimezoneInfo() {
  const config = useTimezoneConfig()
  
  const timezoneDisplayName = useMemo(() => {
    return getTimezoneDisplay(config.effectiveTimezone)
  }, [config.effectiveTimezone])
  
  return {
    ...config,
    timezoneDisplayName,
    canChangeTimezone: config.userCanOverride,
    needsTimezoneSetup: !config.userTimezone && !config.isAutoDetected
  }
}

// Helper function to get timezone display name
function getTimezoneDisplay(timezone: string): string {
  try {
    const cityName = timezone.split('/')[1]?.replace(/_/g, ' ') || timezone
    const now = new Date()
    
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short'
    })
    
    const parts = formatter.formatToParts(now)
    const abbreviation = parts.find(part => part.type === 'timeZoneName')?.value || ''
    
    return `${cityName} (${abbreviation})`
  } catch {
    return timezone
  }
}
