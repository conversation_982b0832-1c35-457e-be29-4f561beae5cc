/**
 * Settings Page with Timezone Management
 * 
 * Demonstrates the timezone management system in action
 * Allows users to configure their timezone preferences
 */

"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { TimezoneSelector } from "@/components/ui/timezone-selector"
import { TimezoneTime, MultiTimezoneDisplay } from "@/components/ui/timezone-time"
import { useTimezoneConfig, useTimezoneUpdate, useTimezoneAutoDetect } from "@/lib/user-timezone-production"
import { Clock, Globe, MapPin, Users, Settings as SettingsIcon, Settings } from "lucide-react"
import { toast } from "sonner"

export default function SettingsPage() {
  const {
    effectiveTimezone,
    userTimezone,
    organizationTimezone,
    loading,
    userCanOverride,
    isAutoDetected,
    error
  } = useTimezoneConfig()
  
  const updateUserTimezone = useTimezoneUpdate()
  const autoDetectUserTimezone = useTimezoneAutoDetect()
  
  const [selectedTimezone, setSelectedTimezone] = React.useState<string>("")
  const [isUpdating, setIsUpdating] = React.useState(false)
  const [isAutoDetecting, setIsAutoDetecting] = React.useState(false)
  
  // Set initial timezone when data loads
  React.useEffect(() => {
    if (!loading && userTimezone) {
      setSelectedTimezone(userTimezone)
    }
  }, [loading, userTimezone])
  
  const handleTimezoneUpdate = async () => {
    if (!selectedTimezone || selectedTimezone === userTimezone) {
      return
    }
    
    setIsUpdating(true)
    
    try {
      const result = await updateUserTimezone(selectedTimezone)
      
      if (result.success) {
        toast.success("Timezone updated successfully!")
      } else {
        const errorMessage = result.error?.message || "Failed to update timezone"
        toast.error(errorMessage)
        
        // Show specific error details for debugging
        if (result.error?.code === 'JWT_ERROR') {
          toast.error("Please configure Clerk JWT template for Supabase integration")
        }
      }
    } catch (error) {
      console.error("Error updating timezone:", error)
      toast.error("Failed to update timezone")
    } finally {
      setIsUpdating(false)
    }
  }
  
  const handleAutoDetect = async () => {
    setIsAutoDetecting(true)
    
    try {
      const result = await autoDetectUserTimezone()
      
      if (result.success && result.timezone) {
        setSelectedTimezone(result.timezone)
        toast.success(`Auto-detected timezone: ${result.timezone}`)
      } else {
        const errorMessage = result.error?.message || "Failed to auto-detect timezone"
        toast.error(errorMessage)
      }
    } catch (error) {
      console.error("Error auto-detecting timezone:", error)
      toast.error("Failed to auto-detect timezone")
    } finally {
      setIsAutoDetecting(false)
    }
  }
  
  // Sample timestamps for demonstration
  const sampleTimestamps = [
    new Date().toISOString(), // Now
    new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
  ]
  
  const commonTimezones = [
    'America/New_York',
    'Europe/London', 
    'Asia/Tokyo',
    'Australia/Sydney'
  ]
  
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <SettingsIcon className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Settings</h1>
        </div>
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <div className="h-6 w-32 bg-muted animate-pulse rounded" />
              <div className="h-4 w-48 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-10 w-full bg-muted animate-pulse rounded" />
                <div className="h-8 w-24 bg-muted animate-pulse rounded" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }
  
  // Show setup instructions if JWT error is detected
  if (error?.code === 'JWT_ERROR') {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <SettingsIcon className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Settings</h1>
        </div>
        
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-800">
              <Settings className="h-5 w-5" />
              Setup Required: Clerk + Supabase Integration
            </CardTitle>
            <CardDescription className="text-amber-700">
              To use timezone preferences, you need to configure the Clerk JWT template for Supabase.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 text-amber-800">
            <div className="space-y-3">
              <h4 className="font-semibold">Required Setup Steps:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Go to your <strong>Supabase Dashboard</strong> → Project Settings → API</li>
                <li>Copy the <strong>JWT Secret</strong> value</li>
                <li>Go to your <strong>Clerk Dashboard</strong> → JWT Templates</li>
                <li>Click <strong>"New template"</strong> → Select <strong>"Supabase"</strong></li>
                <li>Set <strong>Name</strong> to: <code className="bg-amber-100 px-1 rounded">supabase</code></li>
                <li>Set <strong>Signing Algorithm</strong> to: <code className="bg-amber-100 px-1 rounded">HS256</code></li>
                <li>Paste your Supabase JWT Secret in <strong>Signing Key</strong></li>
                <li>Click <strong>Save</strong></li>
              </ol>
            </div>
            <div className="pt-2">
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="border-amber-300 text-amber-800 hover:bg-amber-100"
              >
                Refresh Page After Setup
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <SettingsIcon className="h-6 w-6" />
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        {/* Timezone Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timezone Settings
            </CardTitle>
            <CardDescription>
              Configure your preferred timezone for displaying dates and times
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Current Timezone Info */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Timezone</span>
                <Badge variant={isAutoDetected ? "secondary" : "default"}>
                  {effectiveTimezone}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Time</span>
                <TimezoneTime 
                  timestamp={new Date().toISOString()}
                  timezone={effectiveTimezone}
                  format="h:mm A z"
                  showTooltip={false}
                />
              </div>
              
              {isAutoDetected && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  Auto-detected from your browser
                </div>
              )}
            </div>
            
            <Separator />
            
            {/* Timezone Selector */}
            {userCanOverride ? (
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">
                    Select Your Timezone
                  </label>
                  <p className="text-xs text-muted-foreground mb-2">
                    Choose your preferred timezone for displaying dates and times
                  </p>
                  <TimezoneSelector
                    value={selectedTimezone}
                    onValueChange={setSelectedTimezone}
                    placeholder="Choose timezone..."
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={handleTimezoneUpdate}
                    disabled={isUpdating || !selectedTimezone || selectedTimezone === userTimezone}
                    size="sm"
                  >
                    {isUpdating ? "Updating..." : "Update Timezone"}
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    onClick={handleAutoDetect}
                    disabled={isAutoDetecting}
                    size="sm"
                  >
                    {isAutoDetecting ? "Detecting..." : "Auto-detect"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                <div className="flex items-center gap-2 text-amber-800">
                  <Users className="h-4 w-4" />
                  <span className="text-sm font-medium">Organization Policy</span>
                </div>
                <p className="text-sm text-amber-700 mt-1">
                  Your organization uses a standard timezone: <strong>{organizationTimezone}</strong>
                </p>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Timezone Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Timezone Preview
            </CardTitle>
            <CardDescription>
              See how timestamps will be displayed in your timezone
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Sample Timestamps */}
            <div className="space-y-3">
              <div>
                <div className="text-sm font-medium mb-2">Sample Timestamps</div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Current time:</span>
                    <TimezoneTime 
                      timestamp={sampleTimestamps[0]}
                      timezone={selectedTimezone || effectiveTimezone}
                      relative={false}
                    />
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">2 hours ago:</span>
                    <TimezoneTime 
                      timestamp={sampleTimestamps[1]}
                      timezone={selectedTimezone || effectiveTimezone}
                      relative={true}
                    />
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Tomorrow:</span>
                    <TimezoneTime 
                      timestamp={sampleTimestamps[2]}
                      timezone={selectedTimezone || effectiveTimezone}
                      relative={false}
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <Separator />
            
            {/* Multi-timezone Display */}
            <div>
              <div className="text-sm font-medium mb-2">Global Time Coordination</div>
              <MultiTimezoneDisplay
                timestamp={new Date().toISOString()}
                timezones={commonTimezones}
                primary={selectedTimezone || effectiveTimezone}
              />
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Organization Settings (for admins) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Organization Timezone Settings
          </CardTitle>
          <CardDescription>
            Default timezone settings for your organization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">Organization Default</div>
              <div className="text-sm text-muted-foreground">
                Default timezone for new users and system operations
              </div>
            </div>
            <Badge variant="outline">{organizationTimezone}</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">User Override Policy</div>
              <div className="text-sm text-muted-foreground">
                Whether users can set their own timezone preferences
              </div>
            </div>
            <Badge variant={userCanOverride ? "default" : "secondary"}>
              {userCanOverride ? "Allowed" : "Restricted"}
            </Badge>
          </div>
          
          <div className="pt-2">
            <Button variant="outline" size="sm" disabled>
              Manage Organization Settings
              <span className="ml-2 text-xs">(Admin Only)</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
