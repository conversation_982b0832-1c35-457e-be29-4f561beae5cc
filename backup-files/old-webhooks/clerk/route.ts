import { headers } from 'next/headers'
import { Webhook } from 'svix'
import { createClient } from '@supabase/supabase-js'

export async function POST(req: Request) {
  console.log('🚀 TalentHUB Clerk Webhook received')

  try {
    // Get environment variables
    const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET!
    const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY!

    if (!WEBHOOK_SECRET || !SUPABASE_URL || !SUPABASE_SERVICE_ROLE_