/**
 * TalentHUB Clerk Webhook Handler - Simple Implementation
 * Following official Clerk + Svix best practices
 */

import { Webhook } from 'svix';
import { NextResponse } from 'next/server';
import { WebhookEvent } from '@clerk/nextjs/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function POST(req: Request) {
  console.log('🔄 Webhook received:', new Date().toISOString());

  // Get webhook secret
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;
  console.log('🔍 DEBUG: WEBHOOK_SECRET exists:', !!WEBHOOK_SECRET);
  console.log('🔍 DEBUG: WEBHOOK_SECRET length:', WEBHOOK_SECRET?.length);
  console.log('🔍 DEBUG: WEBHOOK_SECRET starts with whsec_:', WEBHOOK_SECRET?.startsWith('whsec_'));
  
  if (!WEBHOOK_SECRET) {
    console.error('❌ Missing CLERK_WEBHOOK_SECRET');
    return new Response('Webhook configuration error', { status: 500 });
  }

  // Get headers directly from request (official Clerk pattern)
  const svix_id = req.headers.get('svix-id');
  const svix_timestamp = req.headers.get('svix-timestamp');
  const svix_signature = req.headers.get('svix-signature');

  console.log('🔍 DEBUG: Headers received:');
  console.log('  svix-id:', svix_id);
  console.log('  svix-timestamp:', svix_timestamp);
  console.log('  svix-signature:', svix_signature?.substring(0, 20) + '...');

  // Validate headers
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error('❌ Missing svix headers');
    return new Response('Missing svix headers', { status: 400 });
  }

  // Get body as raw text (official Clerk pattern)
  const payload = await req.text();
  
  console.log('🔍 DEBUG: Payload length:', payload.length);
  console.log('🔍 DEBUG: Payload preview:', payload.substring(0, 100) + '...');

  // Verify webhook
  const wh = new Webhook(WEBHOOK_SECRET);
  let evt: WebhookEvent;

  console.log('🔍 DEBUG: About to verify with secret length:', WEBHOOK_SECRET.length);

  try {
    evt = wh.verify(payload, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('❌ Webhook verification failed:', err);
    console.error('🔍 DEBUG: Verification details:');
    console.error('  Secret starts with:', WEBHOOK_SECRET.substring(0, 10) + '...');
    console.error('  Payload starts with:', payload.substring(0, 50) + '...');
    console.error('  Headers:', { svix_id, svix_timestamp, svix_signature: svix_signature?.substring(0, 20) + '...' });
    return new Response('Webhook verification failed', { status: 400 });
  }

  console.log(`✅ Webhook verified: ${evt.type}`);

  // Process events
  try {
    switch (evt.type) {
      case 'user.created':
        await handleUserCreated(evt.data);
        break;
      case 'user.updated':
        await handleUserUpdated(evt.data);
        break;
      case 'user.deleted':
        await handleUserDeleted(evt.data);
        break;
      case 'organizationMembership.created':
        await handleMembershipCreated(evt.data);
        break;
      case 'organizationMembership.deleted':
        await handleMembershipDeleted(evt.data);
        break;
      case 'session.ended':
        await handleSessionEnded(evt.data);
        break;
      default:
        console.log(`ℹ️ Unhandled event type: ${evt.type}`);
    }

    return NextResponse.json({ 
      success: true, 
      eventType: evt.type,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error processing webhook:', error);
    return new Response('Error processing webhook', { status: 500 });
  }
}

// ===== EVENT HANDLERS =====

async function handleUserCreated(userData: any) {
  console.log('👤 Processing user.created:', userData.id);
  
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not available');
  }

  // Get primary email
  const primaryEmail = userData.email_addresses?.find(
    (email: any) => email.id === userData.primary_email_address_id
  )?.email_address;

  // Create username
  const username = `${userData.first_name || 'user'}_${userData.id.slice(-8)}`;
  const displayName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim();

  // Insert user preferences
  const { error } = await supabaseAdmin
    .from('user_preferences')
    .upsert({
      user_id: userData.id,
      first_name: userData.first_name || '',
      last_name: userData.last_name || '',
      username: username,
      display_name: displayName,
      timezone: 'America/New_York',
      theme: 'system',
      account_tier: 'free',
      subscription_status: 'active',
      features_enabled: ['basic'],
      profile_completeness: 25,
      last_active: new Date().toISOString(),
      created_at: new Date(userData.created_at).toISOString(),
      updated_at: new Date().toISOString(),
      org_id: null,
      is_org_member: false,
    }, {
      onConflict: 'user_id'
    });

  if (error) {
    console.error('❌ Error creating user preferences:', error);
    throw error;
  }

  // Log activity
  await logActivity(userData.id, 'user_created', {
    email: primaryEmail,
    signup_method: 'clerk_auth',
    clerk_created_at: userData.created_at
  });

  console.log('✅ User created successfully:', userData.id);
}

async function handleUserUpdated(userData: any) {
  console.log('👤 Processing user.updated:', userData.id);
  
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not available');
  }

  const displayName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim();

  const { error } = await supabaseAdmin
    .from('user_preferences')
    .update({
      first_name: userData.first_name || '',
      last_name: userData.last_name || '',
      display_name: displayName,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userData.id);

  if (error) {
    console.error('❌ Error updating user preferences:', error);
    throw error;
  }

  await logActivity(userData.id, 'profile_updated', {
    updated_fields: ['first_name', 'last_name'],
    clerk_updated_at: userData.updated_at
  });

  console.log('✅ User updated successfully:', userData.id);
}

async function handleUserDeleted(userData: any) {
  console.log('👤 Processing user.deleted:', userData.id);
  
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not available');
  }

  const { error } = await supabaseAdmin
    .from('user_preferences')
    .update({
      is_deleted: true,
      deleted_at: new Date(userData.deleted_at).toISOString(),
      // Anonymize for GDPR compliance
      first_name: '[DELETED]',
      last_name: '[USER]',
      display_name: '[DELETED USER]',
      username: `deleted_${userData.id.slice(-8)}`,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userData.id);

  if (error) {
    console.error('❌ Error deleting user:', error);
    throw error;
  }

  await logActivity(userData.id, 'user_deleted', {
    deleted_at: userData.deleted_at,
    compliance_action: 'soft_delete_and_anonymize'
  });

  console.log('✅ User deleted successfully:', userData.id);
}

async function handleMembershipCreated(membershipData: any) {
  console.log('🏢 Processing organizationMembership.created');
  
  // This will be implemented when we add organization features
  await logActivity(
    membershipData.public_user_data?.user_id, 
    'org_membership_created', 
    membershipData
  );
}

async function handleMembershipDeleted(membershipData: any) {
  console.log('🏢 Processing organizationMembership.deleted');
  
  // This will be implemented when we add organization features
  await logActivity(
    membershipData.public_user_data?.user_id, 
    'org_membership_deleted', 
    membershipData
  );
}

async function handleSessionEnded(sessionData: any) {
  console.log('🔐 Processing session.ended:', sessionData.user_id);
  
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not available');
  }

  // Update last active timestamp
  const { error } = await supabaseAdmin
    .from('user_preferences')
    .update({
      last_active: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', sessionData.user_id);

  if (error) {
    console.error('❌ Error updating last active:', error);
  }

  await logActivity(sessionData.user_id, 'session_ended', {
    session_id: sessionData.id,
    ended_at: sessionData.ended_at
  });

  console.log('✅ Session ended processed:', sessionData.user_id);
}

// ===== UTILITY FUNCTIONS =====

async function logActivity(userId: string, activityType: string, activityData: any) {
  if (!supabaseAdmin || !userId) return;

  try {
    const { error } = await supabaseAdmin
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        org_id: null,
        activity_type: activityType,
        activity_data: {
          ...activityData,
          processed_at: new Date().toISOString(),
          webhook_source: 'clerk'
        },
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('❌ Error logging activity:', error);
    }
  } catch (error) {
    console.error('❌ Activity logging failed:', error);
    // Don't throw - activity logging shouldn't break webhook processing
  }
}
