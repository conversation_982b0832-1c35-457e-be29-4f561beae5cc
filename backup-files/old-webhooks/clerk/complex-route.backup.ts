/**
 * TalentHUB Enhanced Webhook Handler
 * Enterprise-grade webhook processing using modular operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { supabaseAdmin } from '@/lib/supabase';
import { 
  WebhookSecurityManager,
  createSecurityHeaders,
  logSecurityEvent 
} from '@/lib/webhooks/security';
import { 
  WebhookOperationFactory,
  OperationFactory,
  WebhookLogger 
} from '@/lib/webhooks/operations';
import { ClerkWebhookEventType } from '@/lib/webhooks/types';

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  
  // Apply security headers
  const securityHeaders = createSecurityHeaders();
  
  try {
    // Initialize security manager
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    if (!webhookSecret) {
      logSecurityEvent('Missing webhook secret', 'critical', {});
      return new Response('Webhook configuration error', { 
        status: 500,
        headers: securityHeaders 
      });
    }

    const securityManager = new WebhookSecurityManager(webhookSecret);
    
    // Get raw payload for signature verification
    const payload = await req.text();
    
    // Comprehensive security validation
    const validation = await securityManager.validateWebhookRequest(payload, req);
    
    if (!validation.isValid || !validation.event) {
      logSecurityEvent('Webhook validation failed', 'high', { 
        error: validation.error 
      });
      return new Response('Unauthorized', { 
        status: 401,
        headers: securityHeaders 
      });
    }

    const { event, securityContext } = validation;
    const eventType = event.type as ClerkWebhookEventType;
    const eventId = event.data.id || `evt_${Date.now()}`;

    console.log(`🔄 Processing webhook: ${eventType} [${eventId}]`);

    // Initialize Supabase admin client
    if (!supabaseAdmin) {
      logSecurityEvent('Supabase admin unavailable', 'critical', {});
      return new Response('Database configuration error', { 
        status: 500,
        headers: securityHeaders 
      });
    }

    // Log webhook event start
    const logId = await WebhookLogger.logEventStart(
      supabaseAdmin,
      eventId,
      eventType,
      getUserId(event.data, eventType),
      getOrgId(event.data, eventType),
      event.data
    );

    try {
      // Check if event type is supported
      if (!WebhookOperationFactory.isEventTypeSupported(eventType)) {
        console.log(`⚠️ Unsupported webhook event type: ${eventType}`);
        
        if (logId) {
          await WebhookLogger.logEventCompletion(supabaseAdmin, eventId, {
            success: false,
            duration: Date.now() - startTime,
            error: `Unsupported event type: ${eventType}`
          });
        }

        return NextResponse.json({ 
          message: 'Event acknowledged but not processed',
          eventType,
          supported: false
        }, { headers: securityHeaders });
      }

      // Create operation context
      const context = OperationFactory.createContext(
        supabaseAdmin,
        eventId,
        eventType,
        req
      );

      // Create and execute operation
      const operation = WebhookOperationFactory.createOperation(
        eventType,
        context,
        event.data
      );

      const result = await operation.execute();

      // Log completion
      if (logId) {
        await WebhookLogger.logEventCompletion(supabaseAdmin, eventId, result);
      }

      // Handle security events
      if (securityContext?.requiresAlert) {
        logSecurityEvent(
          `Security event processed: ${eventType}`,
          securityContext.eventSeverity,
          {
            eventType,
            userId: getUserId(event.data, eventType),
            orgId: getOrgId(event.data, eventType),
            context: securityContext.additionalContext
          }
        );
      }

      const response = {
        success: result.success,
        eventType,
        eventId,
        duration: result.duration,
        timestamp: new Date().toISOString(),
        metadata: operation.getMetadata()
      };

      if (!result.success) {
        console.error(`❌ Webhook processing failed: ${result.error}`);
        response.error = result.error;
      } else {
        console.log(`✅ Webhook processed successfully: ${eventType} [${result.duration}ms]`);
      }

      return NextResponse.json(response, { 
        status: result.success ? 200 : 500,
        headers: securityHeaders 
      });

    } catch (operationError) {
      const errorMessage = operationError instanceof Error ? operationError.message : 'Unknown operation error';
      console.error('❌ Operation execution failed:', operationError);

      // Log operation failure
      if (logId) {
        await WebhookLogger.logEventCompletion(supabaseAdmin, eventId, {
          success: false,
          duration: Date.now() - startTime,
          error: errorMessage
        });
      }

      return NextResponse.json({
        success: false,
        eventType,
        eventId,
        error: 'Internal processing error',
        duration: Date.now() - startTime
      }, { 
        status: 500,
        headers: securityHeaders 
      });
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown webhook error';
    console.error('❌ Webhook handler failed:', error);
    
    logSecurityEvent('Webhook handler failure', 'high', { 
      error: errorMessage 
    });

    return NextResponse.json({
      success: false,
      error: 'Webhook processing failed',
      duration: Date.now() - startTime
    }, { 
      status: 500,
      headers: securityHeaders 
    });
  }
}

// ===== UTILITY FUNCTIONS =====

function getUserId(eventData: any, eventType: ClerkWebhookEventType): string | undefined {
  // Direct user events
  if (eventType.startsWith('user.') || eventType.startsWith('session.')) {
    return eventData.id || eventData.user_id;
  }
  
  // Membership events
  if (eventType.includes('Membership')) {
    return eventData.public_user_data?.user_id;
  }
  
  // Invitation events
  if (eventType.includes('Invitation')) {
    return eventData.inviter_user_id;
  }
  
  // Organization events
  if (eventType.startsWith('organization.')) {
    return eventData.created_by;
  }
  
  return undefined;
}

function getOrgId(eventData: any, eventType: ClerkWebhookEventType): string | undefined {
  // Organization events
  if (eventType.startsWith('organization.')) {
    return eventData.id;
  }
  
  // Membership and invitation events
  if (eventType.includes('Membership') || eventType.includes('Invitation')) {
    return eventData.organization?.id;
  }
  
  return undefined;
}
