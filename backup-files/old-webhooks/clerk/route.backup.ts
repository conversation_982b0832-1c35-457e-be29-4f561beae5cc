import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { Webhook } from 'svix';
import { WebhookEvent } from '@clerk/nextjs/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function POST(req: NextRequest) {
  // Get the raw body as text (required for svix signature verification)
  const payload = await req.text();
  
  // Get the headers  
  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error('Missing svix headers');
    return new Response('Error occurred -- no svix headers', {
      status: 400,
    });
  }

  // Verify webhook secret is configured
  if (!process.env.CLERK_WEBHOOK_SECRET) {
    console.error('CLERK_WEBHOOK_SECRET not configured');
    return new Response('Webhook secret not configured', { status: 500 });
  }

  // Create a new Svix instance with your secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET);

  let evt: WebhookEvent;

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error verifying webhook', {
      status: 400,
    });
  }

  // Get the ID and type
  const { id } = evt.data;
  const eventType = evt.type;

  console.log(`✅ Webhook verified! ID: ${id}, Type: ${eventType}`);

  // Initialize Supabase admin client (bypasses RLS for webhook operations)
  if (!supabaseAdmin) {
    console.error('Supabase admin client not available');
    return new Response('Database configuration error', { status: 500 });
  }
  
  const supabase = supabaseAdmin;

  try {
    // Handle different webhook events
    switch (eventType) {
      case 'user.created':
        await handleUserCreated(evt.data, supabase);
        break;
      case 'user.updated':
        await handleUserUpdated(evt.data, supabase);
        break;
      case 'organizationMembership.created':
        await handleOrgMembershipCreated(evt.data, supabase);
        break;
      case 'organizationMembership.deleted':
        await handleOrgMembershipDeleted(evt.data, supabase);
        break;
      case 'session.ended':
        await handleSessionEnded(evt.data, supabase);
        break;
      default:
        console.log(`Unhandled webhook event type: ${eventType}`);
    }

    console.log(`✅ Webhook processed successfully: ${eventType}`);
    return NextResponse.json({ message: 'Webhook processed successfully', eventType });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response('Error processing webhook', { status: 500 });
  }
}

// Handle user created event
async function handleUserCreated(userData: any, supabase: any) {
  try {
    console.log('Processing user.created event:', userData.id);
    
    // Extract user data
    const { id: userId, first_name, last_name, email_addresses } = userData;
    const primaryEmail = email_addresses?.find((email: any) => email.id === userData.primary_email_address_id);

    // Create user preferences record
    const { data, error } = await supabase
      .from('user_preferences')
      .upsert({
        user_id: userId,
        first_name: first_name || '',
        last_name: last_name || '',
        username: `${first_name || 'user'}_${userId.slice(-8)}`, // Generate initial username
        display_name: `${first_name || ''} ${last_name || ''}`.trim(),
        timezone: 'UTC',
        theme: 'system',
        account_tier: 'free',
        subscription_status: 'active',
        features_enabled: ['basic'],
        profile_completeness: 25, // Basic info provided
        last_active: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error('Error creating user preferences:', error);
      throw error;
    }

    console.log('✅ User preferences created:', data);

    // Track user activity
    await trackActivity(userId, 'user_created', { 
      email: primaryEmail?.email_address,
      signup_method: 'clerk_auth'
    }, supabase);

    console.log(`✅ User preferences created for user: ${userId}`);
  } catch (error) {
    console.error('Error in handleUserCreated:', error);
    throw error;
  }
}

// Handle user updated event  
async function handleUserUpdated(userData: any, supabase: any) {
  try {
    console.log('Processing user.updated event:', userData.id);
    
    const { id: userId, first_name, last_name } = userData;

    // Update user preferences
    const { error } = await supabase
      .from('user_preferences')
      .update({
        first_name: first_name || '',
        last_name: last_name || '',
        display_name: `${first_name || ''} ${last_name || ''}`.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }

    // Track activity
    await trackActivity(userId, 'profile_updated', {
      updated_fields: ['first_name', 'last_name']
    }, supabase);

    console.log(`✅ User preferences updated for user: ${userId}`);
  } catch (error) {
    console.error('Error in handleUserUpdated:', error);
    throw error;
  }
}

// Handle organization membership created
async function handleOrgMembershipCreated(membershipData: any, supabase: any) {
  try {
    console.log('Processing organizationMembership.created event');
    
    const { public_user_data, organization } = membershipData;
    const userId = public_user_data?.user_id;
    const orgId = organization?.id;

    if (!userId || !orgId) {
      console.warn('Missing user ID or org ID in membership data');
      return;
    }

    // Update user preferences with organization context
    const { error } = await supabase
      .from('user_preferences')
      .update({
        org_id: orgId,
        is_org_member: true,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user org membership:', error);
      throw error;
    }

    // Track activity
    await trackActivity(userId, 'org_membership_created', {
      organization_id: orgId,
      organization_name: organization?.name
    }, supabase);

    console.log(`✅ Organization membership created for user: ${userId}, org: ${orgId}`);
  } catch (error) {
    console.error('Error in handleOrgMembershipCreated:', error);
    throw error;
  }
}

// Handle organization membership deleted
async function handleOrgMembershipDeleted(membershipData: any, supabase: any) {
  try {
    console.log('Processing organizationMembership.deleted event');
    
    const { public_user_data } = membershipData;
    const userId = public_user_data?.user_id;

    if (!userId) {
      console.warn('Missing user ID in membership deletion data');
      return;
    }

    // Remove organization context from user preferences
    const { error } = await supabase
      .from('user_preferences')
      .update({
        org_id: null,
        is_org_member: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error removing user org membership:', error);
      throw error;
    }

    // Track activity
    await trackActivity(userId, 'org_membership_deleted', {}, supabase);

    console.log(`✅ Organization membership removed for user: ${userId}`);
  } catch (error) {
    console.error('Error in handleOrgMembershipDeleted:', error);
    throw error;
  }
}

// Handle session ended event
async function handleSessionEnded(sessionData: any, supabase: any) {
  try {
    console.log('Processing session.ended event');
    
    const userId = sessionData?.user_id;
    
    if (!userId) {
      console.warn('Missing user ID in session data');
      return;
    }

    // Update last active timestamp
    const { error } = await supabase
      .from('user_preferences')
      .update({
        last_active: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating last active:', error);
      throw error;
    }

    console.log(`✅ Last active updated for user: ${userId}`);
  } catch (error) {
    console.error('Error in handleSessionEnded:', error);
    throw error;
  }
}

// Track user activity
async function trackActivity(
  userId: string, 
  activityType: string, 
  activityData: any, 
  supabase: any,
  request?: NextRequest
) {
  try {
    const { error } = await supabase
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        activity_type: activityType,
        activity_data: activityData,
        ip_address: request?.ip || null,
        user_agent: request?.headers.get('user-agent') || null,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error tracking activity:', error);
    } else {
      console.log(`✅ Activity tracked: ${activityType} for user: ${userId}`);
    }
  } catch (error) {
    console.error('Error in trackActivity:', error);
  }
}