/**
 * Clerk Integration for Timezone Management
 * 
 * Handles user and organization timezone preferences 
 * integrated with Clerk authentication system
 */

import { useUser, useOrganization } from '@clerk/nextjs'
import { getEffectiveTimezone, type TimezoneConfig, isValidTimezone } from './timezone'

// Clerk metadata interfaces
interface UserTimezoneMetadata {
  timezone?: string
  timezoneSetAt?: string // ISO timestamp when timezone was last set
  autoDetected?: boolean // Whether timezone was auto-detected vs manually set
}

interface OrganizationTimezoneMetadata {
  defaultTimezone?: string
  timezonePolicy?: 'user_choice' | 'organization_standard' | 'flexible'
  allowUserOverride?: boolean
}

/**
 * Custom hook to get timezone configuration from Clerk
 * Combines user preferences with organization settings
 */
export function useTimezoneConfig(): TimezoneConfig & {
  effectiveTimezone: string
  loading: boolean
  userCanOverride: boolean
  isAutoDetected: boolean
} {
  const { user, isLoaded: userLoaded } = useUser()
  const { organization, isLoaded: orgLoaded } = useOrganization()
  
  const loading = !userLoaded || !orgLoaded
  
  // Extract user timezone preference from metadata
  const userMetadata = user?.publicMetadata as UserTimezoneMetadata | undefined
  const userTimezone = userMetadata?.timezone
  const isAutoDetected = userMetadata?.autoDetected ?? false
  
  // Extract organization timezone settings from metadata
  const orgMetadata = organization?.publicMetadata as OrganizationTimezoneMetadata | undefined
  const organizationTimezone = orgMetadata?.defaultTimezone || 'UTC'
  const timezonePolicy = orgMetadata?.timezonePolicy || 'user_choice'
  const allowUserOverride = orgMetadata?.allowUserOverride ?? true
  
  // Determine if user can override organization timezone
  const userCanOverride = timezonePolicy !== 'organization_standard' && allowUserOverride
  
  // Build timezone config
  const config: TimezoneConfig = {
    userTimezone: userCanOverride ? userTimezone : null,
    organizationTimezone,
    fallbackTimezone: 'UTC'
  }
  
  const effectiveTimezone = getEffectiveTimezone(config)
  
  return {
    ...config,
    effectiveTimezone,
    loading,
    userCanOverride,
    isAutoDetected
  }
}

/**
 * Hook to get timezone update function
 * Returns a function that can update the user's timezone
 */
export function useTimezoneUpdate() {
  const { user } = useUser()
  
  const updateUserTimezone = async (
    timezone: string,
    options: {
      autoDetected?: boolean
    } = {}
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }
      
      if (!isValidTimezone(timezone)) {
        return { success: false, error: 'Invalid timezone' }
      }
      
      const currentMetadata = user.publicMetadata as UserTimezoneMetadata | undefined
      
      const updatedMetadata: UserTimezoneMetadata = {
        ...currentMetadata,
        timezone,
        timezoneSetAt: new Date().toISOString(),
        autoDetected: options.autoDetected ?? false
      }
      
      await user.update({
        publicMetadata: updatedMetadata
      })
      
      return { success: true }
    } catch (error) {
      console.error('Error updating user timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return updateUserTimezone
}

/**
 * Hook to get organization timezone update function
 * Returns a function that can update organization timezone settings
 */
export function useOrganizationTimezoneUpdate() {
  const { organization } = useOrganization()
  
  const updateOrganizationTimezone = async (
    settings: {
      defaultTimezone: string
      timezonePolicy?: 'user_choice' | 'organization_standard' | 'flexible'
      allowUserOverride?: boolean
    }
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!organization) {
        return { success: false, error: 'No organization found' }
      }
      
      if (!isValidTimezone(settings.defaultTimezone)) {
        return { success: false, error: 'Invalid timezone' }
      }
      
      const currentMetadata = organization.publicMetadata as OrganizationTimezoneMetadata | undefined
      
      const updatedMetadata: OrganizationTimezoneMetadata = {
        ...currentMetadata,
        defaultTimezone: settings.defaultTimezone,
        timezonePolicy: settings.timezonePolicy || 'user_choice',
        allowUserOverride: settings.allowUserOverride ?? true
      }
      
      await organization.update({
        publicMetadata: updatedMetadata
      })
      
      return { success: true }
    } catch (error) {
      console.error('Error updating organization timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return updateOrganizationTimezone
}

/**
 * Hook to get auto-detect timezone function
 * Returns a function that can auto-detect and set user timezone
 */
export function useTimezoneAutoDetect() {
  const { user } = useUser()
  const updateUserTimezone = useTimezoneUpdate()
  
  const autoDetectUserTimezone = async (): Promise<{ 
    success: boolean 
    timezone?: string 
    error?: string 
  }> => {
    try {
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }
      
      const currentMetadata = user.publicMetadata as UserTimezoneMetadata | undefined
      
      // Only auto-detect if user hasn't manually set a timezone
      if (currentMetadata?.timezone && !currentMetadata.autoDetected) {
        return { 
          success: true, 
          timezone: currentMetadata.timezone,
          error: 'User already has manually set timezone'
        }
      }
      
      // Detect browser timezone
      const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      
      if (!isValidTimezone(detectedTimezone)) {
        return { success: false, error: 'Could not detect valid timezone' }
      }
      
      // Update user metadata with auto-detected timezone
      const result = await updateUserTimezone(detectedTimezone, { autoDetected: true })
      
      if (result.success) {
        return { success: true, timezone: detectedTimezone }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error) {
      console.error('Error auto-detecting timezone:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  return autoDetectUserTimezone
}

/**
 * Get timezone info for displaying in UI
 */
export function useTimezoneInfo() {
  const config = useTimezoneConfig()
  
  return {
    ...config,
    timezoneDisplayName: getTimezoneDisplay(config.effectiveTimezone),
    canChangeTimezone: config.userCanOverride,
    needsTimezoneSetup: !config.userTimezone && !config.isAutoDetected
  }
}

/**
 * Hook for organization timezone management
 * Provides admin functions for timezone policy
 */
export function useOrganizationTimezoneAdmin() {
  const { organization, membership } = useOrganization()
  const updateOrganizationTimezone = useOrganizationTimezoneUpdate()
  
  const isAdmin = membership?.role === 'admin' || membership?.role === 'basic_member'
  
  const orgMetadata = organization?.publicMetadata as OrganizationTimezoneMetadata | undefined
  
  return {
    isAdmin,
    currentSettings: {
      defaultTimezone: orgMetadata?.defaultTimezone || 'UTC',
      timezonePolicy: orgMetadata?.timezonePolicy || 'user_choice',
      allowUserOverride: orgMetadata?.allowUserOverride ?? true
    },
    updateSettings: isAdmin ? updateOrganizationTimezone : undefined
  }
}

// Helper function to get timezone display name
function getTimezoneDisplay(timezone: string): string {
  try {
    const cityName = timezone.split('/')[1]?.replace(/_/g, ' ') || timezone
    const now = new Date()
    
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short'
    })
    
    const parts = formatter.formatToParts(now)
    const abbreviation = parts.find(part => part.type === 'timeZoneName')?.value || ''
    
    return `${cityName} (${abbreviation})`
  } catch {
    return timezone
  }
}
