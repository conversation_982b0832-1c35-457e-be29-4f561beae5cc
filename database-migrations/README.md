# Database Migrations

This directory contains SQL migration files for TalentHUB database schema changes.

## Migration Naming Convention

```
XXX_migration_name.sql
```

Where:
- `XXX` = Zero-padded migration number (000, 001, 002, etc.)
- `migration_name` = Descriptive name in snake_case

## Migration Order

| File | Description | Status |
|------|-------------|---------|
| `000_initial_schema.sql` | Base schema with organizations and user roles | ✅ Applied |
| `001_add_timezone_support.sql` | Add timezone columns and constraints | ✅ Applied (2025-06-24) |

## How to Apply Migrations

### Using Supabase CLI
```bash
# Apply specific migration
supabase db reset
# Or apply manually through SQL editor
```

### Using SQL Editor (Supabase Dashboard)
1. Go to https://supabase.com/dashboard
2. Select your project
3. Navigate to SQL Editor
4. Copy and paste migration content
5. Execute

### Using MCP (if available)
```bash
# Apply migration through MCP Supabase integration
# (Will use this method below)
```

## Migration Status Tracking

Each migration should:
- ✅ Be tested on development environment first
- ✅ Include rollback strategy if needed
- ✅ Have proper error handling with IF NOT EXISTS
- ✅ Include comments explaining changes
- ✅ Update this README when applied

## Notes

- All timestamps use `TIMESTAMP WITH TIME ZONE` for timezone support
- Row Level Security (RLS) is enabled on all tables
- Extensions used: `uuid-ossp`, `vector`
- Triggers handle automatic `updated_at` timestamps
