-- Migration: Add organization association tables for join workflows
-- This migration adds tables to support organization join requests, domain management, and audit trails

-- Organization domains table (for domain-based organization detection)
CREATE TABLE IF NOT EXISTS org_domains (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  org_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  domain TEXT UNIQUE NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  auto_join_enabled BOOLEAN DEFAULT true,
  verification_method TEXT CHECK (verification_method IN ('dns', 'email', 'file')),
  verification_token TEXT,
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by TEXT, -- Clerk user ID
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organization join requests table (for managing join organization workflows)
CREATE TABLE org_join_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL, -- Clerk user ID
  org_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  domain TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
  request_type TEXT NOT NULL DEFAULT 'domain_detection' CHECK (request_type IN ('domain_detection', 'manual_request', 'invitation')),
  message TEXT, -- Optional message from user
  
  -- Admin response
  reviewed_by TEXT, -- Clerk user ID of admin who reviewed
  reviewed_at TIMESTAMP WITH TIME ZONE,
  admin_notes TEXT,
  
  -- Automatic expiration
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure user can only have one pending request per org
  UNIQUE(user_id, org_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- Organization membership audit table (for tracking association changes)
CREATE TABLE org_membership_audit (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL, -- Clerk user ID
  org_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
  action TEXT NOT NULL CHECK (action IN ('joined', 'left', 'promoted', 'demoted', 'removed')),
  previous_status TEXT,
  new_status TEXT,
  
  -- Context information
  triggered_by TEXT, -- Clerk user ID who triggered the change
  trigger_type TEXT NOT NULL CHECK (trigger_type IN ('user_request', 'admin_action', 'auto_join', 'domain_verification')),
  details JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for all new tables
ALTER TABLE org_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE org_join_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE org_membership_audit ENABLE ROW LEVEL SECURITY;

-- RLS Policies for org_domains
CREATE POLICY "Users can view domains for their organization" ON org_domains
  FOR SELECT USING (
    org_id IN (
      SELECT id FROM organizations WHERE clerk_org_id IN (
        SELECT DISTINCT jsonb_array_elements_text(
          (auth.jwt() -> 'org_memberships')::jsonb
        )
      )
    )
  );

CREATE POLICY "Org admins can manage domains" ON org_domains
  FOR ALL USING (
    org_id IN (
      SELECT id FROM organizations WHERE clerk_org_id IN (
        SELECT DISTINCT jsonb_array_elements_text(
          (auth.jwt() -> 'org_memberships')::jsonb
        )
      )
    )
  );

-- RLS Policies for org_join_requests  
CREATE POLICY "Users can view their own join requests" ON org_join_requests
  FOR SELECT USING (
    user_id = (auth.jwt() -> 'sub')::text
  );

CREATE POLICY "Users can create their own join requests" ON org_join_requests
  FOR INSERT WITH CHECK (
    user_id = (auth.jwt() -> 'sub')::text
  );

CREATE POLICY "Org admins can view and manage requests for their org" ON org_join_requests
  FOR ALL USING (
    org_id IN (
      SELECT id FROM organizations WHERE clerk_org_id IN (
        SELECT DISTINCT jsonb_array_elements_text(
          (auth.jwt() -> 'org_memberships')::jsonb
        )
      )
    )
  );

-- RLS Policies for org_membership_audit
CREATE POLICY "Users can view their own membership audit" ON org_membership_audit
  FOR SELECT USING (
    user_id = (auth.jwt() -> 'sub')::text
  );

CREATE POLICY "Org admins can view audit for their org" ON org_membership_audit
  FOR SELECT USING (
    org_id IN (
      SELECT id FROM organizations WHERE clerk_org_id IN (
        SELECT DISTINCT jsonb_array_elements_text(
          (auth.jwt() -> 'org_memberships')::jsonb
        )
      )
    )
  );

-- Create indexes for performance
CREATE INDEX idx_org_domains_org_id ON org_domains(org_id);
CREATE INDEX idx_org_domains_domain ON org_domains(domain);
CREATE INDEX idx_org_join_requests_user_id ON org_join_requests(user_id);
CREATE INDEX idx_org_join_requests_org_id ON org_join_requests(org_id);
CREATE INDEX idx_org_join_requests_status ON org_join_requests(status);
CREATE INDEX idx_org_join_requests_expires_at ON org_join_requests(expires_at);
CREATE INDEX idx_org_membership_audit_user_id ON org_membership_audit(user_id);
CREATE INDEX idx_org_membership_audit_org_id ON org_membership_audit(org_id);

-- Add updated_at triggers
CREATE TRIGGER update_org_domains_updated_at
  BEFORE UPDATE ON org_domains
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER update_org_join_requests_updated_at
  BEFORE UPDATE ON org_join_requests
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Database function to detect organization by domain with join request support
CREATE OR REPLACE FUNCTION detect_org_by_domain(email_domain TEXT)
RETURNS TABLE (
  org_id UUID,
  org_name TEXT,
  auto_join_enabled BOOLEAN,
  has_pending_request BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    o.id as org_id,
    o.name as org_name,
    od.auto_join_enabled,
    EXISTS(
      SELECT 1 FROM org_join_requests ojr 
      WHERE ojr.org_id = o.id 
        AND ojr.user_id = (auth.jwt() -> 'sub')::text
        AND ojr.status = 'pending'
    ) as has_pending_request
  FROM organizations o
  JOIN org_domains od ON od.org_id = o.id
  WHERE od.domain = email_domain 
    AND od.is_verified = true
    AND o.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create join request with duplicate prevention
CREATE OR REPLACE FUNCTION create_join_request(
  p_user_id TEXT,
  p_org_id UUID,
  p_email TEXT,
  p_domain TEXT,
  p_request_type TEXT DEFAULT 'domain_detection',
  p_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  request_id UUID;
  existing_request UUID;
BEGIN
  -- Check for existing pending request
  SELECT id INTO existing_request
  FROM org_join_requests
  WHERE user_id = p_user_id 
    AND org_id = p_org_id 
    AND status = 'pending';
  
  IF existing_request IS NOT NULL THEN
    -- Update existing request instead of creating new one
    UPDATE org_join_requests 
    SET 
      email = p_email,
      domain = p_domain,
      request_type = p_request_type,
      message = p_message,
      expires_at = NOW() + INTERVAL '7 days',
      updated_at = NOW()
    WHERE id = existing_request;
    
    RETURN existing_request;
  END IF;
  
  -- Create new join request
  INSERT INTO org_join_requests (
    user_id, org_id, email, domain, request_type, message
  ) VALUES (
    p_user_id, p_org_id, p_email, p_domain, p_request_type, p_message
  ) RETURNING id INTO request_id;
  
  RETURN request_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
