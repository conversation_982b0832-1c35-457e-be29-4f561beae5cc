-- Migration: User Preferences System
-- Description: Creates user_preferences table with global username uniqueness and org_domains for domain-based organization mapping
-- Created: June 25, 2025
-- Dependencies: organizations table (existing)

-- Enable necessary extensions if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 1. USER PREFERENCES TABLE
-- =============================================

-- Create user_preferences table
CREATE TABLE public.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Identity linking (Clerk integration)
    user_id TEXT UNIQUE NOT NULL,                    -- Clerk user ID
    org_id UUID REFERENCES public.organizations(id) ON DELETE SET NULL,
    is_org_member BOOLEAN DEFAULT false,             -- Formal vs domain-based association
    
    -- Profile Information (all required for professional identity)
    first_name TEXT NOT NULL CHECK (length(trim(first_name)) >= 2 AND length(trim(first_name)) <= 50),
    last_name TEXT NOT NULL CHECK (length(trim(last_name)) >= 2 AND length(trim(last_name)) <= 50),
    username TEXT UNIQUE NOT NULL CHECK (
        length(username) >= 3 AND 
        length(username) <= 30 AND 
        username ~ '^[a-zA-Z0-9._-]+$'
    ),
    display_name TEXT CHECK (length(display_name) <= 100),  -- Optional professional title
    
    -- User Preferences
    timezone TEXT DEFAULT 'America/Chicago' CHECK (timezone IS NOT NULL),
    theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    process_context TEXT DEFAULT 'both' CHECK (process_context IN ('recruitment', 'bench_sales', 'both')),
    
    -- Account Information  
    account_tier TEXT DEFAULT 'free' CHECK (account_tier IN ('free', 'pro', 'team', 'enterprise')),
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'past_due', 'unpaid')),
    
    -- Feature flags for gradual rollout
    features_enabled JSONB DEFAULT '{}',
    
    -- Audit trail
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Add helpful comments
COMMENT ON TABLE public.user_preferences IS 'Stores user profile information, preferences, and account details with global username uniqueness';
COMMENT ON COLUMN public.user_preferences.user_id IS 'Clerk user ID for authentication integration';
COMMENT ON COLUMN public.user_preferences.username IS 'Globally unique username across entire platform';
COMMENT ON COLUMN public.user_preferences.is_org_member IS 'True for formal org members, false for domain-based association';
COMMENT ON COLUMN public.user_preferences.process_context IS 'User preference for recruitment, bench sales, or both processes';

-- =============================================
-- 2. ORGANIZATION DOMAINS TABLE  
-- =============================================

-- Create org_domains table for domain-based organization mapping
CREATE TABLE public.org_domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Organization reference
    org_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Domain information
    domain TEXT NOT NULL CHECK (
        domain ~ '^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$' AND
        length(domain) >= 4 AND 
        length(domain) <= 255
    ),
    
    -- Domain settings
    is_verified BOOLEAN DEFAULT false,               -- Manual verification by admin
    auto_join_enabled BOOLEAN DEFAULT true,          -- Auto-associate users from this domain
    
    -- Audit trail
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    UNIQUE(domain)  -- Ensure one domain maps to one organization
);

-- Add helpful comments
COMMENT ON TABLE public.org_domains IS 'Maps email domains to organizations for automatic user association';
COMMENT ON COLUMN public.org_domains.domain IS 'Email domain (e.g., company.com) for organization mapping';
COMMENT ON COLUMN public.org_domains.auto_join_enabled IS 'Whether users with this domain automatically join the organization';

-- =============================================
-- 3. INDEXES FOR PERFORMANCE
-- =============================================

-- Primary lookup indexes
CREATE INDEX idx_user_preferences_user_id ON public.user_preferences(user_id);
CREATE INDEX idx_user_preferences_org_id ON public.user_preferences(org_id);
CREATE INDEX idx_user_preferences_username_lower ON public.user_preferences(lower(username));

-- Domain lookup indexes
CREATE INDEX idx_org_domains_domain ON public.org_domains(domain);
CREATE INDEX idx_org_domains_org_id ON public.org_domains(org_id);
CREATE INDEX idx_org_domains_auto_join ON public.org_domains(auto_join_enabled) WHERE auto_join_enabled = true;

-- Composite indexes for common queries
CREATE INDEX idx_user_preferences_org_member ON public.user_preferences(org_id, is_org_member);
CREATE INDEX idx_user_preferences_account_tier ON public.user_preferences(account_tier, subscription_status);

-- =============================================
-- 4. ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.org_domains ENABLE ROW LEVEL SECURITY;

-- User Preferences Policies
-- Users can only access their own preferences
CREATE POLICY "Users can view own preferences" ON public.user_preferences
    FOR SELECT USING (auth.jwt() ->> 'sub' = user_id);

CREATE POLICY "Users can update own preferences" ON public.user_preferences  
    FOR UPDATE USING (auth.jwt() ->> 'sub' = user_id);

CREATE POLICY "Users can insert own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.jwt() ->> 'sub' = user_id);

-- Org admins can view preferences of their org members
CREATE POLICY "Org admins can view org member preferences" ON public.user_preferences
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_process_roles upr
            WHERE upr.user_id = auth.jwt() ->> 'sub'
            AND upr.org_id = user_preferences.org_id
            AND upr.role IN ('admin', 'manager')
            AND upr.is_active = true
        )
    );

-- Org Domains Policies  
-- Org admins can manage their organization's domains
CREATE POLICY "Org admins can manage org domains" ON public.org_domains
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_process_roles upr
            WHERE upr.user_id = auth.jwt() ->> 'sub'
            AND upr.org_id = org_domains.org_id
            AND upr.role = 'admin'
            AND upr.is_active = true
        )
    );

-- Public read access for domain detection (limited to domain field only)
CREATE POLICY "Public domain lookup for org detection" ON public.org_domains
    FOR SELECT USING (auto_join_enabled = true);

-- =============================================
-- 5. TRIGGERS FOR AUDIT TRAIL
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to user_preferences
CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =============================================
-- 6. UTILITY FUNCTIONS
-- =============================================

-- Function to check username availability
CREATE OR REPLACE FUNCTION public.is_username_available(check_username TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT EXISTS (
        SELECT 1 FROM public.user_preferences 
        WHERE lower(username) = lower(check_username)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to suggest username based on first and last name
CREATE OR REPLACE FUNCTION public.suggest_username(first_name_param TEXT, last_name_param TEXT)
RETURNS TEXT AS $$
DECLARE
    base_username TEXT;
    suggested_username TEXT;
    counter INTEGER := 0;
BEGIN
    -- Create base username from first.last
    base_username := lower(trim(first_name_param)) || '.' || lower(trim(last_name_param));
    
    -- Remove any invalid characters and replace with dots
    base_username := regexp_replace(base_username, '[^a-zA-Z0-9._-]', '.', 'g');
    
    -- Remove consecutive dots
    base_username := regexp_replace(base_username, '\.+', '.', 'g');
    
    -- Remove leading/trailing dots
    base_username := trim(base_username, '.');
    
    -- Ensure minimum length
    IF length(base_username) < 3 THEN
        base_username := base_username || '1';
    END IF;
    
    -- Check if base username is available
    suggested_username := base_username;
    
    -- If not available, add numbers until we find an available one
    WHILE NOT public.is_username_available(suggested_username) LOOP
        counter := counter + 1;
        suggested_username := base_username || counter;
        
        -- Prevent infinite loop
        IF counter > 999 THEN
            suggested_username := base_username || extract(epoch from now())::bigint;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN suggested_username;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to detect organization by email domain
CREATE OR REPLACE FUNCTION public.detect_org_by_domain(email_domain TEXT)
RETURNS TABLE(org_id UUID, org_name TEXT, auto_join_enabled BOOLEAN) AS $$
BEGIN
    RETURN QUERY
    SELECT od.org_id, o.name, od.auto_join_enabled
    FROM public.org_domains od
    JOIN public.organizations o ON od.org_id = o.id
    WHERE od.domain = lower(email_domain)
    AND od.auto_join_enabled = true
    AND o.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 7. SAMPLE DATA FOR TESTING (OPTIONAL)
-- =============================================

-- Insert sample org domains for testing
INSERT INTO public.org_domains (org_id, domain, is_verified, auto_join_enabled)
SELECT 
    o.id,
    CASE 
        WHEN o.name ILIKE '%tech%' THEN 'techcorp.com'
        WHEN o.name ILIKE '%global%' THEN 'global-solutions.com'
        WHEN o.name ILIKE '%consulting%' THEN 'consulting-firm.com'
        ELSE lower(replace(o.name, ' ', '')) || '.com'
    END,
    true,
    true
FROM public.organizations o
WHERE NOT EXISTS (
    SELECT 1 FROM public.org_domains od WHERE od.org_id = o.id
)
LIMIT 3;  -- Only add a few for testing
