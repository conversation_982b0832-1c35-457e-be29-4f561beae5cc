-- Migration: Enhanced Webhook Support for Complete Clerk Event Handling
-- Description: Adds fields to support comprehensive Clerk webhook events including deletions, enhanced activity tracking
-- Date: June 28, 2025

-- =============================================
-- PART 1: ENHANCE USER_PREFERENCES TABLE FOR WEBHOOK EVENTS
-- =============================================

-- Add fields for user deletion and enhanced tracking
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT false;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Add enhanced profile completeness tracking
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS profile_completeness INTEGER DEFAULT 0;

-- Add constraint for profile completeness
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_profile_completeness_range') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_profile_completeness_range 
      CHECK (profile_completeness >= 0 AND profile_completeness <= 100);
  END IF;
END $$;

-- =============================================
-- PART 2: ENHANCE ORGANIZATIONS TABLE FOR WEBHOOK EVENTS
-- =============================================

-- Add fields for organization deletion tracking
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Ensure created_by field exists for tracking organization creators
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS created_by TEXT; -- Clerk user ID

-- =============================================
-- PART 3: ENHANCE USER_ACTIVITY_LOGS FOR COMPREHENSIVE TRACKING
-- =============================================

-- Ensure user_activity_logs table has all necessary fields for webhook events
-- (This table should already exist from previous migrations, but we'll ensure completeness)

-- Add indexes for better webhook event querying
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_activity_type ON user_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at_desc ON user_activity_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_org ON user_activity_logs(user_id, org_id);

-- Add index for security event monitoring
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_security_events ON user_activity_logs(activity_type, created_at) 
  WHERE activity_data->>'security_event' = 'true';

-- =============================================
-- PART 4: CREATE WEBHOOK EVENT TRACKING TABLE
-- =============================================

-- Table to track webhook event processing for debugging and analytics
CREATE TABLE IF NOT EXISTS webhook_event_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id TEXT, -- Svix event ID
  event_type TEXT NOT NULL,
  clerk_user_id TEXT,
  clerk_org_id TEXT,
  processing_status TEXT NOT NULL DEFAULT 'processing' CHECK (processing_status IN ('processing', 'success', 'failed', 'retry')),
  processing_duration_ms INTEGER,
  error_message TEXT,
  error_details JSONB,
  retry_count INTEGER DEFAULT 0,
  event_data JSONB,
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for webhook_event_logs (admin access only)
ALTER TABLE webhook_event_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policy for webhook_event_logs (system/admin access only)
CREATE POLICY "System can manage webhook event logs" ON webhook_event_logs
  FOR ALL USING (true); -- This will be restricted by application logic

-- Indexes for webhook event logs
CREATE INDEX IF NOT EXISTS idx_webhook_event_logs_event_type ON webhook_event_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_event_logs_status ON webhook_event_logs(processing_status);
CREATE INDEX IF NOT EXISTS idx_webhook_event_logs_user_id ON webhook_event_logs(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_webhook_event_logs_org_id ON webhook_event_logs(clerk_org_id);
CREATE INDEX IF NOT EXISTS idx_webhook_event_logs_created_at ON webhook_event_logs(created_at DESC);

-- =============================================
-- PART 5: CREATE ORGANIZATION INVITATION TRACKING TABLE
-- =============================================

-- Table to track organization invitations for analytics
CREATE TABLE IF NOT EXISTS org_invitation_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  clerk_invitation_id TEXT UNIQUE NOT NULL,
  organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
  clerk_org_id TEXT NOT NULL,
  inviter_user_id TEXT, -- Clerk user ID
  invited_email TEXT NOT NULL,
  invited_role TEXT,
  status TEXT NOT NULL CHECK (status IN ('created', 'accepted', 'revoked', 'expired')),
  status_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  invitation_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for org_invitation_tracking
ALTER TABLE org_invitation_tracking ENABLE ROW LEVEL SECURITY;

-- RLS Policies for org_invitation_tracking
CREATE POLICY "Users can view invitations for their organization" ON org_invitation_tracking
  FOR SELECT USING (
    clerk_org_id IN (
      SELECT DISTINCT jsonb_array_elements_text(
        (auth.jwt() -> 'org_memberships')::jsonb
      )
    )
  );

CREATE POLICY "System can manage invitation tracking" ON org_invitation_tracking
  FOR ALL USING (true); -- Restricted by application logic

-- Indexes for org_invitation_tracking
CREATE INDEX IF NOT EXISTS idx_org_invitation_tracking_org_id ON org_invitation_tracking(organization_id);
CREATE INDEX IF NOT EXISTS idx_org_invitation_tracking_clerk_org_id ON org_invitation_tracking(clerk_org_id);
CREATE INDEX IF NOT EXISTS idx_org_invitation_tracking_inviter ON org_invitation_tracking(inviter_user_id);
CREATE INDEX IF NOT EXISTS idx_org_invitation_tracking_status ON org_invitation_tracking(status);
CREATE INDEX IF NOT EXISTS idx_org_invitation_tracking_email ON org_invitation_tracking(invited_email);

-- =============================================
-- PART 6: CREATE SESSION TRACKING TABLE
-- =============================================

-- Table to track user sessions for security monitoring
CREATE TABLE IF NOT EXISTS user_sessions_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  clerk_session_id TEXT UNIQUE NOT NULL,
  user_id TEXT NOT NULL, -- Clerk user ID
  session_status TEXT NOT NULL CHECK (session_status IN ('created', 'active', 'ended', 'removed', 'revoked')),
  
  -- Session metadata
  created_at_clerk TIMESTAMP WITH TIME ZONE,
  ended_at_clerk TIMESTAMP WITH TIME ZONE,
  last_active_at TIMESTAMP WITH TIME ZONE,
  expire_at TIMESTAMP WITH TIME ZONE,
  
  -- Security tracking
  ip_address INET,
  user_agent TEXT,
  is_security_event BOOLEAN DEFAULT false,
  removal_reason TEXT, -- 'manual', 'revoked', 'expired', 'logout'
  
  -- Metadata
  session_metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for user_sessions_tracking
ALTER TABLE user_sessions_tracking ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_sessions_tracking
CREATE POLICY "Users can view their own session tracking" ON user_sessions_tracking
  FOR SELECT USING (
    user_id = (auth.jwt() -> 'sub')::text
  );

CREATE POLICY "System can manage session tracking" ON user_sessions_tracking
  FOR ALL USING (true); -- Restricted by application logic

-- Indexes for user_sessions_tracking
CREATE INDEX IF NOT EXISTS idx_user_sessions_tracking_user_id ON user_sessions_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_tracking_status ON user_sessions_tracking(session_status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_tracking_security ON user_sessions_tracking(is_security_event, created_at) 
  WHERE is_security_event = true;
CREATE INDEX IF NOT EXISTS idx_user_sessions_tracking_active ON user_sessions_tracking(user_id, session_status) 
  WHERE session_status IN ('created', 'active');

-- =============================================
-- PART 7: CREATE WEBHOOK UTILITY FUNCTIONS
-- =============================================

-- Function to log webhook events
CREATE OR REPLACE FUNCTION log_webhook_event(
  p_event_id TEXT,
  p_event_type TEXT,
  p_clerk_user_id TEXT DEFAULT NULL,
  p_clerk_org_id TEXT DEFAULT NULL,
  p_processing_status TEXT DEFAULT 'processing',
  p_event_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO webhook_event_logs (
    event_id, event_type, clerk_user_id, clerk_org_id, 
    processing_status, event_data
  ) VALUES (
    p_event_id, p_event_type, p_clerk_user_id, p_clerk_org_id,
    p_processing_status, p_event_data
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update webhook event status
CREATE OR REPLACE FUNCTION update_webhook_event_status(
  p_event_id TEXT,
  p_status TEXT,
  p_duration_ms INTEGER DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_error_details JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE webhook_event_logs 
  SET 
    processing_status = p_status,
    processing_duration_ms = p_duration_ms,
    error_message = p_error_message,
    error_details = p_error_details,
    processed_at = NOW()
  WHERE event_id = p_event_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track organization invitation
CREATE OR REPLACE FUNCTION track_org_invitation(
  p_clerk_invitation_id TEXT,
  p_clerk_org_id TEXT,
  p_inviter_user_id TEXT,
  p_invited_email TEXT,
  p_invited_role TEXT,
  p_status TEXT,
  p_metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  tracking_id UUID;
  org_internal_id UUID;
BEGIN
  -- Get internal organization ID
  SELECT id INTO org_internal_id
  FROM organizations
  WHERE clerk_org_id = p_clerk_org_id;
  
  INSERT INTO org_invitation_tracking (
    clerk_invitation_id, organization_id, clerk_org_id,
    inviter_user_id, invited_email, invited_role, status,
    invitation_metadata
  ) VALUES (
    p_clerk_invitation_id, org_internal_id, p_clerk_org_id,
    p_inviter_user_id, p_invited_email, p_invited_role, p_status,
    p_metadata
  ) 
  ON CONFLICT (clerk_invitation_id) 
  DO UPDATE SET
    status = p_status,
    status_updated_at = NOW(),
    invitation_metadata = p_metadata,
    updated_at = NOW()
  RETURNING id INTO tracking_id;
  
  RETURN tracking_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track user session
CREATE OR REPLACE FUNCTION track_user_session(
  p_clerk_session_id TEXT,
  p_user_id TEXT,
  p_session_status TEXT,
  p_session_metadata JSONB DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_is_security_event BOOLEAN DEFAULT false,
  p_removal_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  tracking_id UUID;
BEGIN
  INSERT INTO user_sessions_tracking (
    clerk_session_id, user_id, session_status,
    session_metadata, ip_address, user_agent,
    is_security_event, removal_reason
  ) VALUES (
    p_clerk_session_id, p_user_id, p_session_status,
    p_session_metadata, p_ip_address, p_user_agent,
    p_is_security_event, p_removal_reason
  )
  ON CONFLICT (clerk_session_id)
  DO UPDATE SET
    session_status = p_session_status,
    session_metadata = p_session_metadata,
    is_security_event = CASE 
      WHEN p_is_security_event THEN true 
      ELSE user_sessions_tracking.is_security_event 
    END,
    removal_reason = COALESCE(p_removal_reason, user_sessions_tracking.removal_reason),
    updated_at = NOW()
  RETURNING id INTO tracking_id;
  
  RETURN tracking_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- PART 8: ADD UPDATED_AT TRIGGERS FOR NEW TABLES
-- =============================================

-- Add updated_at triggers for new tables
CREATE TRIGGER update_org_invitation_tracking_updated_at
  BEFORE UPDATE ON org_invitation_tracking
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER update_user_sessions_tracking_updated_at
  BEFORE UPDATE ON user_sessions_tracking
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- =============================================
-- PART 9: CREATE ANALYTICS VIEWS FOR WEBHOOK INSIGHTS
-- =============================================

-- View for webhook event analytics
CREATE OR REPLACE VIEW webhook_event_analytics AS
SELECT 
  event_type,
  processing_status,
  COUNT(*) as event_count,
  AVG(processing_duration_ms) as avg_duration_ms,
  MAX(processing_duration_ms) as max_duration_ms,
  MIN(processing_duration_ms) as min_duration_ms,
  COUNT(*) FILTER (WHERE processing_status = 'failed') as failed_count,
  COUNT(*) FILTER (WHERE processing_status = 'success') as success_count,
  COUNT(*) FILTER (WHERE retry_count > 0) as retry_count,
  DATE_TRUNC('day', created_at) as event_date
FROM webhook_event_logs
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY event_type, processing_status, DATE_TRUNC('day', created_at)
ORDER BY event_date DESC, event_type;

-- View for user session analytics
CREATE OR REPLACE VIEW user_session_analytics AS
SELECT 
  u.user_id,
  u.first_name,
  u.last_name,
  COUNT(s.*) as total_sessions,
  COUNT(*) FILTER (WHERE s.session_status = 'active') as active_sessions,
  COUNT(*) FILTER (WHERE s.is_security_event = true) as security_events,
  MAX(s.last_active_at) as last_session_activity,
  DATE_TRUNC('day', s.created_at) as session_date
FROM user_sessions_tracking s
JOIN user_preferences u ON u.user_id = s.user_id
WHERE s.created_at >= NOW() - INTERVAL '30 days'
GROUP BY u.user_id, u.first_name, u.last_name, DATE_TRUNC('day', s.created_at)
ORDER BY session_date DESC, total_sessions DESC;

-- View for organization invitation analytics
CREATE OR REPLACE VIEW org_invitation_analytics AS
SELECT 
  o.name as organization_name,
  o.clerk_org_id,
  COUNT(*) as total_invitations,
  COUNT(*) FILTER (WHERE oi.status = 'accepted') as accepted_invitations,
  COUNT(*) FILTER (WHERE oi.status = 'revoked') as revoked_invitations,
  COUNT(*) FILTER (WHERE oi.status = 'created') as pending_invitations,
  ROUND(
    COUNT(*) FILTER (WHERE oi.status = 'accepted')::numeric / 
    NULLIF(COUNT(*), 0) * 100, 2
  ) as acceptance_rate_percent,
  DATE_TRUNC('week', oi.created_at) as invitation_week
FROM org_invitation_tracking oi
JOIN organizations o ON o.id = oi.organization_id
WHERE oi.created_at >= NOW() - INTERVAL '90 days'
GROUP BY o.name, o.clerk_org_id, DATE_TRUNC('week', oi.created_at)
ORDER BY invitation_week DESC, total_invitations DESC;

-- =============================================
-- MIGRATION COMPLETE
-- =============================================

-- Log migration completion
INSERT INTO webhook_event_logs (
  event_type, processing_status, event_data
) VALUES (
  'migration.009_enhanced_webhook_support', 
  'success',
  '{"migration": "Enhanced webhook support for comprehensive Clerk event handling", "tables_added": ["webhook_event_logs", "org_invitation_tracking", "user_sessions_tracking"], "date": "2025-06-28"}'
);
