-- Migration: Add timezone support to TalentHUB (APPLIED VERSION)
-- Description: Adds timezone preferences for organizations and users
-- Date: June 2025
-- Status: ✅ APPLIED to Supabase project weetwfpiancsqezmjyzr on 2025-06-24
-- Applied as: add_timezone_support_simple

-- Add default timezone to organizations table
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS default_timezone TEXT DEFAULT 'UTC';

-- Add timezone preference to user_process_roles table
ALTER TABLE user_process_roles 
ADD COLUMN IF NOT EXISTS timezone_preference TEXT DEFAULT NULL;

-- Add comment for documentation
COMMENT ON COLUMN organizations.default_timezone IS 
'Default timezone for organization operations. Uses IANA timezone names (e.g., America/New_York). Defaults to UTC.';

COMMENT ON COLUMN user_process_roles.timezone_preference IS 
'User''s preferred timezone override. Uses IANA timezone names. NULL means use organization default.';

-- Create index for timezone queries (optional but recommended for performance)
CREATE INDEX IF NOT EXISTS idx_organizations_timezone ON organizations(default_timezone);
CREATE INDEX IF NOT EXISTS idx_user_roles_timezone ON user_process_roles(timezone_preference) 
WHERE timezone_preference IS NOT NULL;

-- Note: Timezone validation is handled at application level due to PostgreSQL constraint limitations
-- The application validates timezone names using Intl.DateTimeFormat and PostgreSQL's pg_timezone_names
