-- TalentHUB Enhanced Webhook System - Verification Queries
-- Run these in Supabase SQL Editor to verify successful deployment

-- =============================================
-- 1. VERIFY NEW TABLES CREATED
-- =============================================

SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('webhook_event_logs', 'org_invitation_tracking', 'user_sessions_tracking')
ORDER BY table_name;

-- =============================================
-- 2. VERIFY NEW COLUMNS IN EXISTING TABLES
-- =============================================

-- Check user_preferences enhancements
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_preferences' 
AND column_name IN ('is_deleted', 'deleted_at', 'profile_completeness')
ORDER BY column_name;

-- Check organizations enhancements
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'organizations' 
AND column_name IN ('deleted_at', 'created_by')
ORDER BY column_name;

-- =============================================
-- 3. VERIFY INDEXES CREATED
-- =============================================

SELECT 
  indexname,
  tablename,
  indexdef
FROM pg_indexes 
WHERE tablename IN ('user_activity_logs', 'webhook_event_logs', 'user_sessions_tracking')
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- =============================================
-- 4. VERIFY RLS POLICIES
-- =============================================

SELECT 
  tablename,
  policyname,
  cmd,
  roles,
  qual
FROM pg_policies 
WHERE tablename IN ('webhook_event_logs', 'org_invitation_tracking', 'user_sessions_tracking')
ORDER BY tablename, policyname;

-- =============================================
-- 5. VERIFY FUNCTIONS CREATED
-- =============================================

SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('log_webhook_event', 'track_org_invitation', 'track_user_session')
ORDER BY routine_name;

-- =============================================
-- 6. TEST DATA INSERTION (OPTIONAL)
-- =============================================

-- Test webhook event log insertion
INSERT INTO webhook_event_logs (
  event_type, 
  processing_status,
  event_data
) VALUES (
  'migration.test', 
  'success',
  '{"test": "Migration verification", "timestamp": "' || NOW() || '"}'::jsonb
);

-- Verify insertion worked
SELECT * FROM webhook_event_logs WHERE event_type = 'migration.test';

-- Clean up test data
DELETE FROM webhook_event_logs WHERE event_type = 'migration.test';

-- =============================================
-- SUCCESS CONFIRMATION
-- =============================================

SELECT 
  'Enhanced Webhook System' as component,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'webhook_event_logs') 
    THEN '✅ DEPLOYED' 
    ELSE '❌ FAILED' 
  END as status,
  NOW() as verified_at;
