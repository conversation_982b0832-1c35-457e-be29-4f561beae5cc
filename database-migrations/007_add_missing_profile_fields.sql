-- Migration: Add Missing Basic Profile Fields
-- Description: Adds first_name, last_name, username, and other missing fields from Migration 003 to user_preferences table
-- This fixes the issue where Migration 006 was applied but basic profile fields are missing

-- Add missing basic profile fields
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS first_name TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS last_name TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS username TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS display_name TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS is_org_member BOOLEAN DEFAULT false;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS theme TEXT DEFAULT 'system';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS process_context TEXT DEFAULT 'both';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS account_tier TEXT DEFAULT 'free';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'active';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS features_enabled JSONB DEFAULT '{}';

-- Add constraints for data integrity
ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_first_name_length 
  CHECK (first_name IS NULL OR (length(trim(first_name)) >= 2 AND length(trim(first_name)) <= 50));

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_last_name_length 
  CHECK (last_name IS NULL OR (length(trim(last_name)) >= 2 AND length(trim(last_name)) <= 50));

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_username_format 
  CHECK (username IS NULL OR (
    length(username) >= 3 AND 
    length(username) <= 30 AND 
    username ~ '^[a-zA-Z0-9._-]+$'
  ));

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_display_name_length 
  CHECK (display_name IS NULL OR length(display_name) <= 100);

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_theme_values 
  CHECK (theme IN ('light', 'dark', 'system'));

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_process_context_values 
  CHECK (process_context IN ('recruitment', 'bench_sales', 'both'));

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_account_tier_values 
  CHECK (account_tier IN ('free', 'pro', 'team', 'enterprise'));

ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_subscription_status_values 
  CHECK (subscription_status IN ('active', 'canceled', 'past_due', 'unpaid'));

-- Create unique constraint on username (allow NULL for now)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_preferences_username_unique 
  ON user_preferences(username) WHERE username IS NOT NULL;

-- Add helpful indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_first_last_name 
  ON user_preferences(first_name, last_name);

CREATE INDEX IF NOT EXISTS idx_user_preferences_username_lower 
  ON user_preferences(lower(username)) WHERE username IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_user_preferences_account_tier 
  ON user_preferences(account_tier, subscription_status);

-- Update existing profile completeness calculation function to include new fields
CREATE OR REPLACE FUNCTION calculate_profile_completeness(
  p_first_name TEXT,
  p_last_name TEXT,
  p_username TEXT,
  p_bio TEXT,
  p_job_title TEXT,
  p_phone TEXT,
  p_linkedin_url TEXT,
  p_website_url TEXT,
  p_location TEXT,
  p_profile_picture_url TEXT
)
RETURNS INTEGER AS $$
DECLARE
  completeness INTEGER := 0;
  required_fields INTEGER := 3; -- first_name, last_name, username are required
  optional_fields INTEGER := 7; -- bio, job_title, phone, linkedin_url, website_url, location, profile_picture_url
  total_fields INTEGER := required_fields + optional_fields;
  filled_fields INTEGER := 0;
BEGIN
  -- Count required fields (these should always be filled)
  IF p_first_name IS NOT NULL AND LENGTH(trim(p_first_name)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_last_name IS NOT NULL AND LENGTH(trim(p_last_name)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_username IS NOT NULL AND LENGTH(trim(p_username)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  -- Count optional fields
  IF p_bio IS NOT NULL AND LENGTH(trim(p_bio)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_job_title IS NOT NULL AND LENGTH(trim(p_job_title)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_phone IS NOT NULL AND LENGTH(trim(p_phone)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_linkedin_url IS NOT NULL AND LENGTH(trim(p_linkedin_url)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_website_url IS NOT NULL AND LENGTH(trim(p_website_url)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_location IS NOT NULL AND LENGTH(trim(p_location)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_profile_picture_url IS NOT NULL AND LENGTH(trim(p_profile_picture_url)) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  -- Calculate percentage
  completeness := ROUND((filled_fields::DECIMAL / total_fields::DECIMAL) * 100);
  
  RETURN completeness;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger for profile completeness updates  
DROP TRIGGER IF EXISTS trigger_update_profile_completeness ON user_preferences;
CREATE TRIGGER trigger_update_profile_completeness
  BEFORE INSERT OR UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_profile_completeness();

-- Add utility functions for username management
CREATE OR REPLACE FUNCTION is_username_available(check_username TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT EXISTS (
        SELECT 1 FROM user_preferences 
        WHERE lower(username) = lower(check_username)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION suggest_username(first_name_param TEXT, last_name_param TEXT)
RETURNS TEXT AS $$
DECLARE
    base_username TEXT;
    suggested_username TEXT;
    counter INTEGER := 0;
BEGIN
    -- Create base username from first.last
    base_username := lower(trim(first_name_param)) || '.' || lower(trim(last_name_param));
    
    -- Remove any invalid characters and replace with dots
    base_username := regexp_replace(base_username, '[^a-zA-Z0-9._-]', '.', 'g');
    
    -- Remove consecutive dots
    base_username := regexp_replace(base_username, '\.+', '.', 'g');
    
    -- Remove leading/trailing dots
    base_username := trim(base_username, '.');
    
    -- Ensure minimum length
    IF length(base_username) < 3 THEN
        base_username := base_username || '1';
    END IF;
    
    -- Check if base username is available
    suggested_username := base_username;
    
    -- If not available, add numbers until we find an available one
    WHILE NOT is_username_available(suggested_username) LOOP
        counter := counter + 1;
        suggested_username := base_username || counter;
        
        -- Prevent infinite loop
        IF counter > 999 THEN
            suggested_username := base_username || extract(epoch from now())::bigint;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN suggested_username;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
