-- Migration: Complete TalentHUB Profile System Integration
-- Description: Adds missing basic profile fields and organization association tables
-- This unifies Migration 003 (basic profile) + Migration 004 (org association) with existing enhanced profile system
-- Date: June 27, 2025

-- =============================================
-- PART 1: ADD MISSING BASIC PROFILE FIELDS TO user_preferences
-- =============================================

-- Add basic profile fields that are missing
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS first_name TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS last_name TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS username TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS display_name TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS is_org_member BOOLEAN DEFAULT false;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS theme TEXT DEFAULT 'system';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS process_context TEXT DEFAULT 'both';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS account_tier TEXT DEFAULT 'free';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'active';
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS features_enabled JSONB DEFAULT '{}';

-- Add constraints (use DO blocks to avoid constraint conflicts)
DO $$
BEGIN
  -- Username constraints
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_username_format') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_username_format 
      CHECK (username IS NULL OR (
        length(username) >= 3 AND 
        length(username) <= 30 AND 
        username ~ '^[a-zA-Z0-9._-]+$'
      ));
  END IF;

  -- Name length constraints
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_first_name_length') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_first_name_length 
      CHECK (first_name IS NULL OR (length(trim(first_name)) >= 2 AND length(trim(first_name)) <= 50));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_last_name_length') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_last_name_length 
      CHECK (last_name IS NULL OR (length(trim(last_name)) >= 2 AND length(trim(last_name)) <= 50));
  END IF;

  -- Enum constraints
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_theme_values') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_theme_values 
      CHECK (theme IN ('light', 'dark', 'system'));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_process_context_values') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_process_context_values 
      CHECK (process_context IN ('recruitment', 'bench_sales', 'both'));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_account_tier_values') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_account_tier_values 
      CHECK (account_tier IN ('free', 'pro', 'team', 'enterprise'));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'chk_subscription_status_values') THEN
    ALTER TABLE user_preferences ADD CONSTRAINT chk_subscription_status_values 
      CHECK (subscription_status IN ('active', 'canceled', 'past_due', 'unpaid'));
  END IF;
END $$;

-- Create unique index on username (allow NULL)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_preferences_username_unique 
  ON user_preferences(username) WHERE username IS NOT NULL;

-- Add helpful indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_first_last_name 
  ON user_preferences(first_name, last_name) WHERE first_name IS NOT NULL AND last_name IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_user_preferences_account_info 
  ON user_preferences(account_tier, subscription_status);

-- =============================================
-- PART 2: ORGANIZATION ASSOCIATION TABLES (Migration 004)
-- =============================================

-- Organization domains table (for domain-based organization detection)
CREATE TABLE IF NOT EXISTS org_domains (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  org_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  domain TEXT UNIQUE NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  auto_join_enabled BOOLEAN DEFAULT true,
  verification_method TEXT CHECK (verification_method IN ('dns', 'email', 'file')),
  verification_token TEXT,
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by TEXT, -- Clerk user ID
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organization join requests table
CREATE TABLE IF NOT EXISTS org_join_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL, -- Clerk user ID
  org_id UUID REFERENCES organizations(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  domain TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
  request_type TEXT NOT NULL DEFAULT 'domain_detection' CHECK (request_type IN ('domain_detection', 'manual_request', 'invitation')),
  message TEXT,
  
  -- Admin response
  reviewed_by TEXT, -- Clerk user ID of admin who reviewed
  reviewed_at TIMESTAMP WITH TIME ZONE,
  admin_notes TEXT,
  
  -- Automatic expiration
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organization membership audit table
CREATE TABLE IF NOT EXISTS org_membership_audit (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL, -- Clerk user ID
  org_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
  action TEXT NOT NULL CHECK (action IN ('joined', 'left', 'promoted', 'demoted', 'removed')),
  previous_status TEXT,
  new_status TEXT,
  
  -- Context information
  triggered_by TEXT, -- Clerk user ID who triggered the change
  trigger_type TEXT NOT NULL CHECK (trigger_type IN ('user_request', 'admin_action', 'auto_join', 'domain_verification')),
  details JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PART 3: ENABLE RLS AND CREATE POLICIES
-- =============================================

-- Enable RLS for new tables
ALTER TABLE org_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE org_join_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE org_membership_audit ENABLE ROW LEVEL SECURITY;

-- RLS Policies for org_domains
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view domains for their organization" ON org_domains;
  DROP POLICY IF EXISTS "Org admins can manage domains" ON org_domains;
  
  -- Create new policies
  CREATE POLICY "Users can view domains for their organization" ON org_domains
    FOR SELECT USING (
      org_id IN (
        SELECT id FROM organizations WHERE clerk_org_id IN (
          SELECT DISTINCT jsonb_array_elements_text(
            (auth.jwt() -> 'org_memberships')::jsonb
          )
        )
      )
    );

  CREATE POLICY "Org admins can manage domains" ON org_domains
    FOR ALL USING (
      org_id IN (
        SELECT id FROM organizations WHERE clerk_org_id IN (
          SELECT DISTINCT jsonb_array_elements_text(
            (auth.jwt() -> 'org_memberships')::jsonb
          )
        )
      )
    );
END $$;

-- RLS Policies for org_join_requests
DO $$
BEGIN
  DROP POLICY IF EXISTS "Users can view their own join requests" ON org_join_requests;
  DROP POLICY IF EXISTS "Users can create their own join requests" ON org_join_requests;
  DROP POLICY IF EXISTS "Org admins can view and manage requests for their org" ON org_join_requests;
  
  CREATE POLICY "Users can view their own join requests" ON org_join_requests
    FOR SELECT USING (
      user_id = (auth.jwt() -> 'sub')::text
    );

  CREATE POLICY "Users can create their own join requests" ON org_join_requests
    FOR INSERT WITH CHECK (
      user_id = (auth.jwt() -> 'sub')::text
    );

  CREATE POLICY "Org admins can view and manage requests for their org" ON org_join_requests
    FOR ALL USING (
      org_id IN (
        SELECT id FROM organizations WHERE clerk_org_id IN (
          SELECT DISTINCT jsonb_array_elements_text(
            (auth.jwt() -> 'org_memberships')::jsonb
          )
        )
      )
    );
END $$;

-- RLS Policies for org_membership_audit
DO $$
BEGIN
  DROP POLICY IF EXISTS "Users can view their own membership audit" ON org_membership_audit;
  DROP POLICY IF EXISTS "Org admins can view audit for their org" ON org_membership_audit;
  
  CREATE POLICY "Users can view their own membership audit" ON org_membership_audit
    FOR SELECT USING (
      user_id = (auth.jwt() -> 'sub')::text
    );

  CREATE POLICY "Org admins can view audit for their org" ON org_membership_audit
    FOR SELECT USING (
      org_id IN (
        SELECT id FROM organizations WHERE clerk_org_id IN (
          SELECT DISTINCT jsonb_array_elements_text(
            (auth.jwt() -> 'org_memberships')::jsonb
          )
        )
      )
    );
END $$;

-- =============================================
-- PART 4: CREATE INDEXES AND TRIGGERS
-- =============================================

-- Create indexes for org association tables
CREATE INDEX IF NOT EXISTS idx_org_domains_org_id ON org_domains(org_id);
CREATE INDEX IF NOT EXISTS idx_org_domains_domain ON org_domains(domain);
CREATE INDEX IF NOT EXISTS idx_org_join_requests_user_id ON org_join_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_org_join_requests_org_id ON org_join_requests(org_id);
CREATE INDEX IF NOT EXISTS idx_org_join_requests_status ON org_join_requests(status);
CREATE INDEX IF NOT EXISTS idx_org_membership_audit_user_id ON org_membership_audit(user_id);
CREATE INDEX IF NOT EXISTS idx_org_membership_audit_org_id ON org_membership_audit(org_id);

-- Add updated_at triggers for new tables
CREATE TRIGGER update_org_domains_updated_at
  BEFORE UPDATE ON org_domains
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER update_org_join_requests_updated_at
  BEFORE UPDATE ON org_join_requests
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- =============================================
-- PART 5: UTILITY FUNCTIONS
-- =============================================

-- Username management functions
CREATE OR REPLACE FUNCTION is_username_available(check_username TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT EXISTS (
        SELECT 1 FROM user_preferences 
        WHERE lower(username) = lower(check_username)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION suggest_username(first_name_param TEXT, last_name_param TEXT)
RETURNS TEXT AS $$
DECLARE
    base_username TEXT;
    suggested_username TEXT;
    counter INTEGER := 0;
BEGIN
    -- Create base username from first.last
    base_username := lower(trim(first_name_param)) || '.' || lower(trim(last_name_param));
    
    -- Remove any invalid characters and replace with dots
    base_username := regexp_replace(base_username, '[^a-zA-Z0-9._-]', '.', 'g');
    
    -- Remove consecutive dots and trim
    base_username := trim(regexp_replace(base_username, '\.+', '.', 'g'), '.');
    
    -- Ensure minimum length
    IF length(base_username) < 3 THEN
        base_username := base_username || '1';
    END IF;
    
    suggested_username := base_username;
    
    -- Find available username
    WHILE NOT is_username_available(suggested_username) LOOP
        counter := counter + 1;
        suggested_username := base_username || counter;
        
        -- Prevent infinite loop
        IF counter > 999 THEN
            suggested_username := base_username || extract(epoch from now())::bigint;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN suggested_username;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organization detection function
CREATE OR REPLACE FUNCTION detect_org_by_domain(email_domain TEXT)
RETURNS TABLE (
  org_id UUID,
  org_name TEXT,
  auto_join_enabled BOOLEAN,
  has_pending_request BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    o.id as org_id,
    o.name as org_name,
    od.auto_join_enabled,
    EXISTS(
      SELECT 1 FROM org_join_requests ojr 
      WHERE ojr.org_id = o.id 
        AND ojr.user_id = (auth.jwt() -> 'sub')::text
        AND ojr.status = 'pending'
    ) as has_pending_request
  FROM organizations o
  JOIN org_domains od ON od.org_id = o.id
  WHERE od.domain = email_domain 
    AND od.is_verified = true
    AND o.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Join request creation function
CREATE OR REPLACE FUNCTION create_join_request(
  p_user_id TEXT,
  p_org_id UUID,
  p_email TEXT,
  p_domain TEXT,
  p_request_type TEXT DEFAULT 'domain_detection',
  p_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  request_id UUID;
  existing_request UUID;
BEGIN
  -- Check for existing pending request
  SELECT id INTO existing_request
  FROM org_join_requests
  WHERE user_id = p_user_id 
    AND org_id = p_org_id 
    AND status = 'pending';
  
  IF existing_request IS NOT NULL THEN
    -- Update existing request
    UPDATE org_join_requests 
    SET 
      email = p_email,
      domain = p_domain,
      request_type = p_request_type,
      message = p_message,
      expires_at = NOW() + INTERVAL '7 days',
      updated_at = NOW()
    WHERE id = existing_request;
    
    RETURN existing_request;
  END IF;
  
  -- Create new join request
  INSERT INTO org_join_requests (
    user_id, org_id, email, domain, request_type, message
  ) VALUES (
    p_user_id, p_org_id, p_email, p_domain, p_request_type, p_message
  ) RETURNING id INTO request_id;
  
  RETURN request_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
