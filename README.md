   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

4. **Database operations:**
   ```bash
   # Test database connection
   curl -X GET http://localhost:3000/api/db-test
   
   # Set up database schema (if needed)
   curl -X POST http://localhost:3000/api/db-test
   
   # Access Supabase dashboard
   # Visit: https://supabase.com/dashboard
   ```

## 📋 Development Roadmap

### Phase 1: Foundation ✅
- [x] Next.js 15 project setup
- [x] shadcn/ui (New York style) integration
- [x] TypeScript configuration
- [x] Tailwind CSS setup
- [x] Basic project structure

### Phase 2: Authentication & Database ✅
- [x] Clerk authentication setup
- [x] NileDB multi-tenant database
- [x] Drizzle ORM configuration
- [x] Organization schema design
- [x] User role system foundation

### Phase 3: Organization & User Management ✅
- [x] Timezone management system (Production-ready)
- [x] Clerk + Supabase RLS integration
- [x] User preferences with database storage
- [x] Organization association and detection
- [x] Join request workflows and approvals
- [x] Multi-tenant data isolation

### Phase 3.5: Enterprise Tier-Gated Access ✅
- [x] Tier-based organization access control
- [x] Revenue-driven upgrade workflows
- [x] Professional billing integration UI
- [x] Admin access control and onboarding
- [x] Cost protection and usage limits

### Phase 4: Core Business Features 📋
- [ ] Job posting management system
- [ ] Candidate profile and matching
- [ ] Client/vendor relationship management
- [ ] Document upload and management
- [ ] Dashboard analytics and reporting

### Phase 4: Advanced Features 📈
- [ ] Real-time notifications
- [ ] Document management (Cloudflare R2)
- [ ] Analytics and reporting
- [ ] Email integrations
- [ ] API endpoints

### Phase 5: Production 🚀
- [ ] Cloudflare deployment
- [ ] Performance optimization
- [ ] Security hardening
- [ ] Monitoring and logging

## 🎨 UI/UX Guidelines

### Design System
- **Style:** shadcn/ui New York style
- **Colors:** Professional B2B palette
- **Typography:** Inter font family
- **Spacing:** Consistent 8px grid
- **Components:** Modular and reusable

### Key Principles
- **Information density** for B2B users
- **Professional appearance** 
- **Accessible design** (WCAG compliant)
- **Responsive layouts** 
- **Loading states** and error handling

## 🗂️ Database Schema (Planned)

### Core Tables
```sql
-- Organizations (tenants)
organizations (id, name, type, settings)

-- Users with process-specific roles
user_process_roles (user_id, org_id, process_type, role)

-- Recruitment process
recruitment_jobs (id, org_id, client_id, vendor_id, status)
candidates (id, org_id, personal_info, skills, availability)

-- Bench sales process  
bench_profiles (id, org_id, supplier_id, vendor_id, candidate_id)
client_requirements (id, org_id, skills_needed, budget, timeline)
```

## 🔒 Security Features

- **Multi-tenant data isolation**
- **Row-level security (RLS)**
- **Role-based access control**
- **JWT-based authentication**
- **HTTPS everywhere**
- **Input validation and sanitization**

## 🚀 Performance Optimizations

- **Next.js Server Components**
- **Static generation where possible**
- **Image optimization**
- **Code splitting**
- **Edge deployment (Cloudflare)**
- **Database query optimization**

## 📱 Responsive Design

- **Mobile-first approach**
- **Tablet-optimized layouts**
- **Desktop-focused workflows**
- **Touch-friendly interactions**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is private and proprietary.

---

**Built with ❤️ using modern web technologies**

For questions or support, please contact the development team.
