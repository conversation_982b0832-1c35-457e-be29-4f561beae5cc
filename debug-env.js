/**
 * Debug script to check environment variables and webhook setup
 */

console.log('=== ENVIRONMENT ANALYSIS ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('CLERK_WEBHOOK_SECRET exists:', !!process.env.CLERK_WEBHOOK_SECRET);
console.log('CLERK_WEBHOOK_SECRET length:', process.env.CLERK_WEBHOOK_SECRET?.length);
console.log('CLERK_WEBHOOK_SECRET starts with whsec_:', process.env.CLERK_WEBHOOK_SECRET?.startsWith('whsec_'));

console.log('\n=== SUPABASE VARIABLES ===');
console.log('NEXT_PUBLIC_SUPABASE_URL exists:', !!process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('SUPABASE_SERVICE_ROLE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

console.log('\n=== CLERK VARIABLES ===');
console.log('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY exists:', !!process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY);
console.log('CLERK_SECRET_KEY exists:', !!process.env.CLERK_SECRET_KEY);
