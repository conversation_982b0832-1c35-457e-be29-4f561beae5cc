# TalentHUB Design System & Timezone Management

## Color Strategy: Apple Blue Unified

### Design Philosophy
We've chosen **Apple Blue (#007AFF)** as our primary color for both recruitment and bench sales workflows. This creates a unified, trustworthy brand experience while leveraging color psychology proven by millions of Apple users.

## 🌍 Timezone Management System

### Enterprise-Grade Multi-Timezone Support
TalentHUB includes comprehensive timezone management designed specifically for global B2B SaaS operations:

**Key Features:**
- **UTC Storage:** All timestamps stored in PostgreSQL with `TIMESTAMP WITH TIME ZONE`
- **User Preferences:** IANA timezone names with Clerk integration
- **Organization Policies:** Admin control over timezone standards
- **Auto-Detection:** Browser timezone detection for seamless onboarding
- **Visual Clarity:** Always-visible timezone indicators prevent confusion

**Libraries Used:**
- **@formkit/tempo:** Modern, lightweight timezone handling (replaces Moment.js)
- **PostgreSQL:** Built-in timezone validation and DST handling
- **Clerk:** User preference storage in metadata

### Implementation Files
```
src/lib/timezone.ts              # Core timezone utilities
src/lib/user-timezone.ts         # Clerk integration
src/lib/database-timezone.ts     # Database helpers
src/components/ui/timezone-*     # UI components
```

## Color Palette

### Primary Colors
```css
--primary: #007AFF;           /* Apple Blue - All workflows */
--primary-hover: #0056CC;     /* Darker variant for interactions */
--primary-light: #CCE7FF;     /* Light variant for backgrounds */
```

### Status Colors
```css
--critical: #DC2626;          /* High-contrast red */
--in-progress: #F59E0B;       /* Amber */
--complete: #059669;          /* Green */
--active: #007AFF;            /* Apple Blue */
--inactive: #6B7280;          /* Muted gray */
```

### Neutral Palette
```css
--background: #FFFFFF;        /* Clean white */
--foreground: #111827;        /* Near black */
--muted: #6B7280;            /* Professional gray */
--border: #E5E7EB;           /* Light gray borders */
```

## Status System

### Status Indicators
- 🔴 **Critical/Urgent:** `#DC2626` - High-contrast red with warning icons
- 🟡 **In Progress:** `#F59E0B` - Amber with progress indicators
- 🟢 **Complete:** `#059669` - Green with checkmark icons
- 🔵 **Active/Open:** `#007AFF` - Apple Blue primary
- ⚪ **Inactive:** `#6B7280` - Muted gray with reduced opacity

### Workflow Identification
Both recruitment and bench sales use Apple Blue (#007AFF) but are differentiated through:
- **Context-specific badges** (Recruitment/Bench Sales labels)
- **Content structure** (different card layouts)
- **Icon systems** (different action icons)
- **Navigation structure** (separate sections)

## Component Usage

### StatusBadge Component
```tsx
<StatusBadge status="active">Active Job</StatusBadge>
<StatusBadge status="critical">Urgent</StatusBadge>
<StatusBadge status="complete">Filled</StatusBadge>
```

### WorkflowCard Component
```tsx
<WorkflowCard
  workflow="recruitment"
  title="Senior Developer"
  status="active"
  priority="high"
  metadata={[
    { label: "Client", value: "TechCorp" },
    { label: "Posted", value: "2 days ago" }
  ]}
/>
```

## Psychological Benefits

### Apple Blue (#007AFF) Benefits
- **User Familiarity:** Recognized as iOS system blue
- **Trust & Reliability:** Associated with professional platforms
- **Tech-Forward:** Suggests modern, cutting-edge software
- **Accessibility:** Designed for optimal contrast and readability

### Status Color Psychology
- **Red:** Creates urgency, demands immediate attention
- **Amber:** Suggests active work, progress in motion
- **Green:** Conveys completion, success, positive outcomes
- **Blue:** Builds trust, represents active and available states
- **Gray:** Indicates inactive/disabled states without negative connotation

## Implementation Guidelines

### CSS Custom Properties
All colors are defined using HSL values in CSS custom properties for easy theme switching and dark mode support.

### Accessibility Standards
- **Text Contrast:** Minimum 4.5:1 ratio (WCAG AA)
- **UI Elements:** Minimum 3:1 contrast ratio
- **Colorblind Support:** All states include icons/text beyond color

### Component Variants
- Each component supports multiple color variants
- Status-specific styling for consistent UX
- Hover and focus states for interactive elements

## Dark Mode Support

### Adjusted Colors
- Primary blue lightened for dark backgrounds
- Status colors adjusted for better contrast
- Neutral colors inverted appropriately
- Maintains brand recognition while ensuring readability

## Future Considerations

### Scalability
- Easy to extend with additional status colors
- Framework supports role-based color customization
- Maintainable through CSS custom properties

### Brand Evolution
- Foundation allows for future brand updates
- Color system can be extended without breaking existing components
- Modular approach supports feature-specific color additions

---

**Last Updated:** June 2025
**Design System Version:** 1.0
**Implementation Status:** ✅ Complete
