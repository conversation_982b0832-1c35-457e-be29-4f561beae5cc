/**
 * TalentHUB Clerk Webhook - Improved Supabase Edge Function
 * Handles Clerk webhook events with enhanced debugging and error handling
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Svix webhook verification - using latest stable version
import { Webhook } from "https://esm.sh/svix@1.24.0"

console.log("🚀 TalentHUB Clerk Webhook Edge Function (Improved) loaded")

serve(async (req: Request) => {
  // CORS headers for preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, svix-id, svix-timestamp, svix-signature',
      },
    })
  }

  if (req.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 })
  }

  const requestId = crypto.randomUUID()
  console.log(`🔄 [${requestId}] Webhook received:`, new Date().toISOString())

  try {
    // Get environment variables with detailed logging
    const WEBHOOK_SECRET = Deno.env.get('CLERK_WEBHOOK_SECRET')
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
    const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    console.log(`🔍 [${requestId}] Environment check:`, {
      hasWebhookSecret: !!WEBHOOK_SECRET,
      webhookSecretLength: WEBHOOK_SECRET?.length,
      webhookSecretPrefix: WEBHOOK_SECRET?.substring(0, 10),
      hasSupabaseUrl: !!SUPABASE_URL,
      hasServiceKey: !!SUPABASE_SERVICE_ROLE_KEY,
    })

    if (!WEBHOOK_SECRET) {
      console.error(`❌ [${requestId}] Missing CLERK_WEBHOOK_SECRET`)
      return new Response('Webhook configuration error', { status: 500 })
    }

    if (!WEBHOOK_SECRET.startsWith('whsec_')) {
      console.error(`❌ [${requestId}] Invalid webhook secret format (should start with whsec_)`)
      return new Response('Invalid webhook secret format', { status: 500 })
    }

    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
      console.error(`❌ [${requestId}] Missing Supabase configuration`)
      return new Response('Database configuration error', { status: 500 })
    }

    // Initialize Supabase client with service role
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Get headers with detailed logging
    const svix_id = req.headers.get('svix-id')
    const svix_timestamp = req.headers.get('svix-timestamp')
    const svix_signature = req.headers.get('svix-signature')
    const userAgent = req.headers.get('user-agent')
    const contentType = req.headers.get('content-type')

    console.log(`🔍 [${requestId}] Headers received:`, { 
      svix_id, 
      svix_timestamp, 
      svix_signature: svix_signature?.substring(0, 20) + '...', 
      userAgent,
      contentType,
      allHeaders: Object.fromEntries(req.headers.entries())
    })

    // Validate headers
    if (!svix_id || !svix_timestamp || !svix_signature) {
      console.error(`❌ [${requestId}] Missing svix headers:`, {
        hasSvixId: !!svix_id,
        hasSvixTimestamp: !!svix_timestamp,
        hasSvixSignature: !!svix_signature
      })
      return new Response('Missing svix headers', { status: 400 })
    }

    // Validate user agent (should be from Svix)
    if (!userAgent?.includes('Svix')) {
      console.warn(`⚠️ [${requestId}] Unexpected user agent:`, userAgent)
    }

    // Get the raw body - CRITICAL: preserve exact bytes for signature verification
    let body: string
    try {
      // Method 1: Try reading as text directly
      body = await req.text()
      console.log(`🔍 [${requestId}] Body read successfully:`, {
        length: body.length,
        preview: body.substring(0, 100) + '...',
        isValidJson: (() => {
          try { JSON.parse(body); return true } catch { return false }
        })()
      })
    } catch (error) {
      console.error(`❌ [${requestId}] Failed to read request body:`, error)
      return new Response('Failed to read request body', { status: 400 })
    }

    // Verify webhook signature with enhanced error handling
    let evt: any
    try {
      console.log(`🔐 [${requestId}] Starting signature verification...`)
      
      const wh = new Webhook(WEBHOOK_SECRET)
      
      evt = wh.verify(body, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature,
      })
      
      console.log(`✅ [${requestId}] Webhook verified successfully:`, {
        eventType: evt.type,
        eventId: evt.data?.id,
        timestamp: new Date(parseInt(svix_timestamp) * 1000).toISOString()
      })
      
    } catch (error: any) {
      console.error(`❌ [${requestId}] Webhook verification failed:`, {
        error: error.message,
        errorType: error.constructor.name,
        stack: error.stack,
        debugInfo: {
          bodyLength: body.length,
          bodyHash: await crypto.subtle.digest('SHA-256', new TextEncoder().encode(body)).then(
            hash => Array.from(new Uint8Array(hash)).map(b => b.toString(16).padStart(2, '0')).join('')
          ),
          webhookSecretLength: WEBHOOK_SECRET.length,
          svixIdLength: svix_id?.length,
          svixTimestampLength: svix_timestamp?.length,
          svixSignatureLength: svix_signature?.length,
          timestampAge: Math.abs(Math.floor(Date.now() / 1000) - parseInt(svix_timestamp)),
        }
      })
      
      return new Response(JSON.stringify({
        error: 'Webhook verification failed',
        requestId,
        details: error.message
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Process the event
    const eventType = evt.type
    console.log(`📝 [${requestId}] Processing event: ${eventType}`)

    // For now, just log the event and return success
    // TODO: Implement actual event processing
    console.log(`📋 [${requestId}] Event data:`, JSON.stringify(evt.data, null, 2))

    return new Response(JSON.stringify({
      success: true,
      requestId,
      eventType,
      message: 'Webhook processed successfully'
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    })

  } catch (error: any) {
    console.error(`💥 [${requestId}] Unexpected error:`, {
      error: error.message,
      stack: error.stack,
      type: error.constructor.name
    })
    
    return new Response(JSON.stringify({
      error: 'Internal server error',
      requestId,
      details: error.message
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500
    })
  }
})

/* Deno.serve(async (req: Request) => {
  // Your existing handler code here
}) */
