/**
 * TalentHUB Clerk Webhook - Supabase Edge Function
 * Handles Clerk webhook events with built-in Supabase integration
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Svix webhook verification
import { Webhook } from "https://esm.sh/svix@1.15.0"

console.log("🚀 TalentHUB Clerk Webhook Edge Function loaded")

serve(async (req) => {
  // CORS headers for preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, svix-id, svix-timestamp, svix-signature',
      },
    })
  }

  if (req.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 })
  }

  console.log('🔄 Webhook received:', new Date().toISOString())

  try {
    // Get environment variables
    const WEBHOOK_SECRET = Deno.env.get('CLERK_WEBHOOK_SECRET')
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
    const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!WEBHOOK_SECRET) {
      console.error('❌ Missing CLERK_WEBHOOK_SECRET')
      return new Response('Webhook configuration error', { status: 500 })
    }

    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
      console.error('❌ Missing Supabase configuration')
      return new Response('Database configuration error', { status: 500 })
    }

    // Initialize Supabase client with service role
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Get headers
    const svix_id = req.headers.get('svix-id')
    const svix_timestamp = req.headers.get('svix-timestamp')
    const svix_signature = req.headers.get('svix-signature')

    console.log('🔍 Headers received:', { 
      svix_id, 
      svix_timestamp, 
      svix_signature: svix_signature?.substring(0, 20) + '...' 
    })

    // Validate headers
    if (!svix_id || !svix_timestamp || !svix_signature) {
      console.error('❌ Missing svix headers')
      return new Response('Missing svix headers', { status: 400 })
    }

    // Get the raw body
    const body = await req.text()
    console.log('🔍 Body length:', body.length)

    // Verify webhook signature
    const wh = new Webhook(WEBHOOK_SECRET)
    let evt

    try {
      evt = wh.verify(body, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature,
      })
      console.log('✅ Webhook verified successfully:', evt.type)
    } catch (err) {
      console.error('❌ Webhook verification failed:', err.message)
      return new Response('Webhook verification failed', { status: 400 })
    }

    // Process the event
    const eventType = evt.type
    console.log(`📝 Processing event: ${eventType}`)

    let result
    switch (eventType) {
      case 'user.created':
        result = await handleUserCreated(supabase, evt.data)
        break
      case 'user.updated':
        result = await handleUserUpdated(supabase, evt.data)
        break
      case 'user.deleted':
        result = await handleUserDeleted(supabase, evt.data)
        break
      case 'organizationMembership.created':
        result = await handleMembershipCreated(supabase, evt.data)
        break
      case 'organizationMembership.deleted':
        result = await handleMembershipDeleted(supabase, evt.data)
        break
      case 'session.ended':
        result = await handleSessionEnded(supabase, evt.data)
        break
      default:
        console.log(`ℹ️ Unhandled event type: ${eventType}`)
        result = { success: true, message: 'Event acknowledged but not processed' }
    }

    if (result.success) {
      console.log('✅ Event processed successfully')
      return new Response(JSON.stringify({
        success: true,
        eventType,
        processed: true,
        timestamp: new Date().toISOString()
      }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      })
    } else {
      console.error('❌ Event processing failed:', result.error)
      return new Response(JSON.stringify({
        success: false,
        error: result.error
      }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500
      })
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500
    })
  }
})

// ===== EVENT HANDLERS =====

async function handleUserCreated(supabase, userData) {
  console.log('👤 Creating user:', userData.id)
  
  try {
    // Get primary email
    const primaryEmail = userData.email_addresses?.find(
      (email) => email.id === userData.primary_email_address_id
    )?.email_address

    // Create username
    const username = `${userData.first_name || 'user'}_${userData.id.slice(-8)}`
    const displayName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim()

    // Insert user preferences
    const { error: userError } = await supabase
      .from('user_preferences')
      .upsert({
        user_id: userData.id,
        first_name: userData.first_name || '',
        last_name: userData.last_name || '',
        username: username,
        display_name: displayName,
        timezone: 'America/New_York',
        theme: 'system',
        account_tier: 'free',
        subscription_status: 'active',
        features_enabled: ['basic'],
        profile_completeness: 25,
        last_active: new Date().toISOString(),
        created_at: new Date(userData.created_at).toISOString(),
        updated_at: new Date().toISOString(),
        org_id: null,
        is_org_member: false,
      }, {
        onConflict: 'user_id'
      })

    if (userError) {
      console.error('❌ Database error (user_preferences):', userError)
      return { success: false, error: userError.message }
    }

    // Log activity
    const { error: activityError } = await supabase
      .from('user_activity_logs')
      .insert({
        user_id: userData.id,
        activity_type: 'user_created',
        activity_data: {
          email: primaryEmail,
          signup_method: 'clerk_auth',
          webhook_source: 'clerk_edge_function'
        },
        created_at: new Date().toISOString()
      })

    if (activityError) {
      console.error('❌ Activity log error:', activityError)
      // Don't fail the webhook for activity log errors
    }

    console.log('✅ User created successfully:', userData.id)
    return { success: true, userId: userData.id, username, email: primaryEmail }

  } catch (error) {
    console.error('❌ Error in handleUserCreated:', error)
    return { success: false, error: error.message }
  }
}

async function handleUserUpdated(supabase, userData) {
  console.log('👤 Updating user:', userData.id)
  
  try {
    const displayName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim()

    const { error } = await supabase
      .from('user_preferences')
      .update({
        first_name: userData.first_name || '',
        last_name: userData.last_name || '',
        display_name: displayName,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userData.id)

    if (error) {
      console.error('❌ Database error:', error)
      return { success: false, error: error.message }
    }

    console.log('✅ User updated successfully:', userData.id)
    return { success: true, userId: userData.id }

  } catch (error) {
    console.error('❌ Error in handleUserUpdated:', error)
    return { success: false, error: error.message }
  }
}

async function handleUserDeleted(supabase, userData) {
  console.log('👤 Deleting user:', userData.id)
  
  try {
    const { error } = await supabase
      .from('user_preferences')
      .update({
        is_deleted: true,
        deleted_at: new Date(userData.deleted_at).toISOString(),
        first_name: '[DELETED]',
        last_name: '[USER]',
        display_name: '[DELETED USER]',
        username: `deleted_${userData.id.slice(-8)}`,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userData.id)

    if (error) {
      console.error('❌ Database error:', error)
      return { success: false, error: error.message }
    }

    console.log('✅ User deleted successfully:', userData.id)
    return { success: true, userId: userData.id }

  } catch (error) {
    console.error('❌ Error in handleUserDeleted:', error)
    return { success: false, error: error.message }
  }
}

async function handleMembershipCreated(supabase, membershipData) {
  console.log('🏢 Processing organizationMembership.created')
  
  // Log the membership activity
  const userId = membershipData.public_user_data?.user_id
  if (userId) {
    await supabase
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        activity_type: 'org_membership_created',
        activity_data: {
          organization_id: membershipData.organization?.id,
          role: membershipData.role,
          webhook_source: 'clerk_edge_function'
        },
        created_at: new Date().toISOString()
      })
  }

  return { success: true, message: 'Membership created' }
}

async function handleMembershipDeleted(supabase, membershipData) {
  console.log('🏢 Processing organizationMembership.deleted')
  
  // Log the membership activity
  const userId = membershipData.public_user_data?.user_id
  if (userId) {
    await supabase
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        activity_type: 'org_membership_deleted',
        activity_data: {
          organization_id: membershipData.organization?.id,
          role: membershipData.role,
          webhook_source: 'clerk_edge_function'
        },
        created_at: new Date().toISOString()
      })
  }

  return { success: true, message: 'Membership deleted' }
}

async function handleSessionEnded(supabase, sessionData) {
  console.log('🔐 Processing session.ended:', sessionData.user_id)
  
  try {
    // Update last active timestamp
    const { error } = await supabase
      .from('user_preferences')
      .update({
        last_active: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', sessionData.user_id)

    if (error) {
      console.error('❌ Error updating last active:', error)
    }

    // Log session activity
    await supabase
      .from('user_activity_logs')
      .insert({
        user_id: sessionData.user_id,
        activity_type: 'session_ended',
        activity_data: {
          session_id: sessionData.id,
          ended_at: sessionData.ended_at,
          webhook_source: 'clerk_edge_function'
        },
        created_at: new Date().toISOString()
      })

    console.log('✅ Session ended processed:', sessionData.user_id)
    return { success: true, userId: sessionData.user_id }

  } catch (error) {
    console.error('❌ Error in handleSessionEnded:', error)
    return { success: false, error: error.message }
  }
}
