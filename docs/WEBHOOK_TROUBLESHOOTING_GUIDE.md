# 🔧 TalentHUB Webhook Troubleshooting Guide

## 🚨 Common Issue: "Webhook verification failed: No matching signature found"

This guide addresses the specific Clerk webhook signature verification issue with Supabase Edge Functions.

## 🔍 Root Cause Analysis

The "No matching signature found" error typically occurs due to:

1. **Body Reading Issues**: Edge Function reading body incorrectly
2. **Environment Variable Problems**: Webhook secret not properly configured
3. **Header Format Issues**: Svix headers not being passed correctly
4. **Library Version Conflicts**: Incompatible Svix library version
5. **Timestamp Issues**: Webhook timestamp too old or invalid

## 🛠️ Step-by-Step Fix

### Step 1: Verify Environment Variables

Check your Supabase Dashboard > Settings > Edge Functions > Environment Variables:

```env
CLERK_WEBHOOK_SECRET=whsec_your_actual_secret_here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

**Critical**: The webhook secret MUST start with `whsec_` and match exactly what's in your Clerk Dashboard.

### Step 2: Update Edge Function Code

Replace your current `supabase/functions/clerk-webhook/index.ts` with the improved version:

```typescript
// Use the improved version in index-improved.ts
// Key changes:
// 1. Updated Svix library to latest version
// 2. Better body reading (preserve exact bytes)
// 3. Enhanced error logging
// 4. Proper environment variable validation
```

### Step 3: Deploy the Updated Function

```bash
# Deploy the improved function
supabase functions deploy clerk-webhook

# Check deployment status
supabase functions list
```

### Step 4: Test the Webhook

Use the debug script to test locally:

```bash
# Run the debug script
npx ts-node scripts/debug-webhook-verification.ts
```

## 🧪 Testing Checklist

### Environment Variables ✅
- [ ] `CLERK_WEBHOOK_SECRET` exists in Supabase Dashboard
- [ ] Secret starts with `whsec_`
- [ ] Secret matches Clerk Dashboard exactly
- [ ] `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY` are set

### Clerk Configuration ✅
- [ ] Webhook endpoint URL is correct
- [ ] Webhook is enabled in Clerk Dashboard
- [ ] Required events are selected (user.created, user.updated, etc.)
- [ ] Endpoint URL uses HTTPS (required for production)

### Edge Function Configuration ✅
- [ ] JWT verification is disabled (`verify_jwt: false` in config.json)
- [ ] Function is deployed successfully
- [ ] Function logs show no deployment errors

### Network & Headers ✅
- [ ] Webhook endpoint is publicly accessible
- [ ] Svix headers are being received (svix-id, svix-timestamp, svix-signature)
- [ ] User-Agent contains "Svix"
- [ ] Content-Type is "application/json"

## 🔍 Debugging Commands

### Check Function Logs
```bash
# View real-time logs
supabase functions logs clerk-webhook --follow

# View recent logs
supabase functions logs clerk-webhook
```

### Test Webhook Endpoint
```bash
# Test endpoint accessibility
curl -X POST https://your-project.supabase.co/functions/v1/clerk-webhook \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Should return 400 (missing svix headers) - this means endpoint is reachable
```

### Verify Environment Variables
```bash
# Check if variables are set (from Supabase CLI)
supabase secrets list
```

## 🚨 Common Mistakes

### 1. Wrong Webhook Secret Format
```bash
# ❌ Wrong
CLERK_WEBHOOK_SECRET=abc123...

# ✅ Correct  
CLERK_WEBHOOK_SECRET=whsec_abc123...
```

### 2. Body Reading Issues
```typescript
// ❌ Wrong - parsing JSON first
const body = await req.json()
const bodyString = JSON.stringify(body)

// ✅ Correct - read as raw text
const body = await req.text()
```

### 3. Missing Headers Validation
```typescript
// ❌ Wrong - not checking headers
const evt = wh.verify(body, headers)

// ✅ Correct - validate headers first
if (!svix_id || !svix_timestamp || !svix_signature) {
  return new Response('Missing headers', { status: 400 })
}
```

## 🔧 Advanced Debugging

### Enable Detailed Logging
Add this to your Edge Function for maximum debugging:

```typescript
console.log('🔍 Debug Info:', {
  bodyLength: body.length,
  bodyHash: await crypto.subtle.digest('SHA-256', new TextEncoder().encode(body)),
  webhookSecretLength: WEBHOOK_SECRET?.length,
  timestampAge: Math.abs(Math.floor(Date.now() / 1000) - parseInt(svix_timestamp)),
  headers: Object.fromEntries(req.headers.entries())
})
```

### Test with Sample Data
Use the debug script to generate valid test headers:

```bash
node scripts/debug-webhook-verification.ts
```

## 📞 Getting Help

If you're still experiencing issues:

1. **Check the improved Edge Function**: Use `index-improved.ts` for better debugging
2. **Review logs**: Look for specific error messages in function logs
3. **Verify Clerk setup**: Double-check webhook configuration in Clerk Dashboard
4. **Test locally**: Use the debug script to isolate the issue

## 🔗 Useful Resources

- [Clerk Webhook Documentation](https://clerk.com/docs/webhooks/sync-data)
- [Svix Verification Guide](https://docs.svix.com/receiving/verifying-payloads)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [TalentHUB Webhook Implementation](./ENHANCED_WEBHOOK_IMPLEMENTATION_GUIDE.md)

## 📋 Quick Fix Summary

1. ✅ Update webhook secret in Supabase Dashboard
2. ✅ Use improved Edge Function code
3. ✅ Deploy updated function
4. ✅ Test with debug script
5. ✅ Monitor function logs for verification success
