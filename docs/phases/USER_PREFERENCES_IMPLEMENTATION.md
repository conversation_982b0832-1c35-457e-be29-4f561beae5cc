# User Preferences System Implementation Plan

## 📋 Overview

**Phase Goal:** Implement enterprise-grade user preferences system with global username uniqueness, org association, and Clerk subscription integration.

**Status:** 🚧 In Planning  
**Priority:** High  
**Estimated Duration:** 4-6 weeks  
**Complexity:** Medium-High  

## 🏗️ Architecture Decisions

### Final Approved Decisions:
- ✅ **Username Strategy:** Global uniqueness + hybrid suggestion (first.last)
- ✅ **Name Requirements:** First Name + Last Name mandatory
- ✅ **Org Display:** Auto-detect domain + "Join Organization" option
- ✅ **Account Types:** Free, Pro (Individual), Team, Enterprise (Org)
- ✅ **Theme:** User-level preference with system default
- ✅ **Timezone:** Individual choice with smart defaults
- ✅ **Data Sync:** Hybrid approach (critical immediate, preferences batched)

## 📊 Implementation Phases

### **Phase 1: Database Foundation** (Week 1-2)
**Complexity:** Medium

#### Database Schema:
```sql
-- user_preferences table (globally unique usernames)
-- org_domains table (domain-based org mapping)
-- Enhanced RLS policies for multi-tenant access
```

#### Components:
- Migration files with proper rollback
- RLS policies for data isolation
- Username validation service
- Domain detection utility

#### Deliverables:
- `migrations/001_user_preferences.sql`
- `migrations/002_org_domains.sql`
- `lib/username-validation.ts`
- `lib/domain-detection.ts`

### **Phase 2: Preferences Modal UI** (Week 2-3)
**Complexity:** Low-Medium

#### UI Components:
- Custom modal (Clerk-style design)
- Form sections: Profile, Display, Account
- Real-time validation feedback
- Theme preview functionality

#### Integration:
- Modal trigger (cog button placement)
- Form state management
- Error handling patterns
- Success notifications

#### Deliverables:
- `components/preferences/PreferencesModal.tsx`
- `components/preferences/ProfileSection.tsx`
- `components/preferences/DisplaySection.tsx`
- `components/preferences/AccountSection.tsx`

### **Phase 3: Org Association Logic** (Week 3-4)
**Complexity:** High

#### Core Features:
- Domain-based org detection
- Join organization workflow
- Individual to org transition
- Admin approval system

#### UX Flows:
- Org detection notifications
- Join request modals
- Pending invitation management
- Data migration wizards

#### Deliverables:
- `lib/org-association.ts`
- `components/org/JoinOrganizationModal.tsx`
- `components/org/PendingInvitations.tsx`
- `hooks/useOrgDetection.ts`

### **Phase 4: Subscription Integration** (Week 4-5)
**Complexity:** Low

#### Clerk Billing:
- Individual Pro plan setup
- Billing portal integration
- Feature gating system
- Usage tracking

#### Components:
- Upgrade prompts
- Subscription status display
- Feature comparison table
- Billing history access

#### Deliverables:
- `lib/subscription.ts`
- `components/billing/UpgradeModal.tsx`
- `components/billing/FeatureGate.tsx`
- `hooks/useSubscription.ts`

### **Phase 5: Data Sync & Polish** (Week 5-6)
**Complexity:** Medium

#### Sync Strategy:
- Critical data: Immediate sync
- Preferences: Batched sync (30s)
- Retry logic for failures
- Conflict resolution

#### Polish Features:
- Loading states
- Error boundaries
- Cross-browser testing
- Performance optimization

#### Deliverables:
- `lib/data-sync.ts`
- `components/ui/LoadingStates.tsx`
- `lib/error-handling.ts`
- Comprehensive test suite

## 🗄️ Database Schema Design

### Core Tables:

```sql
-- Enhanced user_preferences table
user_preferences (
  id UUID PRIMARY KEY,
  user_id TEXT UNIQUE NOT NULL,           -- Clerk user ID
  org_id UUID REFERENCES organizations(id), -- Domain or formal org
  is_org_member BOOLEAN DEFAULT false,     -- Formal vs domain-based
  
  -- Identity (all required)
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL, 
  username TEXT UNIQUE NOT NULL,           -- Globally unique
  display_name TEXT,                       -- Optional title
  
  -- Preferences
  timezone TEXT DEFAULT 'America/Chicago',
  theme TEXT DEFAULT 'system',
  process_context TEXT CHECK (process_context IN ('recruitment', 'bench_sales', 'both')),
  
  -- Account
  account_tier TEXT DEFAULT 'free',
  subscription_status TEXT DEFAULT 'active',
  
  -- Audit
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organization domain mapping
org_domains (
  id UUID PRIMARY KEY,
  org_id UUID REFERENCES organizations(id),
  domain TEXT UNIQUE NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  auto_join_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🎨 UI/UX Specifications

### Modal Design:
- **Style:** Mirror Clerk's professional modal design
- **Trigger:** Cog/wheel button above profile button in sidebar
- **Background:** Blurred backdrop with proper focus management
- **Responsive:** Mobile-friendly with touch interactions

### Form Sections:
1. **Profile Information**
   - First Name (required)
   - Last Name (required)
   - Username (globally unique, real-time validation)
   - Display Name (optional professional title)

2. **Display Preferences**
   - Theme: Light / Dark / System (with preview)
   - Timezone: Smart defaults with search
   - Language: Future enhancement

3. **Account Information**
   - Account Type (read-only with upgrade options)
   - Organization Status (if applicable)
   - Process Context (recruitment/bench sales)

### Validation Rules:
- **Username:** 3-30 characters, alphanumeric + dots/hyphens
- **Names:** 2-50 characters, letters and spaces only
- **Real-time feedback:** Immediate validation with clear error messages

## 🔐 Security & Compliance

### Data Protection:
- **RLS Policies:** Strict tenant isolation
- **Input Validation:** Server-side sanitization
- **Audit Trails:** Complete change logging
- **GDPR Compliance:** Right to be forgotten support

### Access Control:
- **Individual Users:** Own data only
- **Org Members:** Role-based org access
- **Admins:** User management capabilities
- **System:** Automated domain detection

## 🚀 Feature Gating Strategy

### Account Tiers:

| Feature | Free | Pro | Team | Enterprise |
|---------|------|-----|------|------------|
| **Records** | 50 | 500 | Unlimited | Unlimited |
| **Public Profile** | ❌ | ✅ | ✅ | ✅ |
| **API Access** | ❌ | Limited | Full | Full |
| **Custom Fields** | 3 | 10 | Unlimited | Unlimited |
| **Integrations** | ❌ | 2 | 5 | Unlimited |
| **Advanced Search** | ❌ | ✅ | ✅ | ✅ |

## 📝 Testing Strategy

### Unit Tests:
- Username validation logic
- Domain detection algorithms
- Data sync operations
- Form validation rules

### Integration Tests:
- Clerk webhook handling
- Supabase RLS policies
- Modal interactions
- Subscription flows

### User Acceptance Tests:
- Complete onboarding flow
- Org association workflow
- Subscription upgrade process
- Cross-browser compatibility

## 🎯 Success Metrics

### Technical KPIs:
- **Modal Load Time:** <200ms
- **Username Validation:** <100ms
- **Sync Operations:** <500ms
- **Error Rate:** <1% for core operations

### Business KPIs:
- **Onboarding Completion:** 90% through preferences setup
- **Org Association:** 70% of business users join existing orgs
- **Pro Conversion:** 15% individual users upgrade within 30 days
- **User Satisfaction:** >4.5/5 for preferences experience

## 🔄 Next Session Requirements

### Prerequisites:
- Supabase dashboard access (confirmed: project visible)
- Database migration approval process
- Clerk billing configuration permissions
- UI component design approval

### Implementation Readiness:
- ✅ Database schema finalized
- ✅ UX flows approved
- ✅ Component architecture designed
- ✅ Security requirements defined

### Pending Decisions:
- Migration deployment schedule
- Feature flag strategy for gradual rollout
- Beta testing user group selection
- Performance monitoring setup

---

**Status:** Ready for Phase 1 Implementation  
**Next Step:** Database migration creation and deployment  
**Estimated Start:** Next development session  
**Dependencies:** All architectural decisions approved ✅
