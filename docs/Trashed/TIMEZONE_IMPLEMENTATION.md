# Timezone Management Implementation Guide

## 📋 Overview

TalentHUB now includes comprehensive timezone management for B2B SaaS operations. This system handles global teams, recruitment scheduling, and multi-timezone coordination with enterprise-grade precision.

## 🏗️ Architecture

### Database Layer (PostgreSQL/Supabase)
- **Storage Strategy:** All timestamps stored in UTC using `TIMESTAMP WITH TIME ZONE`
- **User Preferences:** IANA timezone names stored in `user_process_roles.timezone_preference`
- **Organization Defaults:** Default timezone in `organizations.default_timezone`
- **Validation:** Built-in PostgreSQL timezone validation ensures data integrity

### Application Layer (@formkit/tempo)
- **Library:** Modern, lightweight timezone handling with TypeScript support
- **Conversion:** UTC storage ↔ User display timezone conversion
- **Performance:** Efficient timezone calculations without heavy dependencies

### Authentication Integration (Clerk)
- **User Metadata:** Timezone preferences stored in Clerk user metadata
- **Organization Policy:** Organization-level timezone settings and policies
- **Auto-detection:** Browser timezone detection for new users

## 📁 File Structure

```
src/
├── lib/
│   ├── timezone.ts              # Core timezone utilities
│   ├── user-timezone.ts         # Clerk integration hooks
│   └── database-timezone.ts     # Database helper functions
├── components/ui/
│   ├── timezone-selector.tsx    # Timezone selection component
│   └── timezone-time.tsx        # Time display components
└── app/dashboard/settings/
    └── page.tsx                 # Settings page with timezone config
```

## 🔧 Key Components

### 1. Core Utilities (`lib/timezone.ts`)
```typescript
// Get effective timezone (user → org → browser → UTC)
getEffectiveTimezone(config: TimezoneConfig): string

// Format UTC timestamp for user display
formatForUser(utcTimestamp: string | Date, timezone: string): string

// Create UTC timestamp for database storage
createTimestampForStorage(localDatetime: string, timezone: string): Date

// Relative time formatting ("2 hours ago")
formatRelativeForUser(utcTimestamp: string, timezone: string): string
```

### 2. Clerk Integration (`lib/user-timezone.ts`)
```typescript
// React hook for timezone configuration
useTimezoneConfig(): TimezoneConfig & { effectiveTimezone: string }

// Update user timezone preference
updateUserTimezone(timezone: string): Promise<{ success: boolean }>

// Auto-detect browser timezone
autoDetectUserTimezone(): Promise<{ success: boolean, timezone?: string }>
```

### 3. UI Components
- **TimezoneSelector:** Searchable dropdown with common timezones
- **TimezoneTime:** Display timestamps with timezone indicators
- **MultiTimezoneDisplay:** Show times across multiple zones
- **CompactTimezoneTime:** Space-efficient time display for tables

## 🎯 Usage Examples

### Basic Time Display
```tsx
import { TimezoneTime } from "@/components/ui/timezone-time"

<TimezoneTime 
  timestamp="2025-06-24T10:00:00Z"
  format="MMM DD, YYYY h:mm A"
  showTimezone={true}
/>
// Output: "Jun 24, 2025 6:00 AM EST"
```

### Timezone Selection
```tsx
import { TimezoneSelector } from "@/components/ui/timezone-selector"

<TimezoneSelector
  value={userTimezone}
  onValueChange={setUserTimezone}
  showCurrentTime={true}
/>
```

### Database Operations
```tsx
import { createJobWithTimezone } from "@/lib/database-timezone"

// Create job with timezone-aware deadline
await createJobWithTimezone(supabase, {
  title: "Senior Developer",
  deadline: new Date("2025-06-30T17:00:00"), // Stored as UTC
  org_id: organizationId
})
```

## 📊 Database Schema

### Organizations Table
```sql
ALTER TABLE organizations 
ADD COLUMN default_timezone TEXT DEFAULT 'UTC';

-- Constraint ensures valid timezone names
ADD CONSTRAINT valid_org_timezone 
CHECK (default_timezone IN (SELECT name FROM pg_timezone_names));
```

### User Process Roles Table
```sql
ALTER TABLE user_process_roles 
ADD COLUMN timezone_preference TEXT DEFAULT NULL;

-- Index for performance
CREATE INDEX idx_user_roles_timezone 
ON user_process_roles(timezone_preference) 
WHERE timezone_preference IS NOT NULL;
```

## 🔐 Security & Validation

### Timezone Validation
- **PostgreSQL:** Built-in timezone name validation
- **Frontend:** Client-side validation using `Intl.DateTimeFormat`
- **IANA Names:** Only standard timezone identifiers accepted

### Data Integrity
- **UTC Storage:** All timestamps stored in UTC to prevent data corruption
- **Immutable Timestamps:** Original UTC timestamps preserved alongside display versions
- **Error Handling:** Graceful fallbacks for invalid timezones or dates

## 🌍 User Experience

### Timezone Priority Hierarchy
1. **User Preference:** Manually set timezone (highest priority)
2. **Organization Default:** Company-wide timezone setting
3. **Browser Detection:** Auto-detected from user's browser
4. **UTC Fallback:** Safe default if all else fails

### Visual Indicators
- **Timezone Abbreviations:** Always shown (EST, PST, UTC, etc.)
- **Tooltips:** Hover to see UTC time and timezone details
- **Relative Time:** "2 hours ago" for recent timestamps
- **Clear Labels:** Never ambiguous about which timezone is displayed

## 🎨 UI Patterns

### Status Colors with Time
```tsx
<StatusBadge status="active">
  Open
</StatusBadge>
<TimezoneTime timestamp={job.created_at} relative={true} />
```

### Multi-timezone Coordination
```tsx
<MeetingTimeCoordinator
  timestamp={interview.scheduled_at}
  attendeeTimezones={["America/New_York", "Europe/London", "Asia/Tokyo"]}
/>
```

## 🚀 Performance Considerations

### Optimization Strategies
- **Memo Functions:** React.useMemo for expensive timezone calculations
- **Batch Processing:** Group timezone conversions for efficiency
- **Caching:** Common timezone data cached in memory
- **Lazy Loading:** Timezone lists loaded on-demand

### Bundle Size
- **@formkit/tempo:** Lightweight core (~15KB gzipped)
- **Tree Shaking:** Only used functions included in bundle
- **No Plugins:** Built-in timezone support without additional packages

## 🧪 Testing Strategy

### Unit Tests
- Timezone conversion accuracy
- Edge cases (DST transitions, invalid timezones)
- Database timestamp handling
- User preference persistence

### Integration Tests
- Clerk metadata synchronization
- Database timezone constraints
- End-to-end timezone workflows

## 📈 Business Benefits

### Global Operations
- **Accurate Scheduling:** Interview times displayed correctly for all participants
- **Compliance:** Audit trails with precise timestamps
- **User Experience:** No mental timezone conversion required

### Cost Efficiency
- **Zero Runtime Costs:** Open-source solution
- **Maintenance:** Standard web APIs reduce custom code
- **Scalability:** Handles unlimited timezones and users

## 🔄 Migration Strategy

### Existing Data
- Current timestamps assumed to be UTC
- User preferences start empty (auto-detection fills them)
- Organization defaults to UTC initially

### Gradual Rollout
1. **Phase 1:** Enable timezone preferences (opt-in)
2. **Phase 2:** Auto-detect for new users
3. **Phase 3:** Organization-wide timezone policies
4. **Phase 4:** Advanced features (meeting coordination, etc.)

## 📚 Resources

### Documentation
- **@formkit/tempo:** https://tempo.formkit.com/
- **IANA Timezone Database:** https://www.iana.org/time-zones
- **PostgreSQL Timezones:** https://www.postgresql.org/docs/current/datatype-datetime.html

### Best Practices
- Always store UTC in database
- Display in user's preferred timezone
- Include timezone indicators in UI
- Handle DST transitions gracefully
- Validate timezone names
- Provide fallbacks for edge cases

---

**Implementation Status:** ✅ Complete and Ready for Production
**Last Updated:** June 2025
**Dependencies:** @formkit/tempo, Clerk, Supabase
**Maintenance:** Low (uses web standards)
