
# Documentation: Implementing Multi-Currency Subscriptions (USD/INR)

This document outlines the strategy and necessary modifications to implement a dual-currency subscription system supporting USD (via Stripe) and INR (via PhonePe), integrated with the existing Clerk and Supabase infrastructure.

## 1. Core Architectural Strategy

The guiding principle is to use Supabase as the single source of truth for a user's or organization's subscription status. The payment provider (Stripe or PhonePe) is responsible for processing payments and notifying our system via webhooks, but the application logic will always query our own database to determine access rights.

- **User Identity**: Clerk will continue to be the source of user identity (`userId`) and organization identity (`orgId`). All subscriptions will be linked to one of these identifiers.
- **Database as Source of Truth**: The `subscriptions` table (or an equivalent) in your Supabase database will store the active plan, period end date, currency, and payment provider. Components like `FeatureGate.tsx` will check against this local data.
- **Provider Abstraction**: We will create a layer of abstraction in the backend to handle the differences between Stripe and PhonePe. A single API endpoint for creating a checkout session will internally delegate to the correct payment provider based on the user's chosen currency.
- **Webhook Unification**: A primary webhook endpoint will receive and process notifications from both Stripe and PhonePe, updating the Supabase database accordingly.

## 2. Database Schema Modifications

Your database schema needs to be updated to accommodate the multi-currency system. You will need to add a new migration file in the `database-migrations` directory.

**Target Table:** You likely have a `subscriptions` table. If not, you will need to create one. The following columns should be added or ensured they exist:

- `id` (uuid, primary key)
- `user_id` (text or uuid, foreign key to `auth.users` or your Clerk user table)
- `org_id` (text or uuid, nullable, for organization subscriptions)
- `plan_id` (text, e.g., 'free', 'pro', 'enterprise')
- `status` (text, e.g., 'active', 'past_due', 'canceled')
- `currency` (text, **new field**, e.g., 'usd', 'inr')
- `payment_provider` (text, **new field**, e.g., 'stripe', 'phonepe')
- `provider_customer_id` (text, nullable, stores the customer ID from Stripe/PhonePe)
- `provider_subscription_id` (text, unique, stores the subscription ID from Stripe/PhonePe)
- `current_period_ends_at` (timestamp with time zone)
- `created_at` (timestamp with time zone)
- `updated_at` (timestamp with time zone)

**Action:** Create a new SQL file like `database-migrations/010_add_multi_currency_support.sql` to alter the necessary tables.

## 3. Backend File Modifications & Implementation

The backend will see the most significant changes. The logic should be centralized to handle the two payment providers.

### `src/lib/services/subscription.service.ts` (New or Existing)

It's best practice to create a service file to abstract all subscription logic.

- **`createCheckoutSession(userId, orgId, planId, currency)`**:
    - This function will be the main entry point.
    - It will contain a `switch` statement or `if/else` block based on the `currency`.
    - If `currency === 'usd'`, it will call a private function `createStripeCheckout(...)`.
    - If `currency === 'inr'`, it will call a private function `createPhonePeCheckout(...)`.
- **`createStripeCheckout(...)`**:
    - This will contain your existing Stripe checkout session logic.
    - It will use the Supabase Stripe integration (`/api/webhooks/stripe`) to handle post-payment synchronization.
- **`createPhonePeCheckout(...)`**:
    - This will be new logic. You will need to use the PhonePe API to create a payment request.
    - This involves making a server-to-server API call to PhonePe with the amount, transaction ID, and user details.
    - It will return a redirect URL for the user to complete the payment on the PhonePe platform.
- **`cancelSubscription(subscriptionId)`**:
    - This function should look up the subscription in your DB to identify the `payment_provider` and `provider_subscription_id`.
    - It will then call the appropriate provider's API (Stripe or PhonePe) to cancel the subscription.

### `src/app/api/webhooks/route.ts` (or similar)

You need a robust, unified webhook handler.

- **Stripe Webhook (`/api/webhooks/stripe`)**:
    - The existing Supabase integration for Stripe webhooks is excellent and should be maintained. It automatically updates the `stripe` schema in your database.
    - You will need a separate trigger or function that listens for changes in the `stripe.subscriptions` table and propagates the relevant status (`active`, `canceled`) to your public `subscriptions` table.
- **PhonePe Webhook (`/api/webhooks/phonepe`)**:
    - Create a new API route to handle webhooks from PhonePe.
    - **Security is critical**: You MUST verify the checksum/signature sent in the header of the PhonePe webhook request to ensure it's legitimate.
    - The handler will parse the PhonePe event (e.g., `PAYMENT_SUCCESS`, `PAYMENT_FAILED`).
    - On `PAYMENT_SUCCESS`, it will create or update the record in your public `subscriptions` table with the correct plan, status, and period end date.

### `src/app/api/create-checkout/route.ts` (New or Existing)

This API route will be called by the frontend. It will extract the user/org details from the Clerk session and the desired plan/currency from the request body, then call `subscription.service.ts`.

## 4. Frontend File Modifications

The frontend changes are primarily focused on allowing the user to select a currency and displaying the correct pricing.

### Currency Selection UI

- You need to decide where the user selects their currency. Good options include:
    1.  **On the Pricing Page**: A simple toggle (USD/INR) that dynamically updates the displayed prices.
    2.  **In User Profile/Settings**: A one-time selection stored in a new `user_preferences` table.
    3.  **Automatic (Geo-IP)**: Detect the user's location to suggest a default currency. This is user-friendly but should always allow for a manual override.

### `src/components/billing/UpgradeModal.tsx` (or pricing page)

- This component will need to be updated to include the currency selection toggle.
- When the "Upgrade" button is clicked, it should now pass the selected `currency` to the backend API (`/api/create-checkout`).
- The prices displayed should be fetched dynamically or conditioned on the selected currency.

### `src/hooks/useSubscription.ts`

- This hook should be modified to fetch the subscription details from your public `subscriptions` table.
- It should return not only the `planId` and `status` but also the `currency` and `payment_provider`. This can be useful for displaying messages like "You are subscribed via Stripe." or for handling the cancellation flow correctly.

### `src/components/billing/FeatureGate.tsx` and `TierGatedFeature.tsx`

- **No changes should be needed here.** Because these components rely on your Supabase database (via the `useSubscription` hook), and the webhooks keep that database updated, they will work seamlessly regardless of which payment provider was used. This is the primary benefit of this architecture.

## 5. Step-by-Step Implementation Plan

1.  **Setup Developer Accounts**: Ensure you have fully set up developer/merchant accounts for both Stripe and PhonePe.
2.  **Database Migration**: Create and run the SQL migration to add the `currency` and `payment_provider` columns.
3.  **Backend - PhonePe Logic**:
    - Implement the `createPhonePeCheckout` logic.
    - Implement the `/api/webhooks/phonepe` endpoint with signature verification.
4.  **Backend - Abstraction**:
    - Refactor the checkout creation logic into the `subscription.service.ts` file.
    - Update the `/api/create-checkout` endpoint to use this service and handle the currency parameter.
5.  **Backend - Stripe Sync**: Create a Supabase database trigger that syncs data from `stripe.subscriptions` to your public `subscriptions` table whenever a Stripe webhook updates it.
6.  **Frontend - UI**: Add the currency selector to your pricing page/modal.
7.  **Frontend - API Call**: Update the frontend to call the backend with the selected currency.
8.  **Testing**:
    - Test the full Stripe flow in USD.
    - Test the full PhonePe flow in INR.
    - Test the webhook handlers for both providers using their respective testing tools (Stripe CLI, PhonePe simulator).
    - Verify that `FeatureGate.tsx` correctly grants access after a successful payment from *either* provider.
    - Test the cancellation flow for both.
