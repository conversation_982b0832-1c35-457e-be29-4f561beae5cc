# 🔧 Phase 1: Database Connection Fix - Implementation Guide

## 📋 Overview

**Objective**: Connect the beautiful UI to actual database data  
**Priority**: Critical (Phase 1 of 3)  
**Estimated Time**: 1-2 hours  
**Status**: 📋 Ready to Start  

**Problem**: Enhanced profile UI exists but returns null/empty data because the database integration is broken.

**Solution**: Verify database schema deployment, fix service functions, and establish proper data flow.

## 🎯 Success Criteria

By completion of Phase 1, we must achieve:
- [ ] Database migration 006 confirmed deployed to Supabase
- [ ] Enhanced profile API returns real data (not null/empty)
- [ ] Profile UI loads actual database information
- [ ] Profile updates save to enhanced database fields
- [ ] No regression in existing functionality

## 📂 File References

### **Primary Files to Work With:**
- **Migration**: `/database-migrations/006_enhanced_user_profiles.sql`
- **Service**: `/src/lib/services/user-preferences.ts`
- **API**: `/src/app/api/test-enhanced-profile/route.ts`
- **UI Components**: `/src/components/profile/PersonalInformationTab.tsx`
- **Types**: `/src/lib/types/profile.ts` (to be created)

### **Supporting Files:**
- **Environment**: `/.env.local`
- **Supabase Client**: `/src/lib/supabase.ts`
- **Activity Service**: `/src/lib/services/activity-tracking.ts`

## 🗄️ Database Schema Status

### **Expected Enhanced Schema:**
```sql
-- Enhanced user_preferences table (Migration 006)
user_preferences (
  -- Existing fields (from Phase 1-5)
  id, user_id, org_id, is_org_member,
  first_name, last_name, username, display_name,
  timezone, theme, process_context,
  account_tier, subscription_status, features_enabled,
  created_at, updated_at,
  
  -- Enhanced fields (Migration 006)
  profile_picture_url TEXT,
  bio TEXT,
  job_title TEXT,
  phone TEXT,
  linkedin_url TEXT,
  website_url TEXT,
  location TEXT,
  notification_preferences JSONB,
  privacy_settings JSONB,
  last_active TIMESTAMPTZ,
  profile_completeness INTEGER
)

-- New audit table (Migration 006)
user_activity_logs (
  id, user_id, org_id, activity_type,
  activity_data, ip_address, user_agent, created_at
)
```

## 📋 Step-by-Step Implementation

### **Task 1.1: Database Migration Verification** ⭐ START HERE
**Objective**: Confirm Migration 006 is deployed to Supabase database

**Steps:**
1. **Access Supabase Dashboard**
   ```
   URL: https://supabase.com/dashboard/project/weetwfpiancsqezmjyzr
   Navigate to: Table Editor > user_preferences
   ```

2. **Verify Enhanced Columns Exist**
   Check for these columns in `user_preferences` table:
   - [ ] `profile_picture_url`
   - [ ] `bio`
   - [ ] `job_title`
   - [ ] `phone`
   - [ ] `linkedin_url`
   - [ ] `website_url`
   - [ ] `location`
   - [ ] `notification_preferences`
   - [ ] `privacy_settings`
   - [ ] `last_active`
   - [ ] `profile_completeness`

3. **Verify Activity Logs Table**
   Check for `user_activity_logs` table with columns:
   - [ ] `id`, `user_id`, `org_id`, `activity_type`
   - [ ] `activity_data`, `ip_address`, `user_agent`, `created_at`

4. **Deploy Migration if Missing**
   ```sql
   -- If columns are missing, run in Supabase SQL Editor:
   -- Copy content from /database-migrations/006_enhanced_user_profiles.sql
   ```

**Expected Result**: All enhanced columns visible in Supabase table editor

**If Issues**: Document exact error messages and missing columns for troubleshooting

---

### **Task 1.2: Service Layer Integration Fix**
**Objective**: Update user-preferences.ts to work with enhanced schema

**Files to Modify:**
- `/src/lib/services/user-preferences.ts`
- `/src/lib/types/profile.ts` (new file)

#### **Subtask 1.2.1: Create Enhanced Profile Types**
**File**: `/src/lib/types/profile.ts`

**Implementation**:
```typescript
// Enhanced profile type definitions
export interface EnhancedUserProfile {
  // Core fields (existing)
  id: string
  userId: string
  orgId?: string
  isOrgMember: boolean
  firstName: string
  lastName: string
  username: string
  displayName?: string
  timezone: string
  theme: 'light' | 'dark' | 'system'
  processContext: 'recruitment' | 'bench_sales' | 'both'
  accountTier: 'free' | 'pro' | 'team' | 'enterprise'
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'unpaid'
  featuresEnabled: Record<string, boolean>
  
  // Enhanced fields (new)
  profilePictureUrl?: string
  bio?: string
  jobTitle?: string
  phone?: string
  linkedinUrl?: string
  websiteUrl?: string
  location?: string
  notificationPreferences: NotificationPreferences
  privacySettings: PrivacySettings
  lastActive: string
  profileCompleteness: number
  createdAt: string
  updatedAt: string
}

export interface NotificationPreferences {
  emailNotifications: boolean
  browserNotifications: boolean
  workflowUpdates: boolean
  organizationUpdates: boolean
  securityAlerts: boolean
}

export interface PrivacySettings {
  profileVisibility: 'private' | 'organization' | 'public'
  showEmail: boolean
  showPhone: boolean
  showLocation: boolean
}
```

#### **Subtask 1.2.2: Fix Database Column Mapping**
**File**: `/src/lib/services/user-preferences.ts`

**Key Functions to Fix:**
1. `getEnhancedUserProfile()` - Fix column mapping
2. `updateEnhancedUserProfile()` - Fix update operations
3. `transformDatabaseToEnhancedProfile()` - Map database fields to TypeScript

**Critical Mapping Issues to Fix:**
```typescript
// Database column → TypeScript property mapping
profile_picture_url → profilePictureUrl
job_title → jobTitle
linkedin_url → linkedinUrl
website_url → websiteUrl
notification_preferences → notificationPreferences
privacy_settings → privacySettings
last_active → lastActive
profile_completeness → profileCompleteness
```

#### **Subtask 1.2.3: Test Service Functions**
**Method**: Use API endpoint to test service integration

```bash
# Test getting profile
curl -X GET http://localhost:3009/api/test-enhanced-profile

# Test creating profile
curl -X POST http://localhost:3009/api/test-enhanced-profile \
  -H "Content-Type: application/json" \
  -d '{"action": "create_profile", "firstName": "Test", "lastName": "User"}'
```

**Expected Results**:
- GET returns profile data (not null)
- POST creates new profile successfully
- Profile completeness calculates correctly

---

### **Task 1.3: API Endpoint Validation**
**Objective**: Ensure API endpoints work with enhanced database schema

**File**: `/src/app/api/test-enhanced-profile/route.ts`

**Testing Steps:**
1. **Test Profile Creation**
   - Create new enhanced profile via API
   - Verify all fields save correctly
   - Check profile completeness calculation

2. **Test Profile Updates**
   - Update various profile fields
   - Verify changes persist to database
   - Check automatic completeness recalculation

3. **Test Error Handling**
   - Invalid data scenarios
   - Authentication failures
   - Database connection issues

**Success Criteria**:
- [ ] API creates enhanced profiles successfully
- [ ] API updates enhanced profiles correctly
- [ ] Profile completeness auto-calculates
- [ ] Proper error messages for failures

---

### **Task 1.4: UI Integration Testing**
**Objective**: Verify profile UI loads and saves real data

**File**: `/src/components/profile/PersonalInformationTab.tsx`

**Testing Process:**
1. **Load Profile Data**
   - Open `/profile` in browser
   - Check if personal information loads
   - Verify profile completeness displays

2. **Save Profile Changes**
   - Modify profile fields in UI
   - Click "Save Changes"
   - Verify data persists after page refresh

3. **Profile Completeness**
   - Test adding/removing profile information
   - Verify completion percentage updates
   - Check progress bar reflects changes

**Success Criteria**:
- [ ] Profile UI loads real database data
- [ ] Profile changes save successfully
- [ ] Profile completeness updates automatically
- [ ] No console errors during operations

---

## 🛡️ Quality Assurance Checklist

### **Before Starting:**
- [ ] Development server running (`npm run dev`)
- [ ] Supabase dashboard accessible
- [ ] Environment variables confirmed in `.env.local`
- [ ] Backup of current code state

### **During Development:**
- [ ] Test each service function individually
- [ ] Verify database changes in Supabase dashboard
- [ ] Check console for errors after each change
- [ ] Test with real Clerk authentication

### **After Completion:**
- [ ] All enhanced profile fields working
- [ ] Profile completeness calculating correctly
- [ ] No regression in existing functionality
- [ ] Performance meets <500ms target for profile operations

## 🚨 Common Issues & Solutions

### **Issue 1: Migration Not Deployed**
**Symptoms**: Enhanced columns missing in Supabase dashboard  
**Solution**: Run migration SQL manually in Supabase SQL Editor

### **Issue 2: Column Mapping Errors**
**Symptoms**: API returns null or undefined for enhanced fields  
**Solution**: Fix snake_case to camelCase mapping in service functions

### **Issue 3: RLS Policy Blocking Access**
**Symptoms**: API returns empty results despite data existing  
**Solution**: Verify user authentication and RLS policies

### **Issue 4: Profile Completeness Not Calculating**
**Symptoms**: Completion percentage shows 0 or incorrect value  
**Solution**: Check database trigger function and manual calculation

## 📊 Progress Tracking

### **Task Completion Status:**
- [ ] **Task 1.1**: Database migration verification
- [ ] **Task 1.2**: Service layer integration fix
- [ ] **Task 1.3**: API endpoint validation  
- [ ] **Task 1.4**: UI integration testing

### **Session Notes:**
```
Date: [Add date when working]
Developer: [Your name]
Issues Encountered: [Document any problems]
Solutions Applied: [How issues were resolved]
Next Session Focus: [What to prioritize next]
```

## 🚀 Completion Criteria

**Phase 1 is COMPLETE when:**
1. ✅ Database migration 006 confirmed deployed
2. ✅ Enhanced profile API returns real data
3. ✅ Profile UI loads actual database information
4. ✅ Profile updates persist to enhanced fields
5. ✅ Profile completeness calculates automatically
6. ✅ No regressions in existing functionality

**Ready for Phase 2:** Profile initialization and webhook activation

---

**Next Phase**: [PHASE_2_PROFILE_INITIALIZATION.md](./PHASE_2_PROFILE_INITIALIZATION.md)

**Last Updated**: [Date]  
**Status**: Ready for implementation  
**Estimated Completion**: 1-2 hours of focused development
