# Railway Deployment
Railway is a platform that provides instant deployments with public URLs.

## Quick Deploy to Railway:
1. Go to: https://railway.app
2. Sign in with GitHub
3. Click "Deploy from GitHub"
4. Select this repository
5. Railway will automatically deploy and give you a public URL

## Or use their CLI:
```bash
npm install -g @railway/cli
railway login
railway deploy
```

This will give you an immediate, stable webhook URL.
