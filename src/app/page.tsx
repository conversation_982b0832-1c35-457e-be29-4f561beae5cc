import { SignedIn, SignedOut, SignIn<PERSON><PERSON>on, SignUp<PERSON><PERSON>on, User<PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Landing Page Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold tracking-tight">TalentHUB</h1>
          </div>
          <div className="flex items-center space-x-4">
            <SignedOut>
              <SignInButton mode="modal">
                <button className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
                  Sign In
                </button>
              </SignInButton>
              <SignUpButton mode="modal">
                <button className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
                  Get Started
                </button>
              </SignUpButton>
            </SignedOut>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-16">
        <SignedOut>
          <div className="text-center space-y-6">
            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Welcome to TalentHUB
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Multi-tier hiring solutions for recruitment and bench sales processes.
              Streamline your hiring workflow with our comprehensive B2B platform.
            </p>
            <div className="flex justify-center gap-4 mt-8">
              <Card className="p-6 max-w-md">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Recruitment Process</CardTitle>
                  <CardDescription>
                    End Client → Vendor → Supplier → Candidate
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Job posting management</li>
                    <li>• Candidate sourcing & screening</li>
                    <li>• Interview scheduling</li>
                    <li>• Offer management</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="p-6 max-w-md">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Bench Sales Process</CardTitle>
                  <CardDescription>
                    Supplier → Vendor → Client
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Available candidate profiles</li>
                    <li>• Client requirement matching</li>
                    <li>• Contract negotiations</li>
                    <li>• Placement tracking</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
            
            <div className="mt-12 p-6 bg-card border border-border rounded-lg shadow-sm max-w-md mx-auto">
              <h2 className="text-lg font-semibold mb-4">Tech Stack</h2>
              <ul className="text-sm text-muted-foreground space-y-2">
                <li>✅ Next.js 15 + React + TypeScript</li>
                <li>✅ shadcn/ui (New York style)</li>
                <li>✅ Tailwind CSS</li>
                <li>✅ Clerk Authentication</li>
                <li>🔄 NileDB (Database - Coming Soon)</li>
                <li>🔄 Cloudflare (Hosting - Coming Soon)</li>
              </ul>
            </div>
          </div>
        </SignedOut>

        <SignedIn>
          <div className="space-y-6">
            <div className="text-center space-y-4">
              <h1 className="text-4xl font-bold tracking-tight text-foreground">
                Welcome to TalentHUB
              </h1>
              <p className="text-xl text-muted-foreground">
                You&apos;re signed in! Access your dashboard to manage your hiring processes.
              </p>
              <div className="flex justify-center gap-4 mt-8">
                <Link href="/dashboard">
                  <Button size="lg">
                    Go to Dashboard
                  </Button>
                </Link>
                <Button variant="outline" size="lg">
                  Quick Tour
                </Button>
              </div>
            </div>
          </div>
        </SignedIn>
      </div>
    </div>
  );
}
