@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core Layout */
    --background: 0 0% 100%;
    --foreground: 220 26% 14%;
    --card: 0 0% 100%;
    --card-foreground: 220 26% 14%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 26% 14%;
    
    /* Apple Blue Primary System */
    --primary: 214 100% 50%;          /* Apple Blue #007AFF */
    --primary-foreground: 0 0% 98%;
    --primary-hover: 214 100% 40%;    /* Darker Apple Blue #0056CC */
    --primary-light: 214 100% 90%;    /* Light Apple Blue #CCE7FF */
    
    /* Workflow Colors (Both Apple Blue) */
    --recruitment: 214 100% 50%;      /* Apple Blue #007AFF */
    --bench-sales: 214 100% 50%;      /* Apple Blue #007AFF */
    
    /* Status Colors */
    --critical: 0 84% 60%;            /* Red #DC2626 */
    --in-progress: 38 92% 50%;        /* Amber #F59E0B */
    --complete: 160 84% 39%;          /* Green #059669 */
    --inactive: 215 16% 47%;          /* Gray #6B7280 */
    
    /* Secondary Colors */
    --secondary: 220 13% 91%;
    --secondary-foreground: 220 26% 14%;
    --muted: 220 13% 91%;
    --muted-foreground: 215 16% 47%;
    --accent: 214 100% 95%;
    --accent-foreground: 214 100% 50%;
    
    /* Destructive (same as critical) */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    /* UI Elements */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 214 100% 50%;
    --radius: 0.5rem;
  }

  .dark {
    /* Core Layout */
    --background: 220 26% 14%;
    --foreground: 0 0% 98%;
    --card: 220 26% 14%;
    --card-foreground: 0 0% 98%;
    --popover: 220 26% 14%;
    --popover-foreground: 0 0% 98%;
    
    /* Apple Blue Primary System (adjusted for dark mode) */
    --primary: 214 100% 60%;          /* Lighter Apple Blue for dark backgrounds */
    --primary-foreground: 220 26% 14%;
    --primary-hover: 214 100% 70%;    /* Even lighter for hover */
    --primary-light: 214 100% 25%;    /* Darker variant for dark mode backgrounds */
    
    /* Workflow Colors */
    --recruitment: 214 100% 60%;      /* Apple Blue */
    --bench-sales: 214 100% 60%;      /* Apple Blue */
    
    /* Status Colors (adjusted for dark mode) */
    --critical: 0 84% 70%;            /* Lighter red for dark backgrounds */
    --in-progress: 38 92% 60%;        /* Lighter amber */
    --complete: 160 84% 49%;          /* Lighter green */
    --inactive: 215 16% 57%;          /* Lighter gray */
    
    /* Secondary Colors */
    --secondary: 215 28% 17%;
    --secondary-foreground: 0 0% 98%;
    --muted: 215 28% 17%;
    --muted-foreground: 215 16% 57%;
    --accent: 215 28% 17%;
    --accent-foreground: 214 100% 60%;
    
    /* Destructive */
    --destructive: 0 84% 70%;
    --destructive-foreground: 0 0% 98%;
    
    /* UI Elements */
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 214 100% 60%;
  }
}

@layer components {
  /* TalentHUB Status Colors */
  .status-critical {
    @apply bg-red-50 text-red-700 border-red-200;
  }
  .status-critical-dark {
    @apply dark:bg-red-950 dark:text-red-300 dark:border-red-800;
  }
  
  .status-in-progress {
    @apply bg-amber-50 text-amber-700 border-amber-200;
  }
  .status-in-progress-dark {
    @apply dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800;
  }
  
  .status-complete {
    @apply bg-green-50 text-green-700 border-green-200;
  }
  .status-complete-dark {
    @apply dark:bg-green-950 dark:text-green-300 dark:border-green-800;
  }
  
  .status-active {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }
  .status-active-dark {
    @apply dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800;
  }
  
  .status-inactive {
    @apply bg-gray-50 text-gray-500 border-gray-200;
  }
  .status-inactive-dark {
    @apply dark:bg-gray-950 dark:text-gray-400 dark:border-gray-800;
  }

  /* Workflow specific utilities */
  .workflow-recruitment {
    @apply border-l-4 border-l-primary bg-primary/5;
  }
  
  .workflow-bench-sales {
    @apply border-l-4 border-l-primary bg-primary/5;
  }
}
