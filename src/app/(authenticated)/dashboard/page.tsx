import { auth, currentUser } from "@clerk/nextjs/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { StatusBadge } from "@/components/ui/status-badge";
import { WorkflowCard } from "@/components/ui/workflow-card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default async function DashboardPage() {
  const { userId } = await auth();
  const user = await currentUser();

  // Sample data to demonstrate the design system with timezone-aware timestamps
  const recentJobs = [
    {
      id: 1,
      workflow: "recruitment" as const,
      title: "Senior React Developer",
      description: "Full-stack position with competitive salary",
      status: "active" as const,
      priority: "high" as const,
      created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
      metadata: [
        { label: "Client", value: "TechCorp Inc." },
        { label: "Posted", value: "2 days ago" }
      ]
    },
    {
      id: 2,
      workflow: "bench-sales" as const,
      title: "Java Developer Available",
      description: "5+ years experience, immediately available",
      status: "in-progress" as const,
      priority: "medium" as const,
      created_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago
      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
      metadata: [
        { label: "Experience", value: "5 years" },
        { label: "Location", value: "Remote" }
      ]
    },
    {
      id: 3,
      workflow: "recruitment" as const,
      title: "DevOps Engineer",
      description: "AWS/Docker expertise required",
      status: "complete" as const,
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
      deadline: null,
      metadata: [
        { label: "Client", value: "StartupXYZ" },
        { label: "Filled", value: "1 week ago" }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.firstName || "User"}! Here&apos;s your recruitment overview.
          </p>
        </div>
        <div className="flex gap-2">
          <Button>New Job</Button>
        </div>
      </div>

      {/* Status Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <StatusBadge status="active" showIcon>
              Open
            </StatusBadge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">12</div>
            <p className="text-xs text-muted-foreground">
              +2 from last week
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <StatusBadge status="in-progress" showIcon>
              Working
            </StatusBadge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600">8</div>
            <p className="text-xs text-muted-foreground">
              Interviews scheduled
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <StatusBadge status="complete" showIcon>
              Filled
            </StatusBadge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">15</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical</CardTitle>
            <StatusBadge status="critical" showIcon>
              Urgent
            </StatusBadge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">3</div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Workflow Cards */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              Live Updates
            </Badge>
          </div>
          
          <div className="space-y-4">
            {recentJobs.map((job) => (
              <WorkflowCard
                key={job.id}
                workflow={job.workflow}
                title={job.title}
                description={job.description}
                status={job.status}
                priority={job.priority}
                metadata={job.metadata}
                className="cursor-pointer hover:bg-primary/10 transition-colors"
              />
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Quick Actions</h2>
          
          <div className="grid gap-4">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-primary"></div>
                  Create New Job
                </CardTitle>
                <CardDescription>
                  Post a new position for recruitment process
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  Start Recruitment
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-primary"></div>
                  Add Bench Resource
                </CardTitle>
                <CardDescription>
                  Add available consultant to bench sales
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="outline">
                  Add to Bench
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-amber-500"></div>
                  Organization Settings
                </CardTitle>
                <CardDescription>
                  Configure workflows and team permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="secondary">
                  Manage Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Apple Blue Design System Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Apple Blue Design System Preview</CardTitle>
          <CardDescription>
            Demonstrating our unified color system across both workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Status Indicators</h4>
              <div className="flex flex-wrap gap-2">
                <StatusBadge status="critical">Critical</StatusBadge>
                <StatusBadge status="in-progress">In Progress</StatusBadge>
                <StatusBadge status="complete">Complete</StatusBadge>
                <StatusBadge status="active">Active</StatusBadge>
                <StatusBadge status="inactive">Inactive</StatusBadge>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-2">Workflow Badges</h4>
              <div className="flex gap-2">
                <Badge className="bg-primary/10 text-primary hover:bg-primary/20">
                  Recruitment
                </Badge>
                <Badge className="bg-primary/10 text-primary hover:bg-primary/20">
                  Bench Sales
                </Badge>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Button Variants</h4>
              <div className="flex gap-2 flex-wrap">
                <Button>Primary Action</Button>
                <Button variant="outline">Secondary</Button>
                <Button variant="secondary">Tertiary</Button>
                <Button variant="destructive">Delete</Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
