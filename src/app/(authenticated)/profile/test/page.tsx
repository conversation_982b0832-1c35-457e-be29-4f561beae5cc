"use client"

import React from 'react'

export default function TestPage() {
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
    console.log('TestPage: Component mounted successfully')
  }, [])

  if (!mounted) {
    return <div>Loading test page...</div>
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Profile Test Page</h1>
        <p className="text-muted-foreground">
          This page tests if basic profile functionality is working
        </p>
      </div>
      
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="font-semibold text-blue-900">✅ Profile System Status</h2>
        <p className="text-blue-800">Components are loading and React is working properly.</p>
      </div>

      <div className="space-y-4">
        <h3 className="font-semibold">Navigation Test</h3>
        <div className="flex gap-4">
          <a href="/profile/account" className="text-blue-600 hover:underline">
            → Go to Account Settings
          </a>
          <a href="/profile/security" className="text-blue-600 hover:underline">
            → Go to Security Settings
          </a>
          <a href="/profile/personal" className="text-blue-600 hover:underline">
            → Go to Personal Settings
          </a>
        </div>
      </div>
    </div>
  )
}
