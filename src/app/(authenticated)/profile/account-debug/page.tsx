"use client"

import React from 'react'

export default function SimpleAccountPage() {
  const [isLoading, setIsLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)
  const [data, setData] = React.useState<any>(null)

  React.useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('SimpleAccountPage: Starting API call...')
        const response = await fetch('/api/user-profile')
        console.log('SimpleAccountPage: Response received:', response.status)
        
        if (response.ok) {
          const result = await response.json()
          console.log('SimpleAccountPage: Data received:', result)
          setData(result)
        } else {
          console.error('SimpleAccountPage: API error:', response.status, response.statusText)
          setError(`API Error: ${response.status}`)
        }
      } catch (err) {
        console.error('SimpleAccountPage: Request failed:', err)
        setError(`Request Failed: ${(err as Error).message}`)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  if (isLoading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Account Settings (Debug)</h1>
        <div className="bg-blue-50 p-4 rounded">
          Loading account data...
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Account Settings (Debug)</h1>
        <div className="bg-red-50 p-4 rounded border border-red-200">
          <h3 className="font-semibold text-red-800">Error Loading Data</h3>
          <p className="text-red-700">{error}</p>
          <p className="text-sm text-red-600 mt-2">
            Check browser console for more details.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Account Settings (Debug Mode)</h1>
        <p className="text-muted-foreground">
          Basic account information and API test
        </p>
      </div>

      <div className="bg-green-50 p-4 rounded border border-green-200">
        <h3 className="font-semibold text-green-800">✅ API Connection Successful</h3>
        <p className="text-green-700">Successfully retrieved user profile data</p>
      </div>

      {data && (
        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Profile Data:</h3>
          <div className="space-y-2">
            <p><strong>User ID:</strong> {data.profile?.user_id || 'Not available'}</p>
            <p><strong>Name:</strong> {data.profile?.display_name || 'Not available'}</p>
            <p><strong>Account Tier:</strong> {data.profile?.account_tier || 'Not available'}</p>
            <p><strong>Theme:</strong> {data.profile?.theme || 'Not available'}</p>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <h3 className="font-semibold">Navigation</h3>
        <div className="flex gap-4">
          <a href="/profile/account" className="text-blue-600 hover:underline">
            → Full Account Settings
          </a>
          <a href="/profile/security" className="text-blue-600 hover:underline">
            → Security Settings
          </a>
        </div>
      </div>
    </div>
  )
}
