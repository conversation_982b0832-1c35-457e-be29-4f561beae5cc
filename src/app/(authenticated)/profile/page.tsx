"use client"

import * as React from "react"
import { useUser } from "@clerk/nextjs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingState } from "@/components/ui/LoadingStates"
import { PersonalInformationTab } from "@/components/profile/PersonalInformationTab"

export default function ProfilePage() {
  const { user, isLoaded: userLoaded } = useUser()

  if (!userLoaded) {
    return (
      <div className="p-6">
        <LoadingState message="Loading profile..." />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access your profile settings.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your personal information, account settings, and preferences
        </p>
      </div>

      {/* Personal Information Content */}
      <PersonalInformationTab />
    </div>
  )
}
