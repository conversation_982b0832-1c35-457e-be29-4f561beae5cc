
"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingState } from "@/components/ui/LoadingStates"

export default function UsageHistoryPage() {
  const [usageHistory, setUsageHistory] = React.useState<any[] | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchUsageHistory = async () => {
      try {
        const response = await fetch("/api/usage-history")
        if (response.ok) {
          const data = await response.json()
          setUsageHistory(data)
        }
      } catch (error) {
        console.error("Failed to fetch usage history:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUsageHistory()
  }, [])

  if (isLoading) {
    return <LoadingState message="Loading usage history..." />
  }

  if (!usageHistory) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load usage history</p>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Usage History</CardTitle>
          <CardDescription>
            View your usage history across different features
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* TODO: Display usage history */}
        </CardContent>
      </Card>
    </div>
  )
}
