import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { Sidebar } from "@/components/ui/sidebar";

/**
 * Authenticated Layout
 * 
 * This layout wraps all authenticated routes and provides:
 * - Authentication protection (redirects non-authenticated users)
 * - Sidebar navigation for the entire authenticated experience
 * - Consistent layout for dashboard, profile, notifications, etc.
 */
export default async function AuthenticatedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { userId } = await auth();

  if (!userId) {
    redirect("/");
  }

  return (
    <Sidebar>
      {children}
    </Sidebar>
  );
}
