
"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingState } from "@/components/ui/LoadingStates"
import { useAuth } from "@clerk/nextjs"

export default function AuditLogPage() {
  const { orgRole } = useAuth()
  const [auditLogs, setAuditLogs] = React.useState<any[] | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchAuditLogs = async () => {
      if (orgRole !== "admin") {
        setIsLoading(false)
        return
      }

      try {
        // TODO: Fetch audit logs from Clerk's API
        setAuditLogs([])
      } catch (error) {
        console.error("Failed to fetch audit logs:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAuditLogs()
  }, [orgRole])

  if (isLoading) {
    return <LoadingState message="Loading audit logs..." />
  }

  if (orgRole !== "admin") {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You must be an administrator to view the audit log.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (!auditLogs) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load audit logs</p>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Audit Log</CardTitle>
          <CardDescription>
            View a log of all activity in your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* TODO: Display audit logs */}
        </CardContent>
      </Card>
    </div>
  )
}
