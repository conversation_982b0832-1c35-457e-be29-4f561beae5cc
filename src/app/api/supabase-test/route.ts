import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  try {
    // Test basic Supabase connection (without auth)
    const { data, error } = await supabase
      .from('organizations')
      .select('id, name')
      .limit(1)
    
    if (error) {
      return NextResponse.json(
        { 
          error: 'Supabase connection failed',
          details: error.message,
          code: error.code
        },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: 'Supabase connection successful',
      data: data,
      tables_accessible: true
    })
  } catch (error) {
    console.error('Supabase test error:', error)
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
