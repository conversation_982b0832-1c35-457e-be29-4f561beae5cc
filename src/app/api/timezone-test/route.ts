import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClerkSupabaseClient } from '@/lib/supabase'

export async function GET() {
  try {
    const { userId, getToken } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const supabase = createClerkSupabaseClient(getToken)
    
    // Test user preferences table
    const { data: userPrefs, error: userError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    // Test organizations table with timezone settings
    const { data: orgs, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, timezone_settings')
      .limit(5)
    
    return NextResponse.json({
      message: 'Timezone database test successful',
      user_id: userId,
      user_preferences: userPrefs,
      user_preferences_error: userError?.message,
      organizations: orgs,
      organizations_error: orgError?.message,
      database_tables_available: true
    })
  } catch (error) {
    console.error('Timezone database test error:', error)
    return NextResponse.json(
      { 
        error: 'Database test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, getToken } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const body = await request.json()
    const { timezone, autoDetected = false } = body
    
    if (!timezone) {
      return NextResponse.json({ error: 'Timezone is required' }, { status: 400 })
    }
    
    const supabase = createClerkSupabaseClient(getToken)
    
    // Test updating user timezone preference
    const { data, error } = await supabase
      .from('user_preferences')
      .upsert({
        user_id: userId,
        timezone,
        timezone_auto_detected: autoDetected,
        timezone_set_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      })
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to update timezone', details: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: 'Timezone updated successfully',
      data
    })
  } catch (error) {
    console.error('Timezone update test error:', error)
    return NextResponse.json(
      { 
        error: 'Timezone update failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
