/**
 * Test webhook secret in API route context
 */

import { NextResponse } from 'next/server';

export async function GET() {
  console.log('=== WEBHOOK SECRET DEBUG ===');
  console.log('CLERK_WEBHOOK_SECRET exists:', !!process.env.CLERK_WEBHOOK_SECRET);
  console.log('CLERK_WEBHOOK_SECRET length:', process.env.CLERK_WEBHOOK_SECRET?.length);
  console.log('CLERK_WEBHOOK_SECRET preview:', process.env.CLERK_WEBHOOK_SECRET?.substring(0, 15) + '...');
  
  return NextResponse.json({
    hasSecret: !!process.env.CLERK_WEBHOOK_SECRET,
    secretLength: process.env.CLERK_WEBHOOK_SECRET?.length,
    secretPreview: process.env.CLERK_WEBHOOK_SECRET?.substring(0, 15) + '...'
  });
}
