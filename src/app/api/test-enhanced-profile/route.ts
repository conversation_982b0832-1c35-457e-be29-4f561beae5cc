import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { 
  getEnhancedUserProfile, 
  createEnhancedUserProfile, 
  updateEnhancedUserProfile,
  calculateProfileCompleteness 
} from '@/lib/services/user-preferences'

export async function GET() {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Supabase admin client not available' },
        { status: 500 }
      )
    }

    // Test 1: Check database schema is complete
    let schemaComplete = false
    let schemaError = null

    try {
      const { data, error } = await supabaseAdmin
        .from('user_preferences')
        .select(`
          id,
          user_id,
          first_name,
          last_name,
          username,
          bio,
          job_title,
          phone,
          linkedin_url,
          website_url,
          location,
          notification_preferences,
          privacy_settings,
          last_active,
          profile_completeness
        `)
        .limit(1)

      if (error) {
        schemaError = error.message
        schemaComplete = false
      } else {
        schemaComplete = true
      }
    } catch (error) {
      schemaError = (error as Error).message
      schemaComplete = false
    }

    // Test 2: Test enhanced profile service
    let serviceTest = {
      works: false,
      error: null as string | null,
      profileCount: 0
    }

    try {
      // Try to get any existing profile to test the service
      const { data: existingProfiles } = await supabaseAdmin
        .from('user_preferences')
        .select('user_id')
        .limit(1)

      if (existingProfiles && existingProfiles.length > 0) {
        const testUserId = existingProfiles[0].user_id
        const profile = await getEnhancedUserProfile(testUserId)
        
        if (profile) {
          serviceTest.works = true
          serviceTest.profileCount = 1
        }
      } else {
        // No profiles exist, but service is available
        serviceTest.works = true
        serviceTest.profileCount = 0
      }
    } catch (error) {
      serviceTest.error = (error as Error).message
      serviceTest.works = false
    }

    // Test 3: Test profile completeness calculation
    let completenessTest = {
      works: false,
      error: null as string | null,
      functionExists: false
    }

    try {
      // Test if the database function exists
      const { data, error } = await supabaseAdmin.rpc('calculate_profile_completeness', {
        p_first_name: 'John',
        p_last_name: 'Doe',
        p_username: 'johndoe',
        p_bio: null,
        p_job_title: null,
        p_phone: null,
        p_linkedin_url: null,
        p_website_url: null,
        p_location: null,
        p_profile_picture_url: null
      })

      if (error) {
        completenessTest.error = error.message
        completenessTest.functionExists = false
      } else {
        completenessTest.works = true
        completenessTest.functionExists = true
      }
    } catch (error) {
      completenessTest.error = (error as Error).message
      completenessTest.functionExists = false
    }

    const migrationStatus = schemaComplete && serviceTest.works ? 'complete' : 'incomplete'

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      phase_1_status: migrationStatus,
      tests: {
        database_schema: {
          complete: schemaComplete,
          error: schemaError
        },
        enhanced_profile_service: serviceTest,
        profile_completeness_function: completenessTest
      },
      next_steps: migrationStatus === 'complete' 
        ? [
            '✅ Phase 1 Database Connection Fix - COMPLETE!',
            '➡️ Ready for Phase 2: Profile Initialization',
            'Next: Implement Clerk webhook handlers',
            'Next: Test complete user profile workflow'
          ]
        : [
            '❌ Phase 1 incomplete - issues found',
            'Fix database schema or service integration',
            'Check error messages above for details'
          ]
    })

  } catch (error) {
    console.error('Database verification error:', error)
    return NextResponse.json(
      { 
        error: 'Database verification failed', 
        details: (error as Error).message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Supabase admin client not available' },
        { status: 500 }
      )
    }

    if (action === 'create_test_profile') {
      // Test creating an enhanced profile directly with admin client
      const testProfile = {
        user_id: 'test_user_' + Date.now(),
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser_' + Date.now(),
        display_name: 'Test User Profile',
        timezone: 'America/New_York',
        theme: 'light',
        process_context: 'recruitment',
        account_tier: 'free',
        subscription_status: 'active',
        features_enabled: {},
        is_org_member: false,
        
        // Enhanced profile fields
        bio: 'Test bio for enhanced profile system',
        job_title: 'Software Engineer',
        phone: '******-0123',
        linkedin_url: 'https://linkedin.com/in/testuser',
        website_url: 'https://testuser.dev',
        location: 'San Francisco, CA',
        notification_preferences: {
          emailNotifications: true,
          browserNotifications: true,
          workflowUpdates: true,
          organizationUpdates: true,
          securityAlerts: true
        },
        privacy_settings: {
          profileVisibility: 'organization',
          showEmail: false,
          showPhone: false,
          showLocation: true
        }
      }

      const { data, error } = await supabaseAdmin
        .from('user_preferences')
        .insert(testProfile)
        .select()
        .single()

      if (error) {
        return NextResponse.json(
          { error: 'Failed to create test profile', details: error.message },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Enhanced profile created successfully',
        profile: data,
        completeness: data.profile_completeness || 0
      })
    }

    if (action === 'update_test_profile') {
      const { userId } = body
      
      if (!userId) {
        return NextResponse.json(
          { error: 'userId is required for profile update test' },
          { status: 400 }
        )
      }

      // Test updating enhanced profile fields
      const updates = {
        bio: 'Updated bio for testing enhanced profile system',
        jobTitle: 'Senior Software Engineer',
        phone: '******-0123',
        linkedinUrl: 'https://linkedin.com/in/testuser',
        websiteUrl: 'https://testuser.dev',
        location: 'San Francisco, CA',
        notificationPreferences: {
          emailNotifications: false,
          browserNotifications: true
        }
      }

      const result = await updateEnhancedUserProfile(userId, updates)

      if (!result.success) {
        return NextResponse.json(
          { error: 'Failed to update test profile', details: result.error },
          { status: 500 }
        )
      }

      // Calculate new completeness
      const completeness = await calculateProfileCompleteness(userId)

      return NextResponse.json({
        success: true,
        message: 'Enhanced profile updated successfully',
        profile: result.data,
        completeness: completeness
      })
    }

    if (action === 'test_completeness_calculation') {
      // Test the profile completeness calculation service function
      const testUserId = body.userId || 'test_user_for_completeness'
      const completeness = await calculateProfileCompleteness(testUserId)

      return NextResponse.json({
        success: true,
        userId: testUserId,
        completeness_percentage: completeness,
        message: 'Profile completeness calculation working correctly'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "create_test_profile", "update_test_profile", or "test_completeness_calculation"' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Test endpoint error:', error)
    return NextResponse.json(
      { 
        error: 'Test endpoint failed', 
        details: (error as Error).message 
      },
      { status: 500 }
    )
  }
}
