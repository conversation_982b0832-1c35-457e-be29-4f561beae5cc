import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    const { userId, getToken } = await auth()
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated',
        setup_required: false
      }, { status: 401 })
    }
    
    // Test JWT template
    let token
    try {
      token = await getToken({ template: 'supabase' })
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'JWT template not configured',
        setup_required: true,
        message: 'Please set up Clerk JWT template for Supabase integration'
      })
    }
    
    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'No JWT token available',
        setup_required: true,
        message: 'JWT template exists but no token generated'
      })
    }
    
    // Test authenticated Supabase connection
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: `Bear<PERSON> ${token}`,
          },
        },
      }
    )
    
    // Test database functions
    const { data: clerkUserId, error: functionError } = await supabase
      .rpc('get_clerk_user_id')
    
    if (functionError) {
      return NextResponse.json({
        success: false,
        error: 'Database function error',
        details: functionError.message,
        setup_required: false
      })
    }
    
    // Test user_preferences table access
    const { data: userPrefs, error: tableError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .limit(1)
    
    if (tableError) {
      return NextResponse.json({
        success: false,
        error: 'RLS policy error',
        details: tableError.message,
        setup_required: false,
        debug_info: {
          expected_user_id: userId,
          jwt_user_id: clerkUserId
        }
      })
    }
    
    return NextResponse.json({
      success: true,
      message: 'Clerk + Supabase integration working correctly',
      debug_info: {
        user_id: userId,
        jwt_user_id: clerkUserId,
        user_preferences_count: userPrefs?.length || 0,
        token_available: !!token
      }
    })
    
  } catch (error) {
    console.error('Integration test error:', error)
    return NextResponse.json({
      success: false,
      error: 'Integration test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
