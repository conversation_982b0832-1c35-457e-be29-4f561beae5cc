import { NextRequest, NextResponse } from 'next/server'
import { currentUser } from '@clerk/nextjs/server'
import { getEnhancedUserProfile, updateEnhancedUserProfile } from '@/lib/services/user-preferences'
import { EnhancedUserProfile } from '@/lib/types/profile'

export async function GET() {
  try {
    const user = await currentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - No user session found' },
        { status: 401 }
      )
    }

    // Return a simple default profile for now
    const defaultProfile = {
      id: user.id,
      user_id: user.id,
      first_name: user.firstName || '',
      last_name: user.lastName || '',
      username: user.username || '',
      display_name: `${user.firstName} ${user.lastName}`.trim(),
      timezone: 'UTC',
      theme: 'light',
      account_tier: 'free',
      subscription_status: 'active',
      process_context: 'recruitment',
      profile_picture_url: user.imageUrl || null,
      bio: null,
      job_title: null,
      phone: null,
      linkedin_url: null,
      website_url: null,
      location: null,
      notification_preferences: {
        email_notifications: true,
        browser_notifications: true,
        workflow_updates: true,
        organization_updates: true,
        security_alerts: true
      },
      privacy_settings: {
        profile_visibility: 'organization',
        show_email: false,
        show_phone: false,
        show_location: true
      },
      last_active: new Date().toISOString(),
      profile_completeness: 25,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      profile: defaultProfile,
      isDefault: true
    })

  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch user profile', 
        details: (error as Error).message 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await currentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - No user session found' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, ...updateData } = body
    console.log("API /user-profile: Received body:", body)
    console.log("API /user-profile: Extracted updateData:", updateData)

    if (action === 'update_profile') {
      // Update user profile using existing service
      const result = await updateEnhancedUserProfile(user.id, updateData)

      if (!result.success) {
        return NextResponse.json(
          { error: 'Failed to update profile', details: result.error },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        profile: result.data,
        message: 'Profile updated successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "update_profile"' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error updating user profile:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update user profile', 
        details: (error as Error).message 
      },
      { status: 500 }
    )
  }
}