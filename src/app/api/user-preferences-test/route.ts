import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'
import { auth } from '@clerk/nextjs/server'

/**
 * API Route: User Preferences Test
 * 
 * Tests the user preferences system database functions and services.
 * Used for validating the migration and ensuring all functionality works correctly.
 */

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID found' },
        { status: 401 }
      )
    }

    const supabase = createClient()
    
    // Test 1: Check if tables exist
    const tablesTest = await testTablesExist(supabase)
    
    // Test 2: Check if database functions work
    const functionsTest = await testDatabaseFunctions(supabase)
    
    // Test 3: Check RLS policies
    const rlsTest = await testRLSPolicies(supabase, userId)
    
    // Test 4: Check indexes performance
    const indexesTest = await testIndexes(supabase)
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      userId,
      tests: {
        tables: tablesTest,
        functions: functionsTest,
        rls: rlsTest,
        indexes: indexesTest
      }
    })
    
  } catch (error) {
    console.error('User preferences test failed:', error)
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID found' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { testType } = body

    const supabase = createClient()

    switch (testType) {
      case 'username-validation':
        return await testUsernameValidation(supabase, body)
      
      case 'domain-detection':
        return await testDomainDetection(supabase, body)
      
      case 'create-preferences':
        return await testCreatePreferences(supabase, userId, body)
      
      case 'update-preferences':
        return await testUpdatePreferences(supabase, userId, body)
      
      default:
        return NextResponse.json(
          { error: 'Invalid test type' },
          { status: 400 }
        )
    }
    
  } catch (error) {
    console.error('User preferences POST test failed:', error)
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function testTablesExist(supabase: any) {
  try {
    // Check if user_preferences table exists and is accessible
    const { data: userPrefsData, error: userPrefsError } = await supabase
      .from('user_preferences')
      .select('count')
      .limit(1)
    
    // Check if org_domains table exists and is accessible
    const { data: orgDomainsData, error: orgDomainsError } = await supabase
      .from('org_domains')
      .select('count')
      .limit(1)
    
    return {
      userPreferences: {
        exists: !userPrefsError,
        error: userPrefsError?.message
      },
      orgDomains: {
        exists: !orgDomainsError,
        error: orgDomainsError?.message
      }
    }
    
  } catch (error) {
    return {
      error: 'Failed to check tables',
      details: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

async function testDatabaseFunctions(supabase: any) {
  try {
    // Test username availability function
    const { data: usernameData, error: usernameError } = await supabase
      .rpc('is_username_available', { check_username: 'test.user.123' })
    
    // Test username suggestion function
    const { data: suggestionData, error: suggestionError } = await supabase
      .rpc('suggest_username', { 
        first_name_param: 'Test', 
        last_name_param: 'User' 
      })
    
    // Test organization detection function
    const { data: orgDetectionData, error: orgDetectionError } = await supabase
      .rpc('detect_org_by_domain', { email_domain: 'example.com' })
    
    return {
      usernameAvailability: {
        works: !usernameError,
        result: usernameData,
        error: usernameError?.message
      },
      usernameSuggestion: {
        works: !suggestionError,
        result: suggestionData,
        error: suggestionError?.message
      },
      orgDetection: {
        works: !orgDetectionError,
        result: orgDetectionData,
        error: orgDetectionError?.message
      }
    }
    
  } catch (error) {
    return {
      error: 'Failed to test database functions',
      details: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

async function testRLSPolicies(supabase: any, userId: string) {
  try {
    // Test user preferences RLS - should only show user's own data
    const { data: prefsData, error: prefsError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
    
    // Test org domains RLS - should work with proper policies
    const { data: domainsData, error: domainsError } = await supabase
      .from('org_domains')
      .select('domain, auto_join_enabled')
      .eq('auto_join_enabled', true)
      .limit(5)
    
    return {
      userPreferences: {
        accessible: !prefsError,
        count: prefsData?.length || 0,
        error: prefsError?.message
      },
      orgDomains: {
        accessible: !domainsError,
        count: domainsData?.length || 0,
        error: domainsError?.message
      }
    }
    
  } catch (error) {
    return {
      error: 'Failed to test RLS policies',
      details: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

async function testIndexes(supabase: any) {
  try {
    // Test username lookup performance (should use index)
    const usernameStart = Date.now()
    const { data: usernameData } = await supabase
      .from('user_preferences')
      .select('username')
      .eq('username', 'nonexistent.user')
      .single()
    const usernameTime = Date.now() - usernameStart
    
    // Test domain lookup performance (should use index)
    const domainStart = Date.now()
    const { data: domainData } = await supabase
      .from('org_domains')
      .select('*')
      .eq('domain', 'example.com')
      .single()
    const domainTime = Date.now() - domainStart
    
    return {
      usernameLookup: {
        timeMs: usernameTime,
        efficient: usernameTime < 100
      },
      domainLookup: {
        timeMs: domainTime,
        efficient: domainTime < 100
      }
    }
    
  } catch (error) {
    return {
      error: 'Failed to test indexes',
      details: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

async function testUsernameValidation(supabase: any, body: any) {
  const { username } = body
  
  if (!username) {
    return NextResponse.json(
      { error: 'Username is required for validation test' },
      { status: 400 }
    )
  }
  
  try {
    const { data, error } = await supabase
      .rpc('is_username_available', { check_username: username })
    
    return NextResponse.json({
      success: true,
      testType: 'username-validation',
      input: { username },
      result: {
        isAvailable: data,
        error: error?.message
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Username validation test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function testDomainDetection(supabase: any, body: any) {
  const { domain } = body
  
  if (!domain) {
    return NextResponse.json(
      { error: 'Domain is required for detection test' },
      { status: 400 }
    )
  }
  
  try {
    const { data, error } = await supabase
      .rpc('detect_org_by_domain', { email_domain: domain })
    
    return NextResponse.json({
      success: true,
      testType: 'domain-detection',
      input: { domain },
      result: {
        organizations: data,
        error: error?.message
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Domain detection test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function testCreatePreferences(supabase: any, userId: string, body: any) {
  const { firstName, lastName, username, email } = body
  
  if (!firstName || !lastName || !username) {
    return NextResponse.json(
      { error: 'firstName, lastName, and username are required' },
      { status: 400 }
    )
  }
  
  try {
    // Create test preferences (will be cleaned up)
    const testUserId = `test_${userId}_${Date.now()}`
    
    const { data, error } = await supabase
      .from('user_preferences')
      .insert({
        user_id: testUserId,
        first_name: firstName,
        last_name: lastName,
        username: `test_${username}_${Date.now()}`,
        theme: 'system',
        timezone: 'America/Chicago',
        process_context: 'both'
      })
      .select()
      .single()
    
    // Clean up test data
    if (data) {
      await supabase
        .from('user_preferences')
        .delete()
        .eq('id', data.id)
    }
    
    return NextResponse.json({
      success: true,
      testType: 'create-preferences',
      result: {
        created: !!data,
        error: error?.message
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Create preferences test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function testUpdatePreferences(supabase: any, userId: string, body: any) {
  // This test requires existing user preferences
  try {
    const { data: existing } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (!existing) {
      return NextResponse.json({
        success: true,
        testType: 'update-preferences',
        result: {
          message: 'No existing preferences to update - this is expected for new users'
        }
      })
    }
    
    // Test update functionality
    const { data, error } = await supabase
      .from('user_preferences')
      .update({ updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .select()
      .single()
    
    return NextResponse.json({
      success: true,
      testType: 'update-preferences',
      result: {
        updated: !!data,
        error: error?.message
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Update preferences test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
