
import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { auth } from '@clerk/nextjs/server';

export async function GET(req: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const { data: userPreferences, error: userError } = await supabaseAdmin
      .from('user_preferences')
      .select('timezone, org_id')
      .eq('user_id', userId)
      .single();

    if (userError && userError.code !== 'PGRST116') { // Ignore 'single row not found' error
      throw userError;
    }

    if (userPreferences?.timezone) {
      return NextResponse.json({ timezone: userPreferences.timezone });
    }

    if (userPreferences?.org_id) {
      const { data: org, error: orgError } = await supabaseAdmin
        .from('organizations')
        .select('default_timezone')
        .eq('id', userPreferences.org_id)
        .single();

      if (orgError) {
        throw orgError;
      }

      if (org?.default_timezone) {
        return NextResponse.json({ timezone: org.default_timezone });
      }
    }

    return NextResponse.json({ timezone: 'America/New_York' });
  } catch (error) {
    console.error('Error fetching timezone:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const { timezone } = await req.json();

    const { error } = await supabaseAdmin
      .from('user_preferences')
      .update({ timezone })
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return NextResponse.json({ message: 'Timezone updated successfully' });
  } catch (error) {
    console.error('Error updating timezone:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
