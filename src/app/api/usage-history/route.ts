
import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { auth } from '@clerk/nextjs/server';

export async function GET(req: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // TODO: Fetch usage history from the database
    const usageHistory = [
      {
        date: '2025-06-27',
        action: 'Created a new profile',
      },
      {
        date: '2025-06-26',
        action: 'Updated a profile',
      },
    ];

    return NextResponse.json(usageHistory);
  } catch (error) {
    console.error('Error fetching usage history:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
