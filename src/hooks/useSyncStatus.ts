/**
 * useSyncStatus Hook
 * 
 * React hook for real-time sync status management in TalentHUB.
 * Provides sync progress tracking, error state management, retry status
 * indicators, and user feedback mechanisms.
 * 
 * Features:
 * - Real-time sync status updates
 * - Progress tracking for batch operations
 * - Error state management with user feedback
 * - Retry status indicators
 * - Connection status monitoring
 * - Background sync coordination
 */

'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useUser } from '@clerk/nextjs'
import { 
  getSyncService, 
  syncUserPreferences, 
  syncOrganizationData, 
  trackUsageEvent,
  type SyncStatusUpdate,
  type SyncProgress,
  type SyncOperation
} from '@/lib/services/data-sync'
import { 
  getRetryService, 
  retryOperation,
  type RetryResult
} from '@/lib/sync/retry-logic'
import { classifyError } from '@/lib/error-handling/error-boundaries'

export interface SyncStatus {
  isOnline: boolean
  isSyncing: boolean
  lastSyncTime: number | null
  queueSize: number
  activeOperations: number
  progress: SyncProgress
  errors: Array<{
    operation: SyncOperation
    error: string
    timestamp: number
  }>
  retryCount: number
  canRetry: boolean
}

export interface SyncActions {
  syncNow: () => Promise<void>
  retryFailed: () => Promise<void>
  clearErrors: () => void
  cancelSync: () => void
  pauseSync: () => void
  resumeSync: () => void
}

export interface UseSyncStatusOptions {
  autoSync?: boolean
  syncInterval?: number
  retryFailedOnMount?: boolean
  trackUsage?: boolean
}

export interface UseSyncStatusReturn {
  status: SyncStatus
  actions: SyncActions
  syncUserData: (data: any, priority?: 'immediate' | 'batched') => Promise<string>
  syncOrgData: (orgId: string, data: any) => Promise<string>
  trackEvent: (eventType: string, metadata?: any) => Promise<string>
}

/**
 * Main Sync Status Hook
 */
export function useSyncStatus(options: UseSyncStatusOptions = {}): UseSyncStatusReturn {
  const {
    autoSync = true,
    syncInterval = 30000, // 30 seconds
    retryFailedOnMount = true,
    trackUsage = true
  } = options

  const { user } = useUser()
  const [status, setStatus] = useState<SyncStatus>({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isSyncing: false,
    lastSyncTime: null,
    queueSize: 0,
    activeOperations: 0,
    progress: { total: 0, completed: 0, failed: 0, pending: 0, percentage: 100 },
    errors: [],
    retryCount: 0,
    canRetry: true
  })

  const [isPaused, setIsPaused] = useState(false)
  const syncService = useRef(getSyncService())
  const retryService = useRef(getRetryService())
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const unsubscribeRef = useRef<(() => void) | null>(null)

  /**
   * Handle sync status updates from service
   */
  const handleStatusUpdate = useCallback((update: SyncStatusUpdate) => {
    setStatus(prevStatus => ({
      ...prevStatus,
      queueSize: update.queueSize,
      activeOperations: update.activeOperations,
      lastSyncTime: update.lastSyncTime || prevStatus.lastSyncTime,
      progress: update.progress,
      errors: update.errors,
      isSyncing: update.activeOperations > 0
    }))
  }, [])

  /**
   * Handle online/offline status
   */
  const handleOnlineStatus = useCallback(() => {
    setStatus(prev => ({ ...prev, isOnline: navigator.onLine }))
    
    // Auto-retry when coming back online
    if (navigator.onLine && autoSync && !isPaused) {
      setTimeout(() => {
        retryFailed()
      }, 1000)
    }
  }, [autoSync, isPaused])

  /**
   * Sync user preferences
   */
  const syncUserData = useCallback(async (
    data: any, 
    priority: 'immediate' | 'batched' = 'batched'
  ): Promise<string> => {
    if (!user?.id) {
      throw new Error('User not authenticated')
    }

    try {
      const operationId = await syncUserPreferences(user.id, data, priority)
      
      if (trackUsage) {
        trackUsageEvent(user.id, 'user_preferences_sync', { priority })
      }
      
      return operationId
    } catch (error) {
      const appError = classifyError(error instanceof Error ? error : new Error(String(error)))
      console.error('Failed to sync user data:', appError)
      throw error
    }
  }, [user?.id, trackUsage])

  /**
   * Sync organization data
   */
  const syncOrgData = useCallback(async (orgId: string, data: any): Promise<string> => {
    if (!user?.id) {
      throw new Error('User not authenticated')
    }

    try {
      const operationId = await syncOrganizationData(user.id, orgId, data)
      
      if (trackUsage) {
        trackUsageEvent(user.id, 'organization_sync', { orgId })
      }
      
      return operationId
    } catch (error) {
      const appError = classifyError(error instanceof Error ? error : new Error(String(error)))
      console.error('Failed to sync organization data:', appError)
      throw error
    }
  }, [user?.id, trackUsage])

  /**
   * Track usage event
   */
  const trackEvent = useCallback(async (
    eventType: string, 
    metadata: any = {}
  ): Promise<string> => {
    if (!user?.id) {
      throw new Error('User not authenticated')
    }

    try {
      return await trackUsageEvent(user.id, eventType, metadata)
    } catch (error) {
      const appError = classifyError(error instanceof Error ? error : new Error(String(error)))
      console.error('Failed to track event:', appError)
      throw error
    }
  }, [user?.id])

  /**
   * Force sync now
   */
  const syncNow = useCallback(async (): Promise<void> => {
    if (!user?.id || isPaused) {
      return
    }

    setStatus(prev => ({ ...prev, isSyncing: true }))

    try {
      // Force process any pending operations
      await retryOperation(
        async () => {
          // This will trigger processing of pending operations
          await new Promise(resolve => setTimeout(resolve, 100))
        },
        { maxRetries: 1, priority: 'high' }
      )
    } catch (error) {
      console.error('Sync now failed:', error)
    } finally {
      setStatus(prev => ({ ...prev, isSyncing: prev.activeOperations > 0 }))
    }
  }, [user?.id, isPaused])

  /**
   * Retry failed operations
   */
  const retryFailed = useCallback(async (): Promise<void> => {
    if (!status.canRetry || isPaused) {
      return
    }

    setStatus(prev => ({ 
      ...prev, 
      retryCount: prev.retryCount + 1,
      canRetry: false 
    }))

    try {
      await syncService.current.retryFailedOperations()
      
      // Re-enable retry after delay
      setTimeout(() => {
        setStatus(prev => ({ ...prev, canRetry: true }))
      }, 5000)

    } catch (error) {
      console.error('Retry failed operations error:', error)
      setStatus(prev => ({ ...prev, canRetry: true }))
    }
  }, [status.canRetry, isPaused])

  /**
   * Clear error list
   */
  const clearErrors = useCallback((): void => {
    syncService.current.clearCompleted()
    setStatus(prev => ({ ...prev, errors: [] }))
  }, [])

  /**
   * Cancel all sync operations
   */
  const cancelSync = useCallback((): void => {
    // Implementation would depend on sync service cancellation support
    setStatus(prev => ({ ...prev, isSyncing: false }))
  }, [])

  /**
   * Pause auto-sync
   */
  const pauseSync = useCallback((): void => {
    setIsPaused(true)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  /**
   * Resume auto-sync
   */
  const resumeSync = useCallback((): void => {
    setIsPaused(false)
    if (autoSync) {
      startAutoSync()
    }
  }, [autoSync])

  /**
   * Start auto-sync interval
   */
  const startAutoSync = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    intervalRef.current = setInterval(() => {
      if (!isPaused && status.isOnline) {
        syncNow()
      }
    }, syncInterval)
  }, [syncInterval, isPaused, status.isOnline, syncNow])

  /**
   * Initialize sync monitoring
   */
  useEffect(() => {
    // Subscribe to sync status updates
    unsubscribeRef.current = syncService.current.subscribe(handleStatusUpdate)

    // Set up online/offline listeners
    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnlineStatus)
      window.addEventListener('offline', handleOnlineStatus)
    }

    // Start auto-sync if enabled
    if (autoSync && !isPaused) {
      startAutoSync()
    }

    // Retry failed operations on mount if requested
    if (retryFailedOnMount && status.isOnline) {
      setTimeout(retryFailed, 1000)
    }

    return () => {
      // Cleanup
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnlineStatus)
        window.removeEventListener('offline', handleOnlineStatus)
      }
    }
  }, [autoSync, retryFailedOnMount, handleStatusUpdate, handleOnlineStatus, startAutoSync, retryFailed, isPaused, status.isOnline])

  /**
   * Update online status on mount
   */
  useEffect(() => {
    if (typeof navigator !== 'undefined') {
      setStatus(prev => ({ ...prev, isOnline: navigator.onLine }))
    }
  }, [])

  return {
    status,
    actions: {
      syncNow,
      retryFailed,
      clearErrors,
      cancelSync,
      pauseSync,
      resumeSync
    },
    syncUserData,
    syncOrgData,
    trackEvent
  }
}

/**
 * Hook for simplified sync operations
 */
export function useSimpleSync() {
  const { user } = useUser()
  
  const syncUserPrefs = useCallback(async (preferences: any) => {
    if (!user?.id) return

    try {
      return await syncUserPreferences(user.id, preferences, 'batched')
    } catch (error) {
      console.error('Failed to sync preferences:', error)
    }
  }, [user?.id])

  const trackAction = useCallback(async (action: string, data?: any) => {
    if (!user?.id) return

    try {
      return await trackUsageEvent(user.id, action, data)
    } catch (error) {
      console.error('Failed to track action:', error)
    }
  }, [user?.id])

  return {
    syncUserPrefs,
    trackAction
  }
}

/**
 * Hook for sync health monitoring
 */
export function useSyncHealth() {
  const [health, setHealth] = useState({
    healthy: true,
    successRate: 100,
    openCircuits: 0,
    activeOperations: 0,
    queueSize: 0
  })

  const retryService = useRef(getRetryService())

  useEffect(() => {
    const checkHealth = () => {
      const healthStatus = retryService.current.getHealthStatus()
      setHealth(healthStatus)
    }

    // Check health immediately
    checkHealth()

    // Set up periodic health checks
    const interval = setInterval(checkHealth, 10000) // Every 10 seconds

    return () => clearInterval(interval)
  }, [])

  return health
}

/**
 * Hook for sync metrics
 */
export function useSyncMetrics() {
  const [metrics, setMetrics] = useState({
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    retriedOperations: 0,
    activeOperations: 0,
    queueSize: 0,
    circuitBreakers: []
  })

  const retryService = useRef(getRetryService())
  const syncService = useRef(getSyncService())

  const refreshMetrics = useCallback(() => {
    const retryMetrics = retryService.current.getMetrics()
    const syncStatus = syncService.current.getCurrentStatus()
    
    setMetrics({
      ...retryMetrics,
      queueSize: syncStatus.queueSize,
      activeOperations: syncStatus.activeOperations
    })
  }, [])

  const resetMetrics = useCallback(() => {
    retryService.current.resetMetrics()
    refreshMetrics()
  }, [refreshMetrics])

  useEffect(() => {
    // Refresh metrics on mount
    refreshMetrics()

    // Set up periodic refresh
    const interval = setInterval(refreshMetrics, 5000) // Every 5 seconds

    return () => clearInterval(interval)
  }, [refreshMetrics])

  return {
    metrics,
    refreshMetrics,
    resetMetrics
  }
}

/**
 * Hook for background sync management
 */
export function useBackgroundSync(enabled: boolean = true) {
  const { status, actions } = useSyncStatus({
    autoSync: enabled,
    syncInterval: 30000,
    retryFailedOnMount: true,
    trackUsage: false // Don't track background sync usage
  })

  // Expose minimal interface for background sync
  return {
    isSyncing: status.isSyncing,
    hasErrors: status.errors.length > 0,
    isOnline: status.isOnline,
    retryFailed: actions.retryFailed,
    pause: actions.pauseSync,
    resume: actions.resumeSync
  }
}

/**
 * Context provider for sync status (optional)
 */
import React, { createContext, useContext } from 'react'

const SyncContext = createContext<UseSyncStatusReturn | null>(null)

export function SyncProvider({ 
  children, 
  options = {} 
}: { 
  children: React.ReactNode
  options?: UseSyncStatusOptions 
}) {
  const syncStatus = useSyncStatus(options)

  return (
    <SyncContext.Provider value={syncStatus}>
      {children}
    </SyncContext.Provider>
  )
}

export function useSyncContext(): UseSyncStatusReturn {
  const context = useContext(SyncContext)
  if (!context) {
    throw new Error('useSyncContext must be used within a SyncProvider')
  }
  return context
}

export default useSyncStatus