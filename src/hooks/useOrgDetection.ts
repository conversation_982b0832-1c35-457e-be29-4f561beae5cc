import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@clerk/nextjs'
import {
  detectOrganizationWithRequests,
  createJoinRequest,
  getUserJoinRequests,
  cancelJoinRequest,
  autoJoinOrganization,
  checkAutoJoinEligibility,
  type OrganizationDetectionWithRequests,
  type JoinRequestWithOrg
} from '@/lib/services/org-association'
import { 
  validateOrganizationAccess,
  getUpgradeRequirement,
  type AccountTier,
  type TierValidationResult 
} from '@/lib/services/tier-gating'
import { getEnhancedUserProfile } from '@/lib/services/user-preferences'

/**
 * Organization Detection Hook
 * 
 * Provides organization detection functionality with join request management.
 * Handles auto-detection on signup/login, join request workflows, and real-time updates.
 * 
 * Features:
 * - Auto-detection based on user email
 * - Join request creation and management
 * - Auto-join for verified domains
 * - Real-time status updates
 * - Loading and error states
 */

export interface UseOrgDetectionState {
  // Detection state
  isDetecting: boolean
  detectionResult: OrganizationDetectionWithRequests | null
  detectionError: string | null
  
  // Join requests state
  joinRequests: JoinRequestWithOrg[]
  isLoadingRequests: boolean
  requestsError: string | null
  
  // Actions state
  isCreatingRequest: boolean
  isCancelingRequest: boolean
  isAutoJoining: boolean
  actionError: string | null
  
  // Tier-related state
  userAccountTier: AccountTier | null
  tierValidation: TierValidationResult | null
  upgradeRequired: boolean
  isLoadingTier: boolean
}

export interface UseOrgDetectionActions {
  // Detection actions
  detectOrganization: (email?: string) => Promise<void>
  clearDetection: () => void
  
  // Join request actions
  createJoinRequest: (organizationId: string, message?: string) => Promise<boolean>
  cancelJoinRequest: (requestId: string) => Promise<boolean>
  refreshJoinRequests: () => Promise<void>
  
  // Auto-join actions
  attemptAutoJoin: (email?: string) => Promise<boolean>
  checkEligibility: (email?: string) => Promise<{
    eligible: boolean
    organizationId?: string
    requiresApproval: boolean
  }>
}

export interface UseOrgDetectionOptions {
  autoDetectOnMount?: boolean
  autoJoinIfEligible?: boolean
  refreshInterval?: number
}

/**
 * Hook for organization detection and join workflow management
 */
export function useOrgDetection(options: UseOrgDetectionOptions = {}) {
  const {
    autoDetectOnMount = true,
    autoJoinIfEligible = false,
    refreshInterval = 30000 // 30 seconds
  } = options
  
  const { user, isLoaded: userLoaded } = useUser()
  
  // State management
  const [state, setState] = useState<UseOrgDetectionState>({
    isDetecting: false,
    detectionResult: null,
    detectionError: null,
    joinRequests: [],
    isLoadingRequests: false,
    requestsError: null,
    isCreatingRequest: false,
    isCancelingRequest: false,
    isAutoJoining: false,
    actionError: null,
    userAccountTier: null,
    tierValidation: null,
    upgradeRequired: false,
    isLoadingTier: false
  })

  /**
   * Loads user account tier and preferences
   */
  const loadUserTier = useCallback(async () => {
    if (!user?.id) return

    setState(prev => ({ ...prev, isLoadingTier: true }))

    try {
      const preferences = await getEnhancedUserProfile(user.id)
      const accountTier = (preferences?.accountTier as AccountTier) || 'free'
      
      setState(prev => ({
        ...prev,
        userAccountTier: accountTier,
        isLoadingTier: false
      }))
      
      return accountTier
    } catch (error) {
      console.error('Failed to load user tier:', error)
      setState(prev => ({
        ...prev,
        userAccountTier: 'free',
        isLoadingTier: false
      }))
      return 'free' as AccountTier
    }
  }, [user?.id])
  
  /**
   * Detects organization for given email or user's primary email with tier validation
   */
  const detectOrganization = useCallback(async (email?: string) => {
    if (!user?.id) return
    
    const targetEmail = email || user.primaryEmailAddress?.emailAddress
    if (!targetEmail) return

    setState(prev => ({
      ...prev,
      isDetecting: true,
      detectionError: null,
      actionError: null
    }))

    try {
      // Load user tier if not already loaded
      let accountTier = state.userAccountTier
      if (!accountTier) {
        accountTier = await loadUserTier()
      }

      const result = await detectOrganizationWithRequests(targetEmail, user.id, accountTier)
      
      setState(prev => ({
        ...prev,
        isDetecting: false,
        detectionResult: result,
        detectionError: null,
        tierValidation: result.tierValidation || null,
        upgradeRequired: result.upgradeRequired || false
      }))
      
    } catch (error) {
      console.error('Organization detection failed:', error)
      setState(prev => ({
        ...prev,
        isDetecting: false,
        detectionError: 'Failed to detect organization'
      }))
    }
  }, [user?.id, user?.primaryEmailAddress?.emailAddress, state.userAccountTier, loadUserTier])
  
  /**
   * Clears detection results
   */
  const clearDetection = useCallback(() => {
    setState(prev => ({
      ...prev,
      detectionResult: null,
      detectionError: null
    }))
  }, [])
  
  /**
   * Creates a join organization request with tier validation
   */
  const createJoinRequestAction = useCallback(async (
    organizationId: string,
    message?: string
  ): Promise<boolean> => {
    if (!user?.id || !user?.primaryEmailAddress?.emailAddress) return false
    
    setState(prev => ({
      ...prev,
      isCreatingRequest: true,
      actionError: null
    }))
    
    try {
      // Ensure we have the user's account tier
      let accountTier = state.userAccountTier
      if (!accountTier) {
        accountTier = await loadUserTier()
      }

      const result = await createJoinRequest(
        user.id,
        organizationId,
        user.primaryEmailAddress.emailAddress,
        accountTier,
        'manual_request',
        message
      )
      
      setState(prev => ({
        ...prev,
        isCreatingRequest: false,
        tierValidation: result.tierValidation || null,
        upgradeRequired: result.upgradeRequired || false
      }))
      
      if (result.success) {
        // Refresh detection and requests after successful creation
        await Promise.all([
          detectOrganization(),
          refreshJoinRequests()
        ])
        return true
      } else {
        setState(prev => ({
          ...prev,
          actionError: result.error || 'Failed to create join request'
        }))
        return false
      }
      
    } catch (error) {
      console.error('Create join request failed:', error)
      setState(prev => ({
        ...prev,
        isCreatingRequest: false,
        actionError: 'An unexpected error occurred'
      }))
      return false
    }
  }, [user?.id, user?.primaryEmailAddress?.emailAddress, state.userAccountTier, loadUserTier, detectOrganization])

  /**
   * Cancels a join request
   */
  const cancelJoinRequestAction = useCallback(async (requestId: string): Promise<boolean> => {
    if (!user?.id) return false
    
    setState(prev => ({
      ...prev,
      isCancelingRequest: true,
      actionError: null
    }))
    
    try {
      const result = await cancelJoinRequest(requestId, user.id)
      
      setState(prev => ({
        ...prev,
        isCancelingRequest: false
      }))
      
      if (result.success) {
        // Refresh detection and requests after cancellation
        await Promise.all([
          detectOrganization(),
          refreshJoinRequests()
        ])
        return true
      } else {
        setState(prev => ({
          ...prev,
          actionError: result.error || 'Failed to cancel join request'
        }))
        return false
      }
      
    } catch (error) {
      console.error('Cancel join request failed:', error)
      setState(prev => ({
        ...prev,
        isCancelingRequest: false,
        actionError: 'An unexpected error occurred'
      }))
      return false
    }
  }, [user?.id, detectOrganization])
  
  /**
   * Refreshes user's join requests
   */
  const refreshJoinRequests = useCallback(async () => {
    if (!user?.id) return
    
    setState(prev => ({
      ...prev,
      isLoadingRequests: true,
      requestsError: null
    }))
    
    try {
      const requests = await getUserJoinRequests(user.id)
      
      setState(prev => ({
        ...prev,
        isLoadingRequests: false,
        joinRequests: requests,
        requestsError: null
      }))
      
    } catch (error) {
      console.error('Refresh join requests failed:', error)
      setState(prev => ({
        ...prev,
        isLoadingRequests: false,
        requestsError: 'Failed to load join requests'
      }))
    }
  }, [user?.id])
  
  /**
   * Attempts auto-join if user is eligible
   */
  const attemptAutoJoin = useCallback(async (email?: string): Promise<boolean> => {
    if (!user?.id) return false
    
    const targetEmail = email || user.primaryEmailAddress?.emailAddress
    if (!targetEmail) return false
    
    setState(prev => ({
      ...prev,
      isAutoJoining: true,
      actionError: null
    }))
    
    try {
      const result = await autoJoinOrganization(user.id, targetEmail)
      
      setState(prev => ({
        ...prev,
        isAutoJoining: false
      }))
      
      if (result.success && result.joined) {
        // Refresh detection after successful auto-join
        await detectOrganization(targetEmail)
        return true
      }
      
      return false
      
    } catch (error) {
      console.error('Auto-join failed:', error)
      setState(prev => ({
        ...prev,
        isAutoJoining: false,
        actionError: 'Auto-join failed'
      }))
      return false
    }
  }, [user?.id, user?.primaryEmailAddress?.emailAddress, detectOrganization])
  
  /**
   * Checks auto-join eligibility
   */
  const checkEligibility = useCallback(async (email?: string) => {
    if (!user?.id) return { eligible: false, requiresApproval: false }
    
    const targetEmail = email || user.primaryEmailAddress?.emailAddress
    if (!targetEmail) return { eligible: false, requiresApproval: false }
    
    try {
      return await checkAutoJoinEligibility(targetEmail, user.id)
    } catch (error) {
      console.error('Eligibility check failed:', error)
      return { eligible: false, requiresApproval: false }
    }
  }, [user?.id, user?.primaryEmailAddress?.emailAddress])
  
  // Load user tier on mount
  useEffect(() => {
    if (userLoaded && user?.id && !state.userAccountTier && !state.isLoadingTier) {
      loadUserTier()
    }
  }, [userLoaded, user?.id, state.userAccountTier, state.isLoadingTier, loadUserTier])

  // Auto-detect on mount
  useEffect(() => {
    if (userLoaded && user?.id && autoDetectOnMount) {
      detectOrganization()
      refreshJoinRequests()
    }
  }, [userLoaded, user?.id, autoDetectOnMount, detectOrganization, refreshJoinRequests])
  
  // Auto-join if eligible and enabled
  useEffect(() => {
    if (
      autoJoinIfEligible &&
      state.detectionResult?.found &&
      state.detectionResult.organization?.autoJoinEnabled &&
      !state.detectionResult.hasPendingRequest
    ) {
      attemptAutoJoin()
    }
  }, [
    autoJoinIfEligible,
    state.detectionResult,
    attemptAutoJoin
  ])
  
  // Periodic refresh of join requests
  useEffect(() => {
    if (!refreshInterval || !user?.id) return
    
    const interval = setInterval(() => {
      refreshJoinRequests()
    }, refreshInterval)
    
    return () => clearInterval(interval)
  }, [refreshInterval, user?.id, refreshJoinRequests])
  
  // Return state and actions
  const actions: UseOrgDetectionActions = {
    detectOrganization,
    clearDetection,
    createJoinRequest: createJoinRequestAction,
    cancelJoinRequest: cancelJoinRequestAction,
    refreshJoinRequests,
    attemptAutoJoin,
    checkEligibility
  }
  
  return {
    ...state,
    ...actions
  }
}

/**
 * Simplified hook for basic organization detection
 */
export function useBasicOrgDetection(email?: string) {
  const fullHook = useOrgDetection({
    autoDetectOnMount: !!email,
    autoJoinIfEligible: false,
    refreshInterval: 0 // Disable auto-refresh for basic usage
  })
  
  useEffect(() => {
    if (email) {
      fullHook.detectOrganization(email)
    }
  }, [email])
  
  return {
    isDetecting: fullHook.isDetecting,
    detectionResult: fullHook.detectionResult,
    detectionError: fullHook.detectionError,
    detectOrganization: fullHook.detectOrganization,
    clearDetection: fullHook.clearDetection
  }
}
