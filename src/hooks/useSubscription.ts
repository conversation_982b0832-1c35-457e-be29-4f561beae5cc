/**
 * useSubscription Hook
 * 
 * React hook for managing subscription state, integrating <PERSON> <PERSON>
 * with TalentHUB's existing user preferences system. Provides real-time
 * subscription status, usage tracking, and upgrade workflows.
 * 
 * Features:
 * - Real-time subscription status
 * - Usage statistics and limits
 * - Upgrade recommendations
 * - Billing portal integration
 * - Feature access checking
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@clerk/nextjs'
import { 
  getSubscriptionStatus,
  canPerformAction,
  recordUsage,
  syncSubscriptionStatus,
  getBillingPortalUrl,
  validateSubscriptionAccess,
  getUsageSummary,
  type EnhancedSubscriptionStatus,
  type UsageStats,
  type UpgradeRequirement
} from '@/lib/services/subscription'
import { 
  hasFeatureAccess,
  hasPlanAccess,
  CLERK_FEATURE_SLUGS,
  CLERK_PLAN_SLUGS
} from '@/lib/billing/clerk-integration'
import type { AccountTier } from '@/lib/services/tier-gating'

export interface UseSubscriptionReturn {
  // Status
  subscription: EnhancedSubscriptionStatus | null
  isLoading: boolean
  error: string | null
  
  // Usage
  usage: UsageStats | null
  usageSummary: any
  isNearingLimits: boolean
  
  // Actions
  checkFeatureAccess: (feature: string) => Promise<boolean>
  checkPlanAccess: (plan: string) => Promise<boolean>
  canPerform: (action: string, currentUsage?: number) => Promise<{ allowed: boolean; reason?: string; upgradeRequired?: boolean }>
  recordUserUsage: (type: string, amount?: number, metadata?: any) => Promise<boolean>
  
  // Billing
  openBillingPortal: () => Promise<void>
  refreshSubscription: () => Promise<void>
  
  // Upgrade flow
  upgradeRequirement: UpgradeRequirement | null
  shouldShowUpgrade: boolean
  
  // Utilities
  hasFeature: (feature: string) => boolean
  hasPlan: (plan: string) => boolean
  isOnTier: (tier: AccountTier) => boolean
}

/**
 * Main subscription management hook
 */
export function useSubscription(): UseSubscriptionReturn {
  const { user, isLoaded } = useUser()
  const [subscription, setSubscription] = useState<EnhancedSubscriptionStatus | null>(null)
  const [usage, setUsage] = useState<UsageStats | null>(null)
  const [usageSummary, setUsageSummary] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  /**
   * Loads subscription status and usage data
   */
  const loadSubscriptionData = useCallback(async () => {
    if (!user?.id || !isLoaded) return

    try {
      setIsLoading(true)
      setError(null)

      // Load subscription status
      const subscriptionData = await getSubscriptionStatus(user.id)
      setSubscription(subscriptionData)
      setUsage(subscriptionData.usageStats)

      // Load usage summary
      const summary = await getUsageSummary(user.id)
      setUsageSummary(summary)

      // Sync with Supabase
      await syncSubscriptionStatus(user.id)

    } catch (err) {
      console.error('Error loading subscription data:', err)
      setError('Failed to load subscription information')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, isLoaded])

  // Load data on mount and user change
  useEffect(() => {
    loadSubscriptionData()
  }, [loadSubscriptionData])

  /**
   * Checks if user has access to a specific feature
   */
  const checkFeatureAccess = useCallback(async (feature: string): Promise<boolean> => {
    try {
      return await hasFeatureAccess(feature)
    } catch (error) {
      console.error('Error checking feature access:', error)
      return false
    }
  }, [])

  /**
   * Checks if user has access to a specific plan
   */
  const checkPlanAccess = useCallback(async (plan: string): Promise<boolean> => {
    try {
      return await hasPlanAccess(plan)
    } catch (error) {
      console.error('Error checking plan access:', error)
      return false
    }
  }, [])

  /**
   * Checks if user can perform a specific action
   */
  const canPerform = useCallback(async (
    action: string, 
    currentUsage?: number
  ): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: boolean }> => {
    try {
      return await canPerformAction(action as any, currentUsage)
    } catch (error) {
      console.error('Error checking action permission:', error)
      return { allowed: false, reason: 'Unable to verify permissions' }
    }
  }, [])

  /**
   * Records usage for tracking and billing
   */
  const recordUserUsage = useCallback(async (
    type: string,
    amount: number = 1,
    metadata?: any
  ): Promise<boolean> => {
    if (!user?.id) return false

    try {
      const result = await recordUsage(user.id, type as any, amount, metadata)
      
      if (result.success) {
        // Refresh usage data after recording
        await loadSubscriptionData()
        return true
      }
      
      return false
    } catch (error) {
      console.error('Error recording usage:', error)
      return false
    }
  }, [user?.id, loadSubscriptionData])

  /**
   * Opens billing portal for subscription management
   */
  const openBillingPortal = useCallback(async () => {
    try {
      const portalUrl = await getBillingPortalUrl()
      if (portalUrl) {
        window.open(portalUrl, '_blank')
      }
    } catch (error) {
      console.error('Error opening billing portal:', error)
    }
  }, [])

  /**
   * Refreshes subscription data
   */
  const refreshSubscription = useCallback(async () => {
    await loadSubscriptionData()
  }, [loadSubscriptionData])

  // Derived state
  const upgradeRequirement = subscription?.upgradeRequirement || null
  const isNearingLimits = usageSummary?.nearingLimits || false
  const shouldShowUpgrade = Boolean(
    upgradeRequirement || 
    isNearingLimits || 
    subscription?.talentHubTier === 'free'
  )

  /**
   * Quick feature check (cached from subscription data)
   */
  const hasFeature = useCallback((feature: string): boolean => {
    return subscription?.features.includes(feature) || false
  }, [subscription?.features])

  /**
   * Quick plan check (cached from subscription data)
   */
  const hasPlan = useCallback((plan: string): boolean => {
    return subscription?.planSlug === plan
  }, [subscription?.planSlug])

  /**
   * Checks if user is on specific tier
   */
  const isOnTier = useCallback((tier: AccountTier): boolean => {
    return subscription?.talentHubTier === tier
  }, [subscription?.talentHubTier])

  return {
    // Status
    subscription,
    isLoading,
    error,
    
    // Usage
    usage,
    usageSummary,
    isNearingLimits,
    
    // Actions
    checkFeatureAccess,
    checkPlanAccess,
    canPerform,
    recordUserUsage,
    
    // Billing
    openBillingPortal,
    refreshSubscription,
    
    // Upgrade flow
    upgradeRequirement,
    shouldShowUpgrade,
    
    // Utilities
    hasFeature,
    hasPlan,
    isOnTier
  }
}

/**
 * Hook for feature-specific access checking
 */
export function useFeatureAccess(featureSlug: string) {
  const { hasFeature, checkFeatureAccess, subscription, isLoading } = useSubscription()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [checking, setChecking] = useState(true)

  useEffect(() => {
    const checkAccess = async () => {
      if (!subscription || isLoading) return

      setChecking(true)
      try {
        // Use cached data if available, otherwise check with Clerk
        const access = hasFeature(featureSlug) || await checkFeatureAccess(featureSlug)
        setHasAccess(access)
      } catch (error) {
        console.error('Error checking feature access:', error)
        setHasAccess(false)
      } finally {
        setChecking(false)
      }
    }

    checkAccess()
  }, [featureSlug, hasFeature, checkFeatureAccess, subscription, isLoading])

  return {
    hasAccess: hasAccess ?? false,
    isLoading: checking || isLoading,
    subscription
  }
}

/**
 * Hook for usage tracking with automatic limits checking
 */
export function useUsageTracking(usageType: string) {
  const { recordUserUsage, usage, canPerform } = useSubscription()
  const [isTracking, setIsTracking] = useState(false)

  const trackUsage = useCallback(async (
    amount: number = 1,
    metadata?: any
  ): Promise<{ success: boolean; blocked?: boolean; reason?: string }> => {
    setIsTracking(true)
    
    try {
      // Check if action is allowed first
      const permission = await canPerform(usageType, usage?.[usageType as keyof typeof usage]?.current)
      
      if (!permission.allowed) {
        return {
          success: false,
          blocked: true,
          reason: permission.reason
        }
      }

      // Record the usage
      const success = await recordUserUsage(usageType, amount, metadata)
      
      return { success }
      
    } catch (error) {
      console.error('Error tracking usage:', error)
      return { success: false, reason: 'Failed to track usage' }
    } finally {
      setIsTracking(false)
    }
  }, [recordUserUsage, canPerform, usage, usageType])

  const getCurrentUsage = useCallback(() => {
    if (!usage) return null
    return usage[usageType as keyof typeof usage] || null
  }, [usage, usageType])

  return {
    trackUsage,
    isTracking,
    currentUsage: getCurrentUsage()
  }
}

/**
 * Hook for upgrade recommendations and flows
 */
export function useUpgradeFlow() {
  const { 
    subscription, 
    upgradeRequirement, 
    shouldShowUpgrade,
    isNearingLimits,
    refreshSubscription
  } = useSubscription()
  
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [upgradeContext, setUpgradeContext] = useState<string>('')

  const triggerUpgrade = useCallback((context: string = 'general') => {
    setUpgradeContext(context)
    setShowUpgradeModal(true)
  }, [])

  const closeUpgrade = useCallback(() => {
    setShowUpgradeModal(false)
    setUpgradeContext('')
  }, [])

  const handleUpgradeSuccess = useCallback(async () => {
    // Refresh subscription data after successful upgrade
    await refreshSubscription()
    closeUpgrade()
  }, [refreshSubscription, closeUpgrade])

  // Auto-trigger upgrade prompts based on usage
  useEffect(() => {
    if (isNearingLimits && subscription?.talentHubTier === 'free') {
      // Could auto-show upgrade prompt for free users hitting limits
      // triggerUpgrade('usage_limit')
    }
  }, [isNearingLimits, subscription?.talentHubTier])

  return {
    // State
    showUpgradeModal,
    upgradeContext,
    upgradeRequirement,
    shouldShowUpgrade,
    isNearingLimits,
    
    // Actions
    triggerUpgrade,
    closeUpgrade,
    handleUpgradeSuccess,
    
    // Data
    currentTier: subscription?.talentHubTier || 'free',
    subscription
  }
}
