'use client'

import { ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Crown, Lock, AlertTriangle } from 'lucide-react'
import { 
  hasAdminCapability,
  validateOrganizationAccess,
  type AccountTier 
} from '@/lib/services/tier-gating'

interface AdminAccessGateProps {
  children: ReactNode
  capability?: string // Specific admin capability required
  accountTier: AccountTier
  isOrgAdmin?: boolean
  fallbackVariant?: 'card' | 'alert' | 'minimal'
  onUpgradeClick?: () => void
}

export function AdminAccessGate({
  children,
  capability = 'basic_admin',
  accountTier,
  isOrgAdmin = false,
  fallbackVariant = 'card',
  onUpgradeClick
}: AdminAccessGateProps) {
  // Check if user has the required tier for admin access
  const tierValidation = validateOrganizationAccess('admin', accountTier)
  
  // Check if user has the specific admin capability
  const hasCapability = hasAdminCapability(accountTier, capability)
  
  // User needs both the right tier AND to be an org admin
  const hasFullAccess = tierValidation.allowed && isOrgAdmin && hasCapability

  if (hasFullAccess) {
    return <>{children}</>
  }

  // Determine what's missing and show appropriate fallback
  if (!tierValidation.allowed) {
    // Tier upgrade needed
    if (fallbackVariant === 'minimal') {
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          <Lock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            Admin access requires {tierValidation.requiredTier} plan
          </span>
          {onUpgradeClick && (
            <Button size="sm" variant="outline" onClick={onUpgradeClick}>
              Upgrade
            </Button>
          )}
        </div>
      )
    }

    if (fallbackVariant === 'alert') {
      return (
        <Alert>
          <Crown className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>Admin features require a {tierValidation.requiredTier} plan.</span>
              {onUpgradeClick && (
                <Button size="sm" onClick={onUpgradeClick}>
                  Upgrade
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )
    }

    // Default card variant
    return (
      <Card className="border-amber-200 bg-amber-50">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-amber-600" />
            <CardTitle className="text-lg">Admin Access Required</CardTitle>
            <Badge className="bg-amber-100 text-amber-800">
              {tierValidation.requiredTier} Plan
            </Badge>
          </div>
          <CardDescription>
            Administrative features require a {tierValidation.requiredTier} plan or higher.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="p-3 bg-white/60 rounded-lg">
            <p className="text-sm text-muted-foreground">
              {tierValidation.message}
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm">Admin capabilities you'll unlock:</h4>
            <ul className="space-y-1 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-amber-600 rounded-full" />
                Manage team members and permissions
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-amber-600 rounded-full" />
                Configure organization settings
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-amber-600 rounded-full" />
                Access analytics and reports
              </li>
            </ul>
          </div>

          {onUpgradeClick && (
            <Button onClick={onUpgradeClick} className="w-full">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade to {tierValidation.requiredTier}
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  if (!isOrgAdmin) {
    // User has the right tier but isn't an org admin
    if (fallbackVariant === 'minimal') {
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            Organization admin privileges required
          </span>
        </div>
      )
    }

    if (fallbackVariant === 'alert') {
      return (
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            You need organization admin privileges to access this feature.
            Contact your organization admin for access.
          </AlertDescription>
        </Alert>
      )
    }

    return (
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">Admin Privileges Required</CardTitle>
            <Badge className="bg-blue-100 text-blue-800">
              Organization Admin
            </Badge>
          </div>
          <CardDescription>
            This feature requires organization administrator privileges.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="p-3 bg-white/60 rounded-lg">
            <p className="text-sm text-muted-foreground">
              You have a {accountTier} plan but need admin privileges within your organization 
              to access administrative features.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm">To gain admin access:</h4>
            <ul className="space-y-1 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full" />
                Contact your current organization admin
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full" />
                Request admin privileges for your role
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full" />
                Or create a new organization if you're the first user
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!hasCapability) {
    // User is admin but doesn't have specific capability
    if (fallbackVariant === 'minimal') {
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            This admin feature requires enterprise plan
          </span>
          {onUpgradeClick && (
            <Button size="sm" variant="outline" onClick={onUpgradeClick}>
              Upgrade
            </Button>
          )}
        </div>
      )
    }

    return (
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-purple-600" />
            <CardTitle className="text-lg">Enhanced Admin Feature</CardTitle>
            <Badge className="bg-purple-100 text-purple-800">
              Enterprise
            </Badge>
          </div>
          <CardDescription>
            This advanced administrative feature requires an Enterprise plan.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="p-3 bg-white/60 rounded-lg">
            <p className="text-sm text-muted-foreground">
              You have admin privileges but this specific capability ({capability}) 
              requires an Enterprise plan for enhanced administrative features.
            </p>
          </div>

          {onUpgradeClick && (
            <Button onClick={onUpgradeClick} className="w-full">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade to Enterprise
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  return null
}
