'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Crown, Users, Settings, CheckCircle, ArrowRight, Sparkles } from 'lucide-react'

interface FirstAdminCreationProps {
  isOpen: boolean
  onClose: () => void
  organizationName: string
  userInfo: {
    firstName: string
    lastName: string
    email: string
  }
  onBecomeAdmin: () => Promise<void>
  onDecline: () => void
}

type Step = 'welcome' | 'responsibilities' | 'confirmation' | 'processing' | 'complete'

export function FirstAdminCreation({
  isOpen,
  onClose,
  organizationName,
  userInfo,
  onBecomeAdmin,
  onDecline
}: FirstAdminCreationProps) {
  const [currentStep, setCurrentStep] = useState<Step>('welcome')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleBecomeAdmin = async () => {
    setCurrentStep('processing')
    setIsProcessing(true)
    
    try {
      await onBecomeAdmin()
      setCurrentStep('complete')
    } catch (error) {
      console.error('Failed to become admin:', error)
      setIsProcessing(false)
      // Could add error handling here
    }
  }

  const handleComplete = () => {
    setCurrentStep('welcome')
    setIsProcessing(false)
    onClose()
  }

  const stepProgress = {
    welcome: 25,
    responsibilities: 50,
    confirmation: 75,
    processing: 90,
    complete: 100
  }

  if (currentStep === 'welcome') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-amber-100 rounded-lg">
                <Crown className="h-6 w-6 text-amber-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">Congratulations!</DialogTitle>
                <DialogDescription>
                  You're the first Team plan user in {organizationName}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-6">
            <Progress value={stepProgress[currentStep]} className="w-full" />

            <Card className="bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Sparkles className="h-8 w-8 text-amber-600 mt-1" />
                  <div>
                    <h3 className="font-semibold text-amber-900 mb-2">
                      You're eligible to become the organization admin!
                    </h3>
                    <p className="text-amber-800 text-sm leading-relaxed">
                      As the first Team+ plan user in <strong>{organizationName}</strong>, 
                      you can set up and manage your organization. This includes inviting 
                      team members, configuring settings, and establishing workflows.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-3">
              <h4 className="font-medium">What this means:</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm">
                    You'll be able to invite and manage up to 10 team members
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                  <Settings className="h-5 w-5 text-green-600" />
                  <span className="text-sm">
                    Full access to organization settings and configurations
                  </span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                  <Crown className="h-5 w-5 text-amber-600" />
                  <span className="text-sm">
                    Administrative privileges within {organizationName}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <Button 
                onClick={() => setCurrentStep('responsibilities')}
                className="flex-1"
              >
                Learn More
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              <Button variant="outline" onClick={onDecline}>
                Maybe Later
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (currentStep === 'responsibilities') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Admin Responsibilities</DialogTitle>
            <DialogDescription>
              Understanding your role as organization administrator
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <Progress value={stepProgress[currentStep]} className="w-full" />

            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    Team Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <ul className="text-sm space-y-1">
                    <li>• Invite new team members</li>
                    <li>• Manage user permissions</li>
                    <li>• Remove inactive users</li>
                    <li>• Monitor team activity</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Settings className="h-4 w-4 text-green-600" />
                    Organization Setup
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <ul className="text-sm space-y-1">
                    <li>• Configure organization profile</li>
                    <li>• Set up domain verification</li>
                    <li>• Customize workflows</li>
                    <li>• Manage integrations</li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Crown className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900 mb-1">
                      Team Plan Benefits
                    </h4>
                    <p className="text-sm text-blue-800 mb-2">
                      Your Team plan includes all these administrative capabilities:
                    </p>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>✓ Up to 10 team members</li>
                      <li>✓ Unlimited records and workflows</li>
                      <li>✓ Advanced collaboration tools</li>
                      <li>✓ Priority support</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex gap-3">
              <Button 
                onClick={() => setCurrentStep('confirmation')}
                className="flex-1"
              >
                I'm Ready
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep('welcome')}
              >
                Back
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (currentStep === 'confirmation') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Confirm Admin Setup</DialogTitle>
            <DialogDescription>
              Ready to become the {organizationName} administrator?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <Progress value={stepProgress[currentStep]} className="w-full" />

            <Card>
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto">
                    <Crown className="h-8 w-8 text-amber-600" />
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-1">
                      {userInfo.firstName} {userInfo.lastName}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {userInfo.email}
                    </p>
                    <Badge className="mt-2 bg-amber-100 text-amber-800">
                      Organization Administrator
                    </Badge>
                  </div>

                  <div className="bg-muted/30 rounded-lg p-4">
                    <h4 className="font-medium mb-2">You'll be able to:</h4>
                    <ul className="text-sm space-y-1">
                      <li>✓ Manage {organizationName} settings</li>
                      <li>✓ Invite and organize team members</li>
                      <li>✓ Configure workflows and permissions</li>
                      <li>✓ Access administrative features</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex gap-3">
              <Button 
                onClick={handleBecomeAdmin}
                className="flex-1"
                disabled={isProcessing}
              >
                <Crown className="h-4 w-4 mr-2" />
                Become Admin
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep('responsibilities')}
                disabled={isProcessing}
              >
                Back
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (currentStep === 'processing') {
    return (
      <Dialog open={isOpen} onOpenChange={() => {}}>
        <DialogContent className="max-w-lg">
          <div className="space-y-6 text-center py-6">
            <Progress value={stepProgress[currentStep]} className="w-full" />
            
            <div className="space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto animate-pulse">
                <Settings className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Setting up your admin access...</h3>
                <p className="text-sm text-muted-foreground">
                  This will only take a moment
                </p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (currentStep === 'complete') {
    return (
      <Dialog open={isOpen} onOpenChange={handleComplete}>
        <DialogContent className="max-w-lg">
          <div className="space-y-6 text-center py-6">
            <Progress value={stepProgress[currentStep]} className="w-full" />
            
            <div className="space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              
              <div>
                <h3 className="font-semibold text-green-900 mb-2">
                  Welcome, Administrator!
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  You now have full administrative access to {organizationName}
                </p>
                
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="p-4">
                    <h4 className="font-medium text-green-900 mb-2">
                      Next Steps:
                    </h4>
                    <ul className="text-sm text-green-800 space-y-1 text-left">
                      <li>• Complete your organization profile</li>
                      <li>• Invite your first team members</li>
                      <li>• Set up domain verification</li>
                      <li>• Explore admin dashboard</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>

            <Button onClick={handleComplete} className="w-full">
              Get Started
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return null
}
