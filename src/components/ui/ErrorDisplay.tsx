/**
 * Error Display Component
 * 
 * User-friendly error messages and recovery options for TalentHUB.
 * Integrates with the error handling system to provide contextual
 * error displays with actionable recovery options.
 * 
 * Features:
 * - Contextual error messages
 * - Actionable recovery options
 * - Progressive error disclosure
 * - Accessibility compliant
 * - Integration with retry logic
 */

'use client'

import React from 'react'
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  Bug,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Badge } from '@/components/ui/badge'
import type { AppError, ErrorSeverity, ErrorCategory } from '@/lib/error-handling/error-boundaries'

export interface ErrorDisplayProps {
  error: AppError | Error | string
  variant?: 'card' | 'banner' | 'inline' | 'toast'
  showDetails?: boolean
  showRetry?: boolean
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  compact?: boolean
}

// Error severity styling
const SEVERITY_STYLES = {
  low: {
    icon: Info,
    cardStyle: 'border-blue-200 bg-blue-50',
    iconStyle: 'text-blue-600',
    titleStyle: 'text-blue-900',
    descStyle: 'text-blue-700'
  },
  medium: {
    icon: AlertCircle,
    cardStyle: 'border-orange-200 bg-orange-50',
    iconStyle: 'text-orange-600',
    titleStyle: 'text-orange-900',
    descStyle: 'text-orange-700'
  },
  high: {
    icon: AlertTriangle,
    cardStyle: 'border-red-200 bg-red-50',
    iconStyle: 'text-red-600',
    titleStyle: 'text-red-900',
    descStyle: 'text-red-700'
  },
  critical: {
    icon: AlertTriangle,
    cardStyle: 'border-red-300 bg-red-100',
    iconStyle: 'text-red-700',
    titleStyle: 'text-red-900',
    descStyle: 'text-red-800'
  }
}

// Category-specific messaging
const CATEGORY_CONFIG = {
  network: {
    title: 'Connection Issue',
    defaultMessage: 'Please check your internet connection and try again.',
    icon: WifiOff,
    actionLabel: 'Retry'
  },
  authentication: {
    title: 'Authentication Required',
    defaultMessage: 'Please sign in to continue.',
    icon: AlertTriangle,
    actionLabel: 'Sign In'
  },
  authorization: {
    title: 'Access Denied',
    defaultMessage: 'You don\'t have permission to access this feature.',
    icon: AlertTriangle,
    actionLabel: 'Contact Support'
  },
  validation: {
    title: 'Input Error',
    defaultMessage: 'Please check your input and try again.',
    icon: AlertCircle,
    actionLabel: 'Fix Input'
  },
  sync: {
    title: 'Sync Issue',
    defaultMessage: 'Your changes will be saved automatically when connection is restored.',
    icon: RefreshCw,
    actionLabel: 'Retry Sync'
  },
  ui: {
    title: 'Display Error',
    defaultMessage: 'Something went wrong with the display.',
    icon: Bug,
    actionLabel: 'Refresh'
  },
  unknown: {
    title: 'Unexpected Error',
    defaultMessage: 'An unexpected error occurred.',
    icon: AlertTriangle,
    actionLabel: 'Try Again'
  }
}

/**
 * Convert error to AppError format
 */
function normalizeError(error: AppError | Error | string): AppError {
  if (typeof error === 'string') {
    return {
      id: `err_${Date.now()}`,
      message: error,
      userMessage: error,
      category: 'unknown',
      severity: 'medium',
      retryable: true,
      timestamp: Date.now()
    }
  }

  if (error instanceof Error) {
    return {
      id: `err_${Date.now()}`,
      message: error.message,
      userMessage: error.message,
      category: 'unknown',
      severity: 'medium',
      retryable: true,
      timestamp: Date.now(),
      stack: error.stack
    }
  }

  return error
}

/**
 * Main Error Display Component
 */
export function ErrorDisplay({
  error,
  variant = 'card',
  showDetails = false,
  showRetry = true,
  onRetry,
  onDismiss,
  className,
  compact = false
}: ErrorDisplayProps) {
  const [isDetailsOpen, setIsDetailsOpen] = React.useState(false)
  const normalizedError = normalizeError(error)
  
  const severity = normalizedError.severity || 'medium'
  const category = normalizedError.category || 'unknown'
  
  const severityConfig = SEVERITY_STYLES[severity]
  const categoryConfig = CATEGORY_CONFIG[category]
  
  const Icon = categoryConfig.icon || severityConfig.icon
  const title = categoryConfig.title
  const message = normalizedError.userMessage || categoryConfig.defaultMessage
  const canRetry = normalizedError.retryable && onRetry && showRetry

  // Card variant
  if (variant === 'card') {
    return (
      <Card className={cn(severityConfig.cardStyle, className)}>
        <CardHeader className={compact ? 'pb-2' : undefined}>
          <div className="flex items-start gap-3">
            <Icon className={cn(severityConfig.iconStyle, compact ? 'w-4 h-4 mt-0.5' : 'w-5 h-5 mt-0.5')} />
            <div className="flex-1">
              <CardTitle className={cn(
                severityConfig.titleStyle,
                compact ? 'text-sm' : 'text-base'
              )}>
                {title}
              </CardTitle>
              {!compact && (
                <CardDescription className={cn(severityConfig.descStyle, 'mt-1')}>
                  {message}
                </CardDescription>
              )}
            </div>
            
            {/* Severity badge */}
            <Badge 
              variant={severity === 'critical' ? 'destructive' : 'secondary'}
              className="text-xs"
            >
              {severity}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className={compact ? 'pt-0' : undefined}>
          {compact && (
            <p className={cn(severityConfig.descStyle, 'text-sm mb-3')}>
              {message}
            </p>
          )}

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            {canRetry && (
              <Button
                size={compact ? 'sm' : 'default'}
                onClick={onRetry}
                className="gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                {categoryConfig.actionLabel}
              </Button>
            )}
            
            {onDismiss && (
              <Button
                variant="outline"
                size={compact ? 'sm' : 'default'}
                onClick={onDismiss}
              >
                Dismiss
              </Button>
            )}

            {showDetails && normalizedError.stack && (
              <Collapsible open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    size={compact ? 'sm' : 'default'}
                    className="gap-1"
                  >
                    Details
                    {isDetailsOpen ? (
                      <ChevronUp className="w-3 h-3" />
                    ) : (
                      <ChevronDown className="w-3 h-3" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                
                <CollapsibleContent className="mt-3">
                  <div className="bg-gray-100 rounded-md p-3 border">
                    <p className="text-xs font-medium text-gray-600 mb-2">
                      Error ID: {normalizedError.id}
                    </p>
                    <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap overflow-auto max-h-32">
                      {normalizedError.message}
                    </pre>
                    {process.env.NODE_ENV === 'development' && normalizedError.stack && (
                      <details className="mt-2">
                        <summary className="text-xs text-gray-500 cursor-pointer">
                          Stack trace
                        </summary>
                        <pre className="text-xs text-gray-600 font-mono whitespace-pre-wrap overflow-auto max-h-24 mt-1">
                          {normalizedError.stack}
                        </pre>
                      </details>
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Banner variant
  if (variant === 'banner') {
    return (
      <div className={cn(
        'flex items-center gap-3 p-4 rounded-md',
        severityConfig.cardStyle,
        className
      )}>
        <Icon className={cn(severityConfig.iconStyle, 'w-5 h-5 flex-shrink-0')} />
        <div className="flex-1 min-w-0">
          <p className={cn(severityConfig.titleStyle, 'font-medium')}>
            {title}
          </p>
          <p className={cn(severityConfig.descStyle, 'text-sm')}>
            {message}
          </p>
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          {canRetry && (
            <Button size="sm" onClick={onRetry} className="gap-1">
              <RefreshCw className="w-3 h-3" />
              Retry
            </Button>
          )}
          {onDismiss && (
            <Button variant="ghost" size="sm" onClick={onDismiss}>
              ×
            </Button>
          )}
        </div>
      </div>
    )
  }

  // Inline variant
  if (variant === 'inline') {
    return (
      <div className={cn('flex items-center gap-2 text-sm', className)}>
        <Icon className={cn(severityConfig.iconStyle, 'w-4 h-4')} />
        <span className={severityConfig.descStyle}>{message}</span>
        {canRetry && (
          <Button variant="link" size="sm" onClick={onRetry} className="h-auto p-0 gap-1">
            <RefreshCw className="w-3 h-3" />
            Retry
          </Button>
        )}
      </div>
    )
  }

  // Toast variant (minimal styling for toast notifications)
  if (variant === 'toast') {
    return (
      <div className="flex items-center gap-3">
        <Icon className={cn(severityConfig.iconStyle, 'w-4 h-4')} />
        <div className="flex-1">
          <p className="font-medium text-sm">{title}</p>
          <p className="text-sm opacity-90">{message}</p>
        </div>
        {canRetry && (
          <Button size="sm" variant="secondary" onClick={onRetry}>
            Retry
          </Button>
        )}
      </div>
    )
  }

  return null
}

/**
 * Network Error Component
 */
export function NetworkError({
  onRetry,
  className
}: {
  onRetry?: () => void
  className?: string
}) {
  const [isOnline, setIsOnline] = React.useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  )

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return (
    <ErrorDisplay
      error={{
        id: 'network_error',
        message: isOnline ? 'Network request failed' : 'You are offline',
        userMessage: isOnline 
          ? 'There was a problem connecting to our servers. Please try again.'
          : 'You are currently offline. Please check your internet connection.',
        category: 'network',
        severity: 'medium',
        retryable: true,
        timestamp: Date.now()
      }}
      variant="banner"
      onRetry={onRetry}
      className={className}
    />
  )
}

/**
 * Sync Error Component
 */
export function SyncError({
  onRetry,
  className
}: {
  onRetry?: () => void
  className?: string
}) {
  return (
    <ErrorDisplay
      error={{
        id: 'sync_error',
        message: 'Sync operation failed',
        userMessage: 'Your changes couldn\'t be synced. They are saved locally and will sync automatically.',
        category: 'sync',
        severity: 'medium',
        retryable: true,
        timestamp: Date.now()
      }}
      variant="banner"
      onRetry={onRetry}
      className={className}
    />
  )
}

/**
 * Generic Error Boundary Fallback
 */
export function ErrorFallback({
  error,
  onRetry
}: {
  error: Error
  onRetry?: () => void
}) {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <ErrorDisplay
        error={error}
        variant="card"
        showDetails={process.env.NODE_ENV === 'development'}
        onRetry={onRetry}
        className="max-w-md w-full"
      />
    </div>
  )
}

/**
 * Loading Error State
 */
export function LoadingError({
  message,
  onRetry,
  className
}: {
  message?: string
  onRetry?: () => void
  className?: string
}) {
  return (
    <div className={cn('text-center py-8', className)}>
      <ErrorDisplay
        error={{
          id: 'loading_error',
          message: message || 'Failed to load',
          userMessage: message || 'Failed to load content. Please try again.',
          category: 'unknown',
          severity: 'medium',
          retryable: !!onRetry,
          timestamp: Date.now()
        }}
        variant="inline"
        onRetry={onRetry}
      />
    </div>
  )
}

/**
 * Form Validation Error
 */
export function ValidationError({
  field,
  message,
  className
}: {
  field?: string
  message: string
  className?: string
}) {
  return (
    <ErrorDisplay
      error={{
        id: 'validation_error',
        message: `${field ? `${field}: ` : ''}${message}`,
        userMessage: message,
        category: 'validation',
        severity: 'low',
        retryable: false,
        timestamp: Date.now()
      }}
      variant="inline"
      showRetry={false}
      className={className}
    />
  )
}

/**
 * Permission Error
 */
export function PermissionError({
  action,
  onUpgrade,
  className
}: {
  action?: string
  onUpgrade?: () => void
  className?: string
}) {
  return (
    <ErrorDisplay
      error={{
        id: 'permission_error',
        message: `Permission denied${action ? ` for ${action}` : ''}`,
        userMessage: `You don't have permission${action ? ` to ${action}` : ''}. Please upgrade your plan or contact support.`,
        category: 'authorization',
        severity: 'high',
        retryable: false,
        timestamp: Date.now()
      }}
      variant="card"
      showRetry={false}
      onRetry={onUpgrade}
      className={className}
    />
  )
}

/**
 * Quota Exceeded Error
 */
export function QuotaError({
  resource,
  limit,
  onUpgrade,
  className
}: {
  resource?: string
  limit?: string | number
  onUpgrade?: () => void
  className?: string
}) {
  const resourceText = resource || 'usage'
  const limitText = limit ? ` (limit: ${limit})` : ''
  
  return (
    <ErrorDisplay
      error={{
        id: 'quota_error',
        message: `${resourceText} quota exceeded${limitText}`,
        userMessage: `You've reached your ${resourceText} limit${limitText}. Please upgrade your plan to continue.`,
        category: 'authorization',
        severity: 'medium',
        retryable: false,
        timestamp: Date.now()
      }}
      variant="banner"
      showRetry={false}
      onRetry={onUpgrade}
      className={className}
    />
  )
}

/**
 * Maintenance Mode Error
 */
export function MaintenanceError({
  className
}: {
  className?: string
}) {
  return (
    <ErrorDisplay
      error={{
        id: 'maintenance_error',
        message: 'Service temporarily unavailable',
        userMessage: 'TalentHUB is currently undergoing maintenance. Please try again in a few minutes.',
        category: 'network',
        severity: 'medium',
        retryable: true,
        timestamp: Date.now()
      }}
      variant="card"
      showRetry={true}
      className={className}
    />
  )
}

/**
 * Hook for error state management
 */
export function useErrorState() {
  const [error, setError] = React.useState<AppError | null>(null)

  const showError = React.useCallback((error: AppError | Error | string) => {
    setError(normalizeError(error))
  }, [])

  const clearError = React.useCallback(() => {
    setError(null)
  }, [])

  const hasError = error !== null

  return {
    error,
    showError,
    clearError,
    hasError
  }
}

export default ErrorDisplay