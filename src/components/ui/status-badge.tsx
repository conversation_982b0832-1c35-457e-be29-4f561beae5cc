import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

const statusBadgeVariants = cva(
  "inline-flex items-center gap-1 px-2.5 py-0.5 text-xs font-medium ring-1 ring-inset",
  {
    variants: {
      status: {
        critical: [
          "bg-red-50 text-red-700 ring-red-600/20",
          "dark:bg-red-950 dark:text-red-300 dark:ring-red-600/30"
        ],
        "in-progress": [
          "bg-amber-50 text-amber-700 ring-amber-600/20",
          "dark:bg-amber-950 dark:text-amber-300 dark:ring-amber-600/30"
        ],
        complete: [
          "bg-green-50 text-green-700 ring-green-600/20",
          "dark:bg-green-950 dark:text-green-300 dark:ring-green-600/30"
        ],
        active: [
          "bg-blue-50 text-blue-700 ring-blue-600/20",
          "dark:bg-blue-950 dark:text-blue-300 dark:ring-blue-600/30"
        ],
        inactive: [
          "bg-gray-50 text-gray-500 ring-gray-500/20",
          "dark:bg-gray-900 dark:text-gray-400 dark:ring-gray-500/30"
        ]
      },
    },
    defaultVariants: {
      status: "active",
    },
  }
)

export interface StatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusBadgeVariants> {
  status: "critical" | "in-progress" | "complete" | "active" | "inactive"
  showIcon?: boolean
}

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'critical':
      return <span className="h-1.5 w-1.5 rounded-full bg-red-500" />
    case 'in-progress':
      return <span className="h-1.5 w-1.5 rounded-full bg-amber-500 animate-pulse" />
    case 'complete':
      return <span className="h-1.5 w-1.5 rounded-full bg-green-500" />
    case 'active':
      return <span className="h-1.5 w-1.5 rounded-full bg-blue-500" />
    case 'inactive':
      return <span className="h-1.5 w-1.5 rounded-full bg-gray-400" />
    default:
      return null
  }
}

const StatusBadge = React.forwardRef<HTMLDivElement, StatusBadgeProps>(
  ({ className, status, showIcon = true, children, ...props }, ref) => {
    return (
      <div
        className={cn(statusBadgeVariants({ status }), className)}
        ref={ref}
        {...props}
      >
        {showIcon && <StatusIcon status={status} />}
        {children}
      </div>
    )
  }
)
StatusBadge.displayName = "StatusBadge"

export { StatusBadge, statusBadgeVariants }
