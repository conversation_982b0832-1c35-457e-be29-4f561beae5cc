import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { StatusBadge } from "@/components/ui/status-badge"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface WorkflowCardProps extends React.HTMLAttributes<HTMLDivElement> {
  workflow: "recruitment" | "bench-sales"
  title: string
  description?: string
  status: "critical" | "in-progress" | "complete" | "active" | "inactive"
  priority?: "high" | "medium" | "low"
  metadata?: {
    label: string
    value: string
  }[]
}

const WorkflowCard = React.forwardRef<HTMLDivElement, WorkflowCardProps>(
  ({ 
    className, 
    workflow, 
    title, 
    description, 
    status, 
    priority,
    metadata,
    children, 
    ...props 
  }, ref) => {
    
    const workflowConfig = {
      recruitment: {
        borderClass: "border-l-4 border-l-primary",
        bgClass: "bg-primary/5",
        label: "Recruitment"
      },
      "bench-sales": {
        borderClass: "border-l-4 border-l-primary", 
        bgClass: "bg-primary/5",
        label: "Bench Sales"
      }
    }

    const config = workflowConfig[workflow]

    return (
      <Card 
        className={cn(
          "transition-all duration-200 hover:shadow-md",
          config.borderClass,
          config.bgClass,
          className
        )}
        ref={ref}
        {...props}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Badge 
                  variant="secondary" 
                  className="text-xs text-primary bg-primary/10"
                >
                  {config.label}
                </Badge>
                {priority && (
                  <Badge 
                    variant={priority === "high" ? "destructive" : "outline"}
                    className="text-xs"
                  >
                    {priority.toUpperCase()}
                  </Badge>
                )}
              </div>
              <CardTitle className="text-lg leading-tight">{title}</CardTitle>
              {description && (
                <CardDescription className="text-sm">
                  {description}
                </CardDescription>
              )}
            </div>
            <StatusBadge status={status} className="shrink-0">
              {status.replace('-', ' ').replace(/^\w/, c => c.toUpperCase())}
            </StatusBadge>
          </div>
        </CardHeader>
        
        {(metadata || children) && (
          <CardContent className="pt-0">
            {metadata && (
              <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                {metadata.map((item, index) => (
                  <div key={index} className="space-y-1">
                    <div className="text-muted-foreground">{item.label}</div>
                    <div className="font-medium">{item.value}</div>
                  </div>
                ))}
              </div>
            )}
            {children}
          </CardContent>
        )}
      </Card>
    )
  }
)
WorkflowCard.displayName = "WorkflowCard"

export { WorkflowCard }
