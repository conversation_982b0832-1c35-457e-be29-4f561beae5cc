"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON><PERSON>, <PERSON>ltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ProfileDropdown } from "@/components/profile/ProfileDropdown"
import { 
  Home,
  Settings,
  Menu,
  ChevronLeft,
  ChevronRight
} from "lucide-react"

interface SidebarProps {
  children: React.ReactNode
}

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home
  }
  // Note: Settings temporarily disabled until preferences modal is implemented
]

export function Sidebar({ children }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(false)
  const [isMobileOpen, setIsMobileOpen] = React.useState(false)
  const pathname = usePathname()

  const toggleCollapse = () => setIsCollapsed(!isCollapsed)
  const toggleMobile = () => setIsMobileOpen(!isMobileOpen)
  const closeMobile = () => setIsMobileOpen(false)

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background flex">
        {/* Desktop Sidebar */}
        <aside className={cn(
          "hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:border-r lg:bg-background transition-all duration-300",
          isCollapsed ? "lg:w-16" : "lg:w-64"
        )}>
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className={cn(
              "flex items-center h-16 border-b px-4",
              isCollapsed ? "justify-center" : "justify-between"
            )}>
              {!isCollapsed && (
                <h1 className="text-xl font-bold">TalentHUB</h1>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleCollapse}
                className="h-8 w-8"
              >
                {isCollapsed ? (
                  <ChevronRight className="h-4 w-4" />
                ) : (
                  <ChevronLeft className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-2">
              <div className="space-y-1">
                {navItems.map((item) => {
                  const Icon = item.icon
                  const isActive = pathname === item.href
                  
                  const NavContent = (
                    <Link
                      href={item.href as any}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-colors",
                        "hover:bg-accent hover:text-accent-foreground",
                        isActive && "bg-accent text-accent-foreground font-medium",
                        isCollapsed && "justify-center"
                      )}
                    >
                      <Icon className="h-5 w-5 shrink-0" />
                      {!isCollapsed && <span>{item.title}</span>}
                    </Link>
                  )

                  if (isCollapsed) {
                    return (
                      <Tooltip key={item.href} delayDuration={0}>
                        <TooltipTrigger asChild>
                          {NavContent}
                        </TooltipTrigger>
                        <TooltipContent side="right" className="font-medium">
                          {item.title}
                        </TooltipContent>
                      </Tooltip>
                    )
                  }

                  return (
                    <React.Fragment key={item.href}>
                      {NavContent}
                    </React.Fragment>
                  )
                })}
              </div>
            </nav>

            {/* User Section */}
            <div className="p-2 border-t">
              <ProfileDropdown isCollapsed={isCollapsed} />
            </div>
          </div>
        </aside>

        {/* Mobile Header */}
        <div className="lg:hidden fixed top-0 left-0 right-0 z-40 bg-background border-b">
          <div className="flex items-center justify-between h-16 px-4">
            <div className="flex items-center gap-3">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={toggleMobile}
              >
                <Menu className="h-5 w-5" />
              </Button>
              <h1 className="text-xl font-bold">TalentHUB</h1>
            </div>
            <ProfileDropdown isCollapsed={true} />
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isMobileOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="lg:hidden fixed inset-0 z-50 bg-black/50"
              onClick={closeMobile}
            />
            
            {/* Mobile Sidebar */}
            <aside className="lg:hidden fixed top-0 left-0 bottom-0 z-50 w-64 bg-background border-r shadow-lg">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between h-16 px-4 border-b">
                  <h1 className="text-xl font-bold">TalentHUB</h1>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={closeMobile}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                </div>

                {/* Navigation */}
                <nav className="flex-1 p-2">
                  <div className="space-y-1">
                    {navItems.map((item) => {
                      const Icon = item.icon
                      const isActive = pathname === item.href
                      
                      return (
                        <Link
                          key={item.href}
                          href={item.href as any}
                          onClick={closeMobile}
                          className={cn(
                            "flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-colors",
                            "hover:bg-accent hover:text-accent-foreground",
                            isActive && "bg-accent text-accent-foreground font-medium"
                          )}
                        >
                          <Icon className="h-5 w-5 shrink-0" />
                          <span>{item.title}</span>
                        </Link>
                      )
                    })}
                  </div>
                </nav>

                {/* User Section */}
                <div className="p-2 border-t">
                  <ProfileDropdown isCollapsed={false} />
                </div>
              </div>
            </aside>
          </>
        )}

        {/* Main Content */}
        <main className={cn(
          "flex-1 transition-all duration-300",
          "lg:pl-64", // Default desktop margin
          isCollapsed && "lg:pl-16", // Collapsed desktop margin
          "pt-16 lg:pt-0" // Mobile top padding
        )}>
          <div className="container mx-auto p-6">
            {children}
          </div>
        </main>
      </div>
    </TooltipProvider>
  )
}