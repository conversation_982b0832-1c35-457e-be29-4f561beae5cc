/**
 * Organization Components
 * 
 * Centralized exports for all organization-related components.
 * Provides clean imports for the organization association system.
 */

// Core organization components
export { JoinOrganizationModal } from './JoinOrganizationModal'
export { PendingInvitations } from './PendingInvitations'
export { 
  OrganizationBadge, 
  OrganizationRoleBadge, 
  OrganizationStatus 
} from './OrganizationBadge'
export { IndividualToOrgWorkflow } from './IndividualToOrgWorkflow'

// Phase 3: Organization Management Components
export { OrganizationProfile } from './OrganizationProfile'
export { MemberManagement } from './MemberManagement'
export { RoleAssignment } from './RoleAssignment'
export { OrganizationBilling } from './OrganizationBilling'

// Notification components
export {
  OrgDetectionNotification,
  MultipleOrgsNotification,
  OrgRequestStatusNotification
} from '../notifications/OrgDetectionNotification'

// Hooks
export { useOrgDetection, useBasicOrgDetection } from '../../hooks/useOrgDetection'

// Types (re-exported for convenience)
export type {
  OrganizationDetectionWithRequests,
  JoinRequest,
  JoinRequestWithOrg,
  MembershipTransition
} from '../../lib/services/org-association'

export type {
  UserPreferencesWithOrg
} from '../../lib/services/user-preferences'
