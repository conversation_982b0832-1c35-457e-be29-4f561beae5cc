"use client"

import * as React from "react"
import { Organization } from "@clerk/nextjs/server"
import { useOrganization } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { LoadingState } from "@/components/ui/LoadingStates"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Shield, 
  Crown, 
  User, 
  Users,
  Settings,
  Eye,
  Edit,
  UserCheck,
  Building2,
  FileText,
  BarChart3,
  Save,
  Loader2,
  Check,
  AlertCircle
} from "lucide-react"

interface RoleAssignmentProps {
  organization: Organization
}

interface RolePermissions {
  id: string
  name: string
  description: string
  permissions: {
    viewMembers: boolean
    inviteMembers: boolean
    removeMembers: boolean
    manageRoles: boolean
    editOrganization: boolean
    viewBilling: boolean
    manageBilling: boolean
    viewReports: boolean
    manageSettings: boolean
  }
  isSystem: boolean
}

interface MemberRole {
  userId: string
  email: string
  name: string
  currentRole: string
  permissions: string[]
}

const systemRoles: RolePermissions[] = [
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Full access to all organization features and settings',
    permissions: {
      viewMembers: true,
      inviteMembers: true,
      removeMembers: true,
      manageRoles: true,
      editOrganization: true,
      viewBilling: true,
      manageBilling: true,
      viewReports: true,
      manageSettings: true,
    },
    isSystem: true
  },
  {
    id: 'manager',
    name: 'Manager',
    description: 'Can manage team members and view organization data',
    permissions: {
      viewMembers: true,
      inviteMembers: true,
      removeMembers: false,
      manageRoles: false,
      editOrganization: false,
      viewBilling: true,
      manageBilling: false,
      viewReports: true,
      manageSettings: false,
    },
    isSystem: true
  },
  {
    id: 'member',
    name: 'Member',
    description: 'Basic access to organization features',
    permissions: {
      viewMembers: true,
      inviteMembers: false,
      removeMembers: false,
      manageRoles: false,
      editOrganization: false,
      viewBilling: false,
      manageBilling: false,
      viewReports: false,
      manageSettings: false,
    },
    isSystem: true
  }
]

export function RoleAssignment({ organization }: RoleAssignmentProps) {
  const { membershipList } = useOrganization({
    membershipList: {},
  })
  const [roles, setRoles] = React.useState<RolePermissions[]>(systemRoles)
  const [members, setMembers] = React.useState<MemberRole[]>([])
  const [isLoading, setIsLoading] = React.useState(true)
  const [isSaving, setIsSaving] = React.useState(false)
  const [saveStatus, setSaveStatus] = React.useState<'idle' | 'success' | 'error'>('idle')

  React.useEffect(() => {
    if (membershipList) {
      const processedMembers: MemberRole[] = membershipList.map((membership: any) => ({
        userId: membership.publicUserData.userId,
        email: membership.publicUserData.identifier,
        name: `${membership.publicUserData.firstName} ${membership.publicUserData.lastName}`,
        currentRole: membership.role,
        permissions: getPermissionsForRole(membership.role)
      }))

      setMembers(processedMembers)
      setIsLoading(false)
    }
  }, [membershipList])

  const getPermissionsForRole = (roleId: string): string[] => {
    const role = roles.find(r => r.id === roleId)
    if (!role) return []
    
    return Object.entries(role.permissions)
      .filter(([_, hasPermission]) => hasPermission)
      .map(([permission, _]) => permission)
  }

  const handleRoleChange = async (userId: string, newRole: string) => {
    setIsSaving(true)
    try {
      // TODO: Implement role change API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setMembers(prev => prev.map(member => 
        member.userId === userId 
          ? { ...member, currentRole: newRole, permissions: getPermissionsForRole(newRole) }
          : member
      ))
      
      setSaveStatus('success')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } catch (error) {
      console.error('Failed to change role:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const getRoleIcon = (roleId: string) => {
    switch (roleId) {
      case 'admin':
        return <Crown className="h-4 w-4" />
      case 'manager':
        return <Shield className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getRoleBadgeVariant = (roleId: string) => {
    switch (roleId) {
      case 'admin':
        return 'default'
      case 'manager':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'viewMembers':
      case 'inviteMembers':
      case 'removeMembers':
        return <Users className="h-4 w-4" />
      case 'manageRoles':
        return <UserCheck className="h-4 w-4" />
      case 'editOrganization':
        return <Building2 className="h-4 w-4" />
      case 'viewBilling':
      case 'manageBilling':
        return <FileText className="h-4 w-4" />
      case 'viewReports':
        return <BarChart3 className="h-4 w-4" />
      case 'manageSettings':
        return <Settings className="h-4 w-4" />
      default:
        return <Eye className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return <LoadingState message="Loading roles and permissions..." />
  }

  return (
    <div className="space-y-6">
      {/* Role Definitions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Role Definitions
          </CardTitle>
          <CardDescription>
            System-defined roles and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {roles.map((role) => (
              <div key={role.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getRoleIcon(role.id)}
                    <h4 className="font-medium">{role.name}</h4>
                    <Badge variant={getRoleBadgeVariant(role.id)}>
                      {role.id}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {role.description}
                </p>
                
                {/* Permissions Grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {Object.entries(role.permissions).map(([permission, hasPermission]) => (
                    <div 
                      key={permission}
                      className={`flex items-center gap-2 p-2 rounded text-sm ${
                        hasPermission 
                          ? 'bg-green-50 text-green-700 border border-green-200' 
                          : 'bg-gray-50 text-gray-500 border border-gray-200'
                      }`}
                    >
                      {getPermissionIcon(permission)}
                      <span className="capitalize">
                        {permission.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </span>
                      {hasPermission ? (
                        <Check className="h-3 w-3 ml-auto" />
                      ) : (
                        <div className="w-3 h-3 ml-auto" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Member Role Assignments */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Member Role Assignments
              </CardTitle>
              <CardDescription>
                Assign roles to organization members
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              {saveStatus === 'success' && (
                <>
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">Changes saved</span>
                </>
              )}
              {saveStatus === 'error' && (
                <>
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-600">Save failed</span>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Member</TableHead>
                  <TableHead>Current Role</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Change Role</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map((member) => (
                  <TableRow key={member.userId}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {member.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(member.currentRole)} className="flex items-center gap-1 w-fit">
                        {getRoleIcon(member.currentRole)}
                        {member.currentRole}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-muted-foreground">
                        {member.permissions.length} permissions
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select
                        value={member.currentRole}
                        onValueChange={(value) => handleRoleChange(member.userId, value)}
                        disabled={isSaving}
                      >
                        <SelectTrigger className="w-[130px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              <div className="flex items-center gap-2">
                                {getRoleIcon(role.id)}
                                {role.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
