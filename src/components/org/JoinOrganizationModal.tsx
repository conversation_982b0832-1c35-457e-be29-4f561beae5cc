'use client'

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Building2, Mail, Globe, AlertCircle, CheckCircle, Clock, Crown } from 'lucide-react'
import { useOrgDetection } from '@/hooks/useOrgDetection'
import { OrganizationUpgradePrompt } from '@/components/billing/OrganizationUpgradePrompt'
import { UpgradeToTeamModal } from '@/components/billing/UpgradeToTeamModal'
import type { OrganizationDetectionWithRequests } from '@/lib/services/org-association'
import type { AccountTier } from '@/lib/services/tier-gating'

/**
 * Join Organization Modal
 * 
 * Professional modal for requesting to join an organization.
 * Handles domain-based detection, join requests, and status display.
 * 
 * Features:
 * - Organization information display
 * - Join request form with optional message
 * - Status indicators for existing requests
 * - Auto-join for eligible domains
 * - Error handling and loading states
 */

interface JoinOrganizationModalProps {
  isOpen: boolean
  onClose: () => void
  detectionResult: OrganizationDetectionWithRequests
  userEmail: string
  accountTier: AccountTier
  onUpgradeRequest?: () => void
}

export function JoinOrganizationModal({
  isOpen,
  onClose,
  detectionResult,
  userEmail,
  accountTier,
  onUpgradeRequest
}: JoinOrganizationModalProps) {
  const [message, setMessage] = useState('')
  const [showMessageField, setShowMessageField] = useState(false)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  
  const {
    createJoinRequest,
    cancelJoinRequest,
    attemptAutoJoin,
    isCreatingRequest,
    isCancelingRequest,
    isAutoJoining,
    actionError
  } = useOrgDetection({ autoDetectOnMount: false })
  
  const organization = detectionResult.organization
  const existingRequest = detectionResult.existingRequest
  
  if (!organization) return null
  
  const handleJoinRequest = async () => {
    const success = await createJoinRequest(organization.id, message || undefined)
    if (success) {
      onClose()
    }
  }
  
  const handleCancelRequest = async () => {
    if (!existingRequest) return
    const success = await cancelJoinRequest(existingRequest.id)
    if (success) {
      onClose()
    }
  }
  
  const handleAutoJoin = async () => {
    const success = await attemptAutoJoin(userEmail)
    if (success) {
      onClose()
    }
  }
  
  const getStatusBadge = () => {
    if (!existingRequest) return null
    
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending Review' },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Approved' },
      rejected: { color: 'bg-red-100 text-red-800', icon: AlertCircle, label: 'Rejected' },
      expired: { color: 'bg-gray-100 text-gray-800', icon: AlertCircle, label: 'Expired' }
    }
    
    const config = statusConfig[existingRequest.status]
    const Icon = config.icon
    
    return (
      <Badge variant="secondary" className={`${config.color} gap-1`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            Join Organization
          </DialogTitle>
          <DialogDescription>
            We detected that your email domain is associated with an organization.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Organization Info */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h3 className="font-semibold text-lg">{organization.name}</h3>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span>{detectionResult.domain}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Globe className="h-4 w-4" />
                  <span>{organization.autoJoinEnabled ? 'Auto-join enabled' : 'Requires approval'}</span>
                </div>
              </div>
              {getStatusBadge()}
            </div>
          </div>

          {/* Tier Validation - Show upgrade prompt if needed */}
          {detectionResult.upgradeRequired && detectionResult.tierValidation && (
            <OrganizationUpgradePrompt
              currentTier={accountTier}
              organizationName={organization.name}
              context="join_attempt"
              variant="card"
              onUpgrade={() => setShowUpgradeModal(true)}
              onDismiss={onClose}
            />
          )}
          
          {/* Action Error */}
          {actionError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{actionError}</AlertDescription>
            </Alert>
          )}
          
          {/* Existing Request Info */}
          {existingRequest && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                You have a {existingRequest.status} join request from{' '}
                {new Date(existingRequest.createdAt).toLocaleDateString()}.
                {existingRequest.status === 'pending' && (
                  <span className="block mt-1 text-sm">
                    The organization admins will review your request and notify you of their decision.
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}
          
          {/* Join Request Form */}
          {!detectionResult.hasPendingRequest && (
            <div className="space-y-4">
              {organization.autoJoinEnabled ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    This organization allows automatic joining for verified email domains.
                    You can join immediately without approval.
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    This organization requires admin approval for new members.
                    Your request will be reviewed by organization administrators.
                  </AlertDescription>
                </Alert>
              )}
              
              {/* Optional Message */}
              {!organization.autoJoinEnabled && (
                <div className="space-y-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowMessageField(!showMessageField)}
                    className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
                  >
                    {showMessageField ? 'Hide' : 'Add'} optional message
                  </Button>
                  
                  {showMessageField && (
                    <div className="space-y-1">
                      <Textarea
                        placeholder="Introduce yourself or explain why you'd like to join this organization..."
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        className="resize-none"
                        rows={3}
                        maxLength={500}
                      />
                      <p className="text-xs text-gray-500">
                        {message.length}/500 characters
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            
            {detectionResult.upgradeRequired ? (
              <Button
                onClick={() => setShowUpgradeModal(true)}
                className="flex-1"
              >
                <Crown className="h-4 w-4 mr-2" />
                Upgrade to Join
              </Button>
            ) : existingRequest?.status === 'pending' ? (
              <Button
                variant="destructive"
                onClick={handleCancelRequest}
                disabled={isCancelingRequest}
                className="flex-1"
              >
                {isCancelingRequest ? 'Canceling...' : 'Cancel Request'}
              </Button>
            ) : !detectionResult.hasPendingRequest ? (
              organization.autoJoinEnabled ? (
                <Button
                  onClick={handleAutoJoin}
                  disabled={isAutoJoining}
                  className="flex-1"
                >
                  {isAutoJoining ? 'Joining...' : 'Join Organization'}
                </Button>
              ) : (
                <Button
                  onClick={handleJoinRequest}
                  disabled={isCreatingRequest}
                  className="flex-1"
                >
                  {isCreatingRequest ? 'Requesting...' : 'Request to Join'}
                </Button>
              )
            ) : null}
          </div>
        </div>
      </DialogContent>

      {/* Upgrade Modal */}
      <UpgradeToTeamModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        currentTier={accountTier}
        organizationName={organization.name}
        context="join_attempt"
        onUpgrade={async (tier, billing) => {
          // Handle upgrade logic here
          if (onUpgradeRequest) {
            onUpgradeRequest()
          }
          setShowUpgradeModal(false)
        }}
      />
    </Dialog>
  )
}
