'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Building2, 
  User, 
  Crown, 
  Shield, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  ExternalLink 
} from 'lucide-react'
import type { UserPreferencesWithOrg } from '@/lib/services/user-preferences'
import type { JoinRequestWithOrg } from '@/lib/services/org-association'

/**
 * Organization Badge Component
 * 
 * Displays user's organization status with visual indicators.
 * Shows organization name, membership status, and join request status.
 * 
 * Features:
 * - Organization name display
 * - Membership status indicators
 * - Join request status
 * - Tooltips with detailed information
 * - Click-to-expand organization details
 */

interface OrganizationBadgeProps {
  userPreferences?: UserPreferencesWithOrg
  joinRequests?: JoinRequestWithOrg[]
  variant?: 'default' | 'compact' | 'detailed'
  showTooltip?: boolean
  onClick?: () => void
  className?: string
}

export function OrganizationBadge({
  userPreferences,
  joinRequests = [],
  variant = 'default',
  showTooltip = true,
  onClick,
  className
}: OrganizationBadgeProps) {
  const organization = userPreferences?.organization
  const isOrgMember = userPreferences?.isOrgMember
  const pendingRequests = joinRequests.filter(req => req.status === 'pending')
  
  // Determine badge content based on status
  const getBadgeConfig = () => {
    if (organization && isOrgMember) {
      return {
        icon: Building2,
        text: organization.name,
        subtext: 'Member',
        color: 'bg-green-100 text-green-800 border-green-200',
        tooltip: `Organization member of ${organization.name}`
      }
    }
    
    if (organization && !isOrgMember) {
      return {
        icon: Building2,
        text: organization.name,
        subtext: 'Domain Association',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        tooltip: `Associated with ${organization.name} via email domain`
      }
    }
    
    if (pendingRequests.length > 0) {
      const request = pendingRequests[0]
      return {
        icon: Clock,
        text: request.organization.name,
        subtext: 'Request Pending',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        tooltip: `Join request pending for ${request.organization.name}`
      }
    }
    
    return {
      icon: User,
      text: 'Individual Account',
      subtext: null,
      color: 'bg-gray-100 text-gray-700 border-gray-200',
      tooltip: 'Individual user account - not associated with any organization'
    }
  }
  
  const config = getBadgeConfig()
  const Icon = config.icon
  
  // Compact variant
  if (variant === 'compact') {
    const badge = (
      <Badge 
        variant="outline" 
        className={`${config.color} gap-1 ${onClick ? 'cursor-pointer hover:opacity-80' : ''} ${className}`}
        onClick={onClick}
      >
        <Icon className="h-3 w-3" />
        {organization ? organization.name : 'Individual'}
      </Badge>
    )
    
    if (showTooltip) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {badge}
            </TooltipTrigger>
            <TooltipContent>
              <p>{config.tooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }
    
    return badge
  }
  
  // Detailed variant
  if (variant === 'detailed') {
    return (
      <div className={`border rounded-lg p-3 ${config.color} ${className}`}>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium">{config.text}</span>
            </div>
            {config.subtext && (
              <p className="text-sm opacity-80">{config.subtext}</p>
            )}
            {organization && (
              <div className="text-xs opacity-70">
                Type: {organization.type} • Slug: {organization.slug}
              </div>
            )}
          </div>
          {onClick && (
            <Button variant="ghost" size="sm" onClick={onClick}>
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        {pendingRequests.length > 0 && (
          <div className="mt-2 pt-2 border-t border-current/20">
            <div className="text-xs">
              {pendingRequests.length} pending request{pendingRequests.length > 1 ? 's' : ''}
            </div>
          </div>
        )}
      </div>
    )
  }
  
  // Default variant
  const badge = (
    <div className={`inline-flex items-center gap-2 ${onClick ? 'cursor-pointer' : ''} ${className}`}>
      <Badge 
        variant="outline" 
        className={`${config.color} gap-1`}
        onClick={onClick}
      >
        <Icon className="h-3 w-3" />
        {config.text}
        {config.subtext && (
          <span className="text-xs opacity-70">• {config.subtext}</span>
        )}
      </Badge>
      
      {pendingRequests.length > 0 && (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 gap-1">
          <Clock className="h-3 w-3" />
          {pendingRequests.length} pending
        </Badge>
      )}
    </div>
  )
  
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p>{config.tooltip}</p>
              {pendingRequests.length > 0 && (
                <p className="text-xs">
                  {pendingRequests.length} join request{pendingRequests.length > 1 ? 's' : ''} pending review
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }
  
  return badge
}

/**
 * Organization Role Badge
 * 
 * Displays user's role within an organization
 */
interface OrganizationRoleBadgeProps {
  role: 'admin' | 'manager' | 'recruiter' | 'coordinator'
  className?: string
}

export function OrganizationRoleBadge({ role, className }: OrganizationRoleBadgeProps) {
  const roleConfig = {
    admin: {
      icon: Crown,
      label: 'Admin',
      color: 'bg-purple-100 text-purple-800 border-purple-200'
    },
    manager: {
      icon: Shield,
      label: 'Manager',
      color: 'bg-blue-100 text-blue-800 border-blue-200'
    },
    recruiter: {
      icon: User,
      label: 'Recruiter',
      color: 'bg-green-100 text-green-800 border-green-200'
    },
    coordinator: {
      icon: CheckCircle,
      label: 'Coordinator',
      color: 'bg-orange-100 text-orange-800 border-orange-200'
    }
  }
  
  const config = roleConfig[role]
  const Icon = config.icon
  
  return (
    <Badge 
      variant="outline" 
      className={`${config.color} gap-1 ${className}`}
    >
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

/**
 * Organization Status Indicator
 * 
 * Simple status indicator for organization-related states
 */
interface OrganizationStatusProps {
  status: 'member' | 'pending' | 'domain' | 'individual' | 'rejected'
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  className?: string
}

export function OrganizationStatus({ 
  status, 
  size = 'md', 
  showLabel = true, 
  className 
}: OrganizationStatusProps) {
  const statusConfig = {
    member: {
      icon: CheckCircle,
      label: 'Member',
      color: 'text-green-600'
    },
    pending: {
      icon: Clock,
      label: 'Pending',
      color: 'text-yellow-600'
    },
    domain: {
      icon: Building2,
      label: 'Domain',
      color: 'text-blue-600'
    },
    individual: {
      icon: User,
      label: 'Individual',
      color: 'text-gray-600'
    },
    rejected: {
      icon: AlertCircle,
      label: 'Rejected',
      color: 'text-red-600'
    }
  }
  
  const config = statusConfig[status]
  const Icon = config.icon
  
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4', 
    lg: 'h-5 w-5'
  }
  
  return (
    <div className={`flex items-center gap-1 ${config.color} ${className}`}>
      <Icon className={sizeClasses[size]} />
      {showLabel && (
        <span className="text-sm font-medium">{config.label}</span>
      )}
    </div>
  )
}
