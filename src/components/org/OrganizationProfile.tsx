"use client"

import * as React from "react"
import { Organization } from "@clerk/nextjs/server"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LoadingState } from "@/components/ui/LoadingStates"
import { 
  Save, 
  Upload, 
  Building2, 
  MapPin, 
  Globe, 
  Phone,
  Loader2,
  Check,
  AlertCircle
} from "lucide-react"

interface OrganizationProfileProps {
  organization: Organization
}

interface OrgProfileData {
  name: string
  description: string
  website: string
  phone: string
  location: string
  industry: string
  size: string
  logoUrl: string
}

export function OrganizationProfile({ organization }: OrganizationProfileProps) {
  const [profile, setProfile] = React.useState<OrgProfileData>({
    name: organization.name || '',
    description: '',
    website: '',
    phone: '',
    location: '',
    industry: '',
    size: '',
    logoUrl: organization.imageUrl || ''
  })
  const [isLoading, setIsLoading] = React.useState(true)
  const [isSaving, setIsSaving] = React.useState(false)
  const [hasChanges, setHasChanges] = React.useState(false)
  const [saveStatus, setSaveStatus] = React.useState<'idle' | 'success' | 'error'>('idle')

  React.useEffect(() => {
    const fetchOrgProfile = async () => {
      try {
        // TODO: Implement API endpoint for org profile data
        setProfile(prev => ({
          ...prev,
          name: organization.name || '',
          logoUrl: organization.imageUrl || ''
        }))
      } catch (error) {
        console.error('Failed to fetch organization profile:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrgProfile()
  }, [organization])

  const handleInputChange = (field: keyof OrgProfileData, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }))
    setHasChanges(true)
    setSaveStatus('idle')
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // TODO: Implement organization profile update API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setSaveStatus('success')
      setHasChanges(false)
      setTimeout(() => setSaveStatus('idle'), 3000)
    } catch (error) {
      console.error('Failed to save organization profile:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return <LoadingState message="Loading organization profile..." />
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Update your organization's basic profile information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Logo Upload */}
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={profile.logoUrl} alt={profile.name} />
              <AvatarFallback className="text-lg">
                <Building2 className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div>
              <Button variant="outline" size="sm" className="mb-2">
                <Upload className="h-4 w-4 mr-2" />
                Upload Logo
              </Button>
              <p className="text-sm text-muted-foreground">
                Recommended: 400x400px, max 2MB
              </p>
            </div>
          </div>

          <Separator />

          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name">Organization Name</Label>
              <Input
                id="name"
                value={profile.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter organization name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="industry">Industry</Label>
              <Input
                id="industry"
                value={profile.industry}
                onChange={(e) => handleInputChange('industry', e.target.value)}
                placeholder="e.g., Technology, Healthcare"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="size">Company Size</Label>
              <Input
                id="size"
                value={profile.size}
                onChange={(e) => handleInputChange('size', e.target.value)}
                placeholder="e.g., 1-10 employees"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="location"
                  className="pl-10"
                  value={profile.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="City, Country"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <div className="relative">
                <Globe className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="website"
                  className="pl-10"
                  value={profile.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://www.example.com"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="phone"
                  className="pl-10"
                  value={profile.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+****************"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={profile.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Tell us about your organization..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {saveStatus === 'success' && (
            <>
              <Check className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-600">Changes saved successfully</span>
            </>
          )}
          {saveStatus === 'error' && (
            <>
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">Failed to save changes</span>
            </>
          )}
        </div>
        
        <Button 
          onClick={handleSave} 
          disabled={!hasChanges || isSaving}
          className="bg-[#007AFF] hover:bg-[#0056b3]"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
