'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@clerk/nextjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Building2, 
  Users, 
  Settings, 
  Bell,
  RefreshCw,
  CheckCircle
} from 'lucide-react'
import {
  useOrgDetection,
  OrganizationBadge,
  PendingInvitations,
  OrgDetectionNotification
} from '@/components/org'
import { getEnhancedUserProfileWithOrg } from '@/lib/services/user-preferences'
import type { UserPreferencesWithOrg } from '@/lib/services/user-preferences'

/**
 * Individual to Organization Transition Workflow
 * 
 * Comprehensive component that manages the complete workflow for users
 * transitioning from individual accounts to organization membership.
 * 
 * Features:
 * - Auto-detection of organizations
 * - Join request management
 * - Status tracking and notifications
 * - Data transition handling
 * - Admin approval workflows
 */

interface IndividualToOrgWorkflowProps {
  className?: string
}

export function IndividualToOrgWorkflow({ className }: IndividualToOrgWorkflowProps) {
  const { user, isLoaded: userLoaded } = useUser()
  const [userPreferences, setUserPreferences] = useState<UserPreferencesWithOrg | null>(null)
  const [isLoadingPreferences, setIsLoadingPreferences] = useState(true)
  const [showNotifications, setShowNotifications] = useState(true)
  
  const {
    detectionResult,
    joinRequests,
    isDetecting,
    detectionError,
    detectOrganization,
    refreshJoinRequests
  } = useOrgDetection({
    autoDetectOnMount: true,
    autoJoinIfEligible: false, // Let user decide
    refreshInterval: 30000
  })
  
  // Load user preferences
  useEffect(() => {
    const loadUserPreferences = async () => {
      if (!user?.id) return
      
      setIsLoadingPreferences(true)
      try {
        const prefs = await getEnhancedUserProfileWithOrg(user.id)
        setUserPreferences(prefs)
      } catch (error) {
        console.error('Failed to load user preferences:', error)
      } finally {
        setIsLoadingPreferences(false)
      }
    }
    
    if (userLoaded && user?.id) {
      loadUserPreferences()
    }
  }, [userLoaded, user?.id])
  
  const handleRefresh = async () => {
    if (user?.primaryEmailAddress?.emailAddress) {
      await Promise.all([
        detectOrganization(user.primaryEmailAddress.emailAddress),
        refreshJoinRequests()
      ])
    }
  }
  
  const handleNotificationDismiss = () => {
    setShowNotifications(false)
  }
  
  const handleJoinSuccess = () => {
    // Refresh user preferences after successful join
    if (user?.id) {
      getEnhancedUserProfileWithOrg(user.id).then(setUserPreferences)
    }
    setShowNotifications(false)
  }
  
  // Don't render until user is loaded
  if (!userLoaded || !user) {
    return null
  }
  
  const userEmail = user.primaryEmailAddress?.emailAddress
  const hasOrgAssociation = userPreferences?.orgId
  const pendingRequests = joinRequests.filter(req => req.status === 'pending')
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Organization Detection Notification */}
      {showNotifications && 
       detectionResult?.found && 
       !hasOrgAssociation && 
       userEmail && (
        <OrgDetectionNotification
          detectionResult={detectionResult}
          userEmail={userEmail}
          onDismiss={handleNotificationDismiss}
          onJoinSuccess={handleJoinSuccess}
          variant="banner"
        />
      )}
      
      {/* Main Workflow Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Organization Association
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Manage your organization membership and requests
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              {userPreferences && (
                <OrganizationBadge
                  userPreferences={userPreferences}
                  joinRequests={joinRequests}
                  variant="compact"
                  showTooltip={true}
                />
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isDetecting || isLoadingPreferences}
              >
                <RefreshCw className={`h-4 w-4 ${isDetecting ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="status" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="status" className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Status
              </TabsTrigger>
              <TabsTrigger value="requests" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Requests
                {pendingRequests.length > 0 && (
                  <span className="bg-red-500 text-white rounded-full text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center">
                    {pendingRequests.length}
                  </span>
                )}
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </TabsTrigger>
            </TabsList>
            
            {/* Status Tab */}
            <TabsContent value="status" className="space-y-4">
              {isLoadingPreferences ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto text-gray-400" />
                  <p className="text-sm text-gray-600 mt-2">Loading your organization status...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Current Status */}
                  <div className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-3">Current Status</h3>
                    {userPreferences ? (
                      <OrganizationBadge
                        userPreferences={userPreferences}
                        joinRequests={joinRequests}
                        variant="detailed"
                      />
                    ) : (
                      <Alert>
                        <Users className="h-4 w-4" />
                        <AlertDescription>
                          Individual account - not associated with any organization
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                  
                  {/* Detection Results */}
                  {detectionResult && (
                    <div className="border rounded-lg p-4">
                      <h3 className="font-semibold mb-3">Organization Detection</h3>
                      {detectionResult.found ? (
                        <div className="space-y-2">
                          <p className="text-sm text-green-700">
                            ✓ Organization found for domain: <strong>{detectionResult.domain}</strong>
                          </p>
                          <p className="text-sm">
                            Organization: <strong>{detectionResult.organization?.name}</strong>
                          </p>
                          {detectionResult.hasPendingRequest && (
                            <p className="text-sm text-yellow-700">
                              ⏳ You have a pending join request for this organization
                            </p>
                          )}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-600">
                          No organization detected for your email domain: {detectionResult.domain}
                        </p>
                      )}
                    </div>
                  )}
                  
                  {detectionError && (
                    <Alert variant="destructive">
                      <AlertDescription>{detectionError}</AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </TabsContent>
            
            {/* Requests Tab */}
            <TabsContent value="requests">
              <PendingInvitations />
            </TabsContent>
            
            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-3">Detection Settings</h3>
                <div className="space-y-2 text-sm">
                  <p>
                    <strong>Auto-detection:</strong> Enabled for your email domain
                  </p>
                  <p>
                    <strong>Email:</strong> {userEmail}
                  </p>
                  <p>
                    <strong>Domain:</strong> {userEmail?.split('@')[1]}
                  </p>
                </div>
                
                <div className="mt-4">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => userEmail && detectOrganization(userEmail)}
                    disabled={isDetecting}
                  >
                    {isDetecting ? 'Detecting...' : 'Re-scan for Organizations'}
                  </Button>
                </div>
              </div>
              
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold mb-3">Notification Preferences</h3>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input 
                      type="checkbox" 
                      checked={showNotifications}
                      onChange={(e) => setShowNotifications(e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Show organization detection notifications</span>
                  </label>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
