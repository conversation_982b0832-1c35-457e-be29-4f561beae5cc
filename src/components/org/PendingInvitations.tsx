'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Building2, 
  Mail, 
  RefreshCw,
  MessageSquare 
} from 'lucide-react'
import { useOrgDetection } from '@/hooks/useOrgDetection'
import type { JoinRequestWithOrg } from '@/lib/services/org-association'

/**
 * Pending Invitations Component
 * 
 * Displays and manages user's organization join requests.
 * Shows status, organization details, and allows cancellation of pending requests.
 * 
 * Features:
 * - Real-time status updates
 * - Request cancellation
 * - Organization information display
 * - Status indicators with timestamps
 * - Error handling and loading states
 */

interface PendingInvitationsProps {
  className?: string
}

export function PendingInvitations({ className }: PendingInvitationsProps) {
  const {
    joinRequests,
    isLoadingRequests,
    requestsError,
    cancelJoinRequest,
    refreshJoinRequests,
    isCancelingRequest,
    actionError
  } = useOrgDetection({ autoDetectOnMount: true })
  
  const [cancelingRequestId, setCancelingRequestId] = useState<string | null>(null)
  
  const handleCancelRequest = async (requestId: string) => {
    setCancelingRequestId(requestId)
    const success = await cancelJoinRequest(requestId)
    setCancelingRequestId(null)
    
    if (success) {
      // Refresh handled by hook
    }
  }
  
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: Clock,
          label: 'Pending Review',
          description: 'Waiting for organization admin approval'
        }
      case 'approved':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircle,
          label: 'Approved',
          description: 'Your request has been approved'
        }
      case 'rejected':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: XCircle,
          label: 'Rejected',
          description: 'Your request was not approved'
        }
      case 'expired':
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: AlertCircle,
          label: 'Expired',
          description: 'Request expired without review'
        }
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: AlertCircle,
          label: 'Unknown',
          description: 'Unknown status'
        }
    }
  }
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // Filter for active requests (exclude old approved/rejected ones)
  const activeRequests = joinRequests.filter(request => 
    request.status === 'pending' || 
    (request.status !== 'pending' && 
     new Date(request.updatedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) // Last 7 days
  )
  
  if (isLoadingRequests) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Requests
          </CardTitle>
          <CardDescription>
            Loading your organization join requests...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    )
  }
  
  if (requestsError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {requestsError}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={refreshJoinRequests}
                className="ml-2"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }
  
  if (activeRequests.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Requests
          </CardTitle>
          <CardDescription>
            You don't have any recent organization join requests.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Building2 className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p className="text-sm">No organization requests</p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Organization Requests
            </CardTitle>
            <CardDescription>
              Manage your organization join requests
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshJoinRequests}
            disabled={isLoadingRequests}
          >
            <RefreshCw className={`h-4 w-4 ${isLoadingRequests ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {actionError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{actionError}</AlertDescription>
          </Alert>
        )}
        
        {activeRequests.map((request, index) => {
          const statusConfig = getStatusConfig(request.status)
          const StatusIcon = statusConfig.icon
          const isCurrentlyCanceling = cancelingRequestId === request.id
          
          return (
            <div key={request.id}>
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h4 className="font-semibold">{request.organization.name}</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="h-4 w-4" />
                      <span>{request.domain}</span>
                    </div>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={`${statusConfig.color} gap-1`}
                  >
                    <StatusIcon className="h-3 w-3" />
                    {statusConfig.label}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600">
                  {statusConfig.description}
                </p>
                
                {request.message && (
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                      <MessageSquare className="h-4 w-4" />
                      Your message:
                    </div>
                    <p className="text-sm text-gray-600 italic">"{request.message}"</p>
                  </div>
                )}
                
                {request.adminNotes && (
                  <div className="bg-blue-50 p-3 rounded-md">
                    <div className="text-sm font-medium text-blue-700 mb-1">
                      Admin response:
                    </div>
                    <p className="text-sm text-blue-600">"{request.adminNotes}"</p>
                  </div>
                )}
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    Requested: {formatDate(request.createdAt)}
                  </span>
                  {request.reviewedAt && (
                    <span>
                      Reviewed: {formatDate(request.reviewedAt)}
                    </span>
                  )}
                </div>
                
                {request.status === 'pending' && (
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCancelRequest(request.id)}
                      disabled={isCurrentlyCanceling || isCancelingRequest}
                    >
                      {isCurrentlyCanceling ? 'Canceling...' : 'Cancel Request'}
                    </Button>
                  </div>
                )}
              </div>
              
              {index < activeRequests.length - 1 && (
                <Separator className="mt-4" />
              )}
            </div>
          )
        })}
      </CardContent>
    </Card>
  )
}
