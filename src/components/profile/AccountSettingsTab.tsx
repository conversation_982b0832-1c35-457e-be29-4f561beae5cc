"use client"

import * as React from "react"
import { useUser } from "@clerk/nextjs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { LoadingState } from "@/components/ui/LoadingStates"
import { TimezoneSettings } from "@/components/profile/TimezoneSettings"
import { 
  CreditCard,
  Crown,
  Activity,
  Calendar,
  Users,
  Zap,
  ArrowUpRight,
  CheckCircle,
  AlertTriangle
} from "lucide-react"

interface AccountData {
  accountTier: 'free' | 'pro' | 'team' | 'enterprise'
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'unpaid'
  usageMetrics: {
    profilesUsed: number
    profilesLimit: number
    storageUsed: number
    storageLimit: number
    apiCallsUsed: number
    apiCallsLimit: number
  }
  billingInfo: {
    nextBillingDate?: string
    amount?: number
    currency?: string
  }
  features: string[]
}

const tierFeatures = {
  free: [
    'Up to 50 candidate profiles',
    'Basic search and filtering',
    'Email support',
    '1GB storage'
  ],
  pro: [
    'Up to 500 candidate profiles',
    'Advanced search and filtering',
    'Priority email support',
    '10GB storage',
    'API access',
    'Custom fields'
  ],
  team: [
    'Unlimited candidate profiles',
    'Team collaboration features',
    'Priority support',
    '100GB storage',
    'Advanced API access',
    'Custom integrations'
  ],
  enterprise: [
    'Everything in Team',
    'Dedicated account manager',
    'Custom deployment options',
    'Advanced security features',
    'SLA guarantees',
    'Custom training'
  ]
}

const tierColors = {
  free: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
  pro: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  team: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  enterprise: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
}

export function AccountSettingsTab() {
  const { user } = useUser()
  const [accountData, setAccountData] = React.useState<AccountData | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchAccountData = async () => {
      try {
        console.log('AccountSettingsTab: Starting data fetch...')
        const response = await fetch('/api/user-profile')
        console.log('AccountSettingsTab: Response status:', response.status)
        
        if (response.ok) {
          const data = await response.json()
          console.log('AccountSettingsTab: Response data:', data)
          
          if (data.success && data.profile) {
            // Use actual profile data
            setAccountData({
              accountTier: data.profile.account_tier || 'free',
              subscriptionStatus: data.profile.subscription_status || 'active',
              usageMetrics: {
                profilesUsed: 23,
                profilesLimit: data.profile.account_tier === 'free' ? 50 : 
                              data.profile.account_tier === 'pro' ? 500 : -1,
                storageUsed: 2.3,
                storageLimit: data.profile.account_tier === 'free' ? 1 : 
                             data.profile.account_tier === 'pro' ? 10 : 100,
                apiCallsUsed: 1250,
                apiCallsLimit: data.profile.account_tier === 'free' ? 0 : 10000
              },
              billingInfo: {
                nextBillingDate: '2025-07-27',
                amount: data.profile.account_tier === 'pro' ? 29 : 
                       data.profile.account_tier === 'team' ? 99 : 0,
                currency: 'USD'
              },
              features: tierFeatures[data.profile.account_tier as keyof typeof tierFeatures] || tierFeatures.free
            })
          }
        } else {
          console.error('AccountSettingsTab: Failed to fetch user profile:', response.status, response.statusText)
          // Set default data when API fails
          setAccountData({
            accountTier: 'free',
            subscriptionStatus: 'active',
            usageMetrics: {
              profilesUsed: 0,
              profilesLimit: 50,
              storageUsed: 0,
              storageLimit: 1,
              apiCallsUsed: 0,
              apiCallsLimit: 0
            },
            billingInfo: {
              nextBillingDate: undefined,
              amount: 0,
              currency: 'USD'
            },
            features: tierFeatures.free
          })
        }
      } catch (error) {
        console.error('AccountSettingsTab: Failed to fetch account data:', error)
        // Set default data when request fails
        setAccountData({
          accountTier: 'free',
          subscriptionStatus: 'active',
          usageMetrics: {
            profilesUsed: 0,
            profilesLimit: 50,
            storageUsed: 0,
            storageLimit: 1,
            apiCallsUsed: 0,
            apiCallsLimit: 0
          },
          billingInfo: {
            nextBillingDate: undefined,
            amount: 0,
            currency: 'USD'
          },
          features: tierFeatures.free
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchAccountData()
  }, [])

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0 // Unlimited
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'past_due':
      case 'unpaid':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    }
  }

  if (isLoading) {
    return <LoadingState message="Loading account information..." />
  }

  if (!accountData) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load account data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Account Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                Current Plan
              </CardTitle>
              <CardDescription>
                Manage your subscription and billing preferences
              </CardDescription>
            </div>
            <Badge className={tierColors[accountData.accountTier]}>
              {accountData.accountTier.toUpperCase()}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon(accountData.subscriptionStatus)}
              <span className="text-sm">
                Status: <span className="font-medium capitalize">{accountData.subscriptionStatus}</span>
              </span>
            </div>
            {accountData.billingInfo.nextBillingDate && (
              <div className="text-sm text-muted-foreground">
                Next billing: {new Date(accountData.billingInfo.nextBillingDate).toLocaleDateString()}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Plan Features</h4>
              <ul className="space-y-1 text-sm">
                {accountData.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Billing Information</h4>
              <div className="space-y-1 text-sm">
                {accountData.billingInfo.amount ? (
                  <p>
                    <span className="font-medium">${accountData.billingInfo.amount}</span>
                    <span className="text-muted-foreground"> /{accountData.billingInfo.currency} monthly</span>
                  </p>
                ) : (
                  <p className="text-muted-foreground">Free plan - no billing</p>
                )}
                <Button variant="outline" size="sm" className="w-full">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Manage Billing
                  <ArrowUpRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Usage Overview
          </CardTitle>
          <CardDescription>
            Monitor your current usage across different features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Profiles Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">Candidate Profiles</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {accountData.usageMetrics.profilesUsed} / {
                  accountData.usageMetrics.profilesLimit === -1 ? '∞' : accountData.usageMetrics.profilesLimit
                }
              </span>
            </div>
            {accountData.usageMetrics.profilesLimit !== -1 && (
              <Progress 
                value={getUsagePercentage(accountData.usageMetrics.profilesUsed, accountData.usageMetrics.profilesLimit)}
                className="h-2"
              />
            )}
          </div>

          {/* Storage Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                <span className="font-medium">Storage</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {accountData.usageMetrics.storageUsed} GB / {accountData.usageMetrics.storageLimit} GB
              </span>
            </div>
            <Progress 
              value={getUsagePercentage(accountData.usageMetrics.storageUsed, accountData.usageMetrics.storageLimit)}
              className="h-2"
            />
          </div>

          {/* API Calls (if available) */}
          {accountData.usageMetrics.apiCallsLimit > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">API Calls</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {accountData.usageMetrics.apiCallsUsed.toLocaleString()} / {accountData.usageMetrics.apiCallsLimit.toLocaleString()}
                </span>
              </div>
              <Progress 
                value={getUsagePercentage(accountData.usageMetrics.apiCallsUsed, accountData.usageMetrics.apiCallsLimit)}
                className="h-2"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upgrade Options */}
      {accountData.accountTier === 'free' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5" />
              Upgrade Your Plan
            </CardTitle>
            <CardDescription>
              Unlock more features and increase your limits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Pro Plan</h3>
                  <Badge className={tierColors.pro}>POPULAR</Badge>
                </div>
                <p className="text-2xl font-bold">$29<span className="text-sm text-muted-foreground">/month</span></p>
                <ul className="space-y-1 text-sm">
                  {tierFeatures.pro.slice(0, 3).map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <Button className="w-full">Upgrade to Pro</Button>
              </div>

              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Team Plan</h3>
                  <Badge className={tierColors.team}>TEAM</Badge>
                </div>
                <p className="text-2xl font-bold">$99<span className="text-sm text-muted-foreground">/month</span></p>
                <ul className="space-y-1 text-sm">
                  {tierFeatures.team.slice(0, 3).map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <Button variant="outline" className="w-full">Upgrade to Team</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Account Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Account Management</CardTitle>
          <CardDescription>
            Manage your account settings and data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="justify-start" onClick={() => window.location.href = '/api/download-account-data'}>
              <Calendar className="h-4 w-4 mr-2" />
              Download Account Data
            </Button>
            <Button variant="outline" className="justify-start" onClick={() => window.location.href = '/profile/usage-history'}>
              <Activity className="h-4 w-4 mr-2" />
              View Usage History
            </Button>
          </div>
          
          <Separator />

          <TimezoneSettings />
          
          <Separator />
          
          <div className="space-y-2">
            <h4 className="font-medium text-red-600">Danger Zone</h4>
            <p className="text-sm text-muted-foreground">
              These actions cannot be undone. Please proceed with caution.
            </p>
            <Button variant="destructive" size="sm" onClick={() => {
              if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
                fetch('/api/delete-account', { method: 'DELETE' })
                  .then(response => {
                    if (response.ok) {
                      window.location.href = '/';
                    } else {
                      console.error('Failed to delete account');
                    }
                  })
                  .catch(error => {
                    console.error('Failed to delete account:', error);
                  });
              }
            }}>
              Delete Account
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}