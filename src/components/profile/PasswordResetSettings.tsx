
"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useClerk } from "@clerk/nextjs"

export function PasswordResetSettings() {
  const { openUserProfile } = useClerk()

  const handlePasswordReset = () => {
    openUserProfile({ startPath: '/security' })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Password</CardTitle>
        <CardDescription>
          Reset your password to secure your account.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={handlePasswordReset}>
          Reset Password
        </Button>
      </CardContent>
    </Card>
  )
}
