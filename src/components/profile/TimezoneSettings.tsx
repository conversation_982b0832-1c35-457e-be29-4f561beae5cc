
"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { timezones } from "@/lib/timezones"

export function TimezoneSettings() {
  const [selectedTimezone, setSelectedTimezone] = React.useState("")
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchTimezone = async () => {
      try {
        const response = await fetch("/api/user-timezone")
        if (response.ok) {
          const data = await response.json()
          setSelectedTimezone(data.timezone)
        }
      } catch (error) {
        console.error("Failed to fetch timezone:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTimezone()
  }, [])

  const handleTimezoneChange = (value: string) => {
    setSelectedTimezone(value)
  }

  const handleSaveChanges = async () => {
    try {
      const response = await fetch("/api/user-timezone", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ timezone: selectedTimezone }),
      })

      if (response.ok) {
        // Handle success (e.g., show a toast notification)
        console.log("Timezone updated successfully")
      } else {
        // Handle error
        console.error("Failed to update timezone")
      }
    } catch (error) {
      console.error("Failed to update timezone:", error)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Timezone</CardTitle>
        <CardDescription>
          Set your preferred timezone for accurate date and time display.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Select
          value={selectedTimezone}
          onValueChange={handleTimezoneChange}
          disabled={isLoading}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a timezone" />
          </SelectTrigger>
          <SelectContent>
            {timezones.map((tz) => (
              <SelectItem key={tz} value={tz}>
                {tz}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button onClick={handleSaveChanges} disabled={isLoading}>
          Save Changes
        </Button>
      </CardContent>
    </Card>
  )
}
