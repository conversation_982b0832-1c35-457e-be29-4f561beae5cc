"use client"

import * as React from "react"
import { useUser } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LoadingState } from "@/components/ui/LoadingStates"
import { TwoFactorSetup } from "@/components/security/TwoFactorSetup"
import { ActiveSessions } from "@/components/security/ActiveSessions"
import { SecurityAuditLog } from "@/components/security/SecurityAuditLog"
import { ActivityDashboard } from "@/components/security/ActivityDashboard"
import { PasswordResetSettings } from "@/components/profile/PasswordResetSettings"
import { 
  <PERSON>,
  Key,
  Eye,
  EyeOff,
  Smartphone,
  Monitor,
  Globe,
  Users,
  Lock,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Activity,
  Calendar,
  FileText,
  BarChart3
} from "lucide-react"

interface PrivacySettings {
  profile_visibility: 'private' | 'organization' | 'public'
  show_email: boolean
  show_phone: boolean
  show_location: boolean
}

interface SecurityData {
  twoFactorEnabled: boolean
  activeSessions: Array<{
    id: string
    device: string
    location: string
    lastActive: string
    isCurrent: boolean
  }>
  recentActivity: Array<{
    action: string
    timestamp: string
    location: string
  }>
  connectedAccounts: Array<{
    provider: string
    email: string
    connectedAt: string
  }>
}

const privacyOptions = [
  {
    value: 'private' as const,
    label: 'Private',
    description: 'Only you can see your profile',
    icon: Lock
  },
  {
    value: 'organization' as const,
    label: 'Organization',
    description: 'Visible to your organization members',
    icon: Users
  },
  {
    value: 'public' as const,
    label: 'Public',
    description: 'Visible to anyone on the platform',
    icon: Globe
  }
]

export function SecuritySettingsTab() {
  const { user } = useUser()
  const [privacySettings, setPrivacySettings] = React.useState<PrivacySettings | null>(null)
  const [securityData, setSecurityData] = React.useState<SecurityData | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)
  const [isSaving, setIsSaving] = React.useState(false)
  const [hasChanges, setHasChanges] = React.useState(false)

  React.useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('SecuritySettingsTab: Starting data fetch...')
        const response = await fetch('/api/user-profile')
        console.log('SecuritySettingsTab: Response status:', response.status)
        
        if (response.ok) {
          const data = await response.json()
          console.log('SecuritySettingsTab: Response data:', data)
          
          if (data.success && data.profile) {
            setPrivacySettings(data.profile.privacy_settings || {
              profile_visibility: 'organization',
              show_email: false,
              show_phone: false,
              show_location: true
            })
          }
        } else {
          console.error('SecuritySettingsTab: Failed to fetch user profile:', response.status, response.statusText)
          // Set default privacy settings when API fails
          setPrivacySettings({
            profile_visibility: 'organization',
            show_email: false,
            show_phone: false,
            show_location: true
          })
        }

        // Mock security data
        setSecurityData({
          twoFactorEnabled: false,
          activeSessions: [
            {
              id: '1',
              device: 'Chrome on macOS',
              location: 'San Francisco, CA',
              lastActive: '2025-06-27T04:00:00Z',
              isCurrent: true
            },
            {
              id: '2',
              device: 'Safari on iPhone',
              location: 'San Francisco, CA',
              lastActive: '2025-06-26T22:30:00Z',
              isCurrent: false
            }
          ],
          recentActivity: [
            {
              action: 'Profile updated',
              timestamp: '2025-06-27T03:45:00Z',
              location: 'San Francisco, CA'
            },
            {
              action: 'Signed in',
              timestamp: '2025-06-27T03:30:00Z',
              location: 'San Francisco, CA'
            }
          ],
          connectedAccounts: [
            {
              provider: 'Google',
              email: user?.primaryEmailAddress?.emailAddress || '',
              connectedAt: '2025-06-20T10:00:00Z'
            }
          ]
        })
      } catch (error) {
        console.error('SecuritySettingsTab: Failed to fetch security data:', error)
        // Set default privacy settings when request fails
        setPrivacySettings({
          profile_visibility: 'organization',
          show_email: false,
          show_phone: false,
          show_location: true
        })
        // Still set mock security data
        setSecurityData({
          twoFactorEnabled: false,
          activeSessions: [
            {
              id: '1',
              device: 'Chrome on macOS',
              location: 'San Francisco, CA',
              lastActive: '2025-06-27T04:00:00Z',
              isCurrent: true
            }
          ],
          recentActivity: [
            {
              action: 'Profile updated',
              timestamp: '2025-06-27T03:45:00Z',
              location: 'San Francisco, CA'
            }
          ],
          connectedAccounts: [
            {
              provider: 'Google',
              email: user?.primaryEmailAddress?.emailAddress || '',
              connectedAt: '2025-06-20T10:00:00Z'
            }
          ]
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [user])

  const handlePrivacyChange = (key: keyof PrivacySettings, value: any) => {
    if (!privacySettings) return
    
    setPrivacySettings(prev => ({
      ...prev!,
      [key]: value
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    if (!privacySettings || !hasChanges) return

    setIsSaving(true)
    try {
      const response = await fetch('/api/user-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_profile',
          privacy_settings: privacySettings
        }),
      })

      const result = await response.json()
      
      if (result.success) {
        setHasChanges(false)
      } else {
        console.error('Failed to update privacy settings:', result.error)
      }
    } catch (error) {
      console.error('Error updating privacy settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return <LoadingState message="Loading security settings..." />
  }

  if (!privacySettings || !securityData) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load security data</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Security Header */}
      <div>
        <h2 className="text-xl font-semibold">Security & Privacy</h2>
        <p className="text-muted-foreground">
          Manage your account security, privacy settings, and activity monitoring
        </p>
      </div>

      {/* Security Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="2fa" className="flex items-center gap-2">
            <Smartphone className="h-4 w-4" />
            2FA Setup
          </TabsTrigger>
          <TabsTrigger value="sessions" className="flex items-center gap-2">
            <Monitor className="h-4 w-4" />
            Sessions
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Activity
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Audit Log
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* Two-Factor Authentication Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Two-Factor Authentication
                </CardTitle>
                <CardDescription>
                  Add an extra layer of security to your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      {securityData.twoFactorEnabled ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      )}
                      <span className="font-medium">
                        {securityData.twoFactorEnabled ? 'Enabled' : 'Not Enabled'}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {securityData.twoFactorEnabled 
                        ? 'Your account is protected with 2FA'
                        : 'Secure your account with authenticator app or SMS'
                      }
                    </p>
                  </div>
                  <Button 
                    variant={securityData.twoFactorEnabled ? "outline" : "default"}
                    className="flex items-center gap-2 bg-[#007AFF] hover:bg-[#0056b3] text-white"
                  >
                    <Smartphone className="h-4 w-4" />
                    {securityData.twoFactorEnabled ? 'Manage 2FA' : 'Enable 2FA'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <PasswordResetSettings />

            {/* Privacy Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Privacy Settings
                </CardTitle>
                <CardDescription>
                  Control who can see your profile and information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Visibility */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Profile Visibility</Label>
                  <div className="grid grid-cols-1 gap-3">
                    {privacyOptions.map((option) => {
                      const Icon = option.icon
                      const isSelected = privacySettings.profile_visibility === option.value
                      
                      return (
                        <div
                          key={option.value}
                          className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                            isSelected ? 'border-primary bg-primary/5' : 'border-border hover:bg-accent'
                          }`}
                          onClick={() => handlePrivacyChange('profile_visibility', option.value)}
                        >
                          <div className="flex items-center gap-3">
                            <Icon className="h-4 w-4" />
                            <div>
                              <p className="font-medium">{option.label}</p>
                              <p className="text-sm text-muted-foreground">{option.description}</p>
                            </div>
                            {isSelected && (
                              <CheckCircle className="h-4 w-4 text-primary ml-auto" />
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                <Separator />

                {/* Contact Information Visibility */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">Contact Information Visibility</Label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="show-email">Show Email Address</Label>
                        <p className="text-sm text-muted-foreground">Display your email on your profile</p>
                      </div>
                      <Switch
                        id="show-email"
                        checked={privacySettings.show_email}
                        onCheckedChange={(checked) => handlePrivacyChange('show_email', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="show-phone">Show Phone Number</Label>
                        <p className="text-sm text-muted-foreground">Display your phone number on your profile</p>
                      </div>
                      <Switch
                        id="show-phone"
                        checked={privacySettings.show_phone}
                        onCheckedChange={(checked) => handlePrivacyChange('show_phone', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="show-location">Show Location</Label>
                        <p className="text-sm text-muted-foreground">Display your location on your profile</p>
                      </div>
                      <Switch
                        id="show-location"
                        checked={privacySettings.show_location}
                        onCheckedChange={(checked) => handlePrivacyChange('show_location', checked)}
                      />
                    </div>
                  </div>
                </div>

                {/* Save Button */}
                {hasChanges && (
                  <div className="pt-4">
                    <Button 
                      onClick={handleSave} 
                      disabled={isSaving}
                      className="bg-[#007AFF] hover:bg-[#0056b3]"
                    >
                      {isSaving ? 'Saving...' : 'Save Privacy Settings'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="2fa">
          <TwoFactorSetup />
        </TabsContent>

        <TabsContent value="sessions">
          <ActiveSessions />
        </TabsContent>

        <TabsContent value="activity">
          <ActivityDashboard />
        </TabsContent>

        <TabsContent value="audit">
          <SecurityAuditLog />
        </TabsContent>
      </Tabs>
    </div>
  )
}