"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { LoadingState } from "@/components/ui/LoadingStates"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Shield, 
  Search,
  Filter,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  LogIn,
  LogOut,
  UserPlus,
  Settings,
  Key,
  Download,
  Calendar,
  MapPin,
  Monitor
} from "lucide-react"

interface SecurityEvent {
  id: string
  type: 'login' | 'logout' | 'password_change' | '2fa_enabled' | '2fa_disabled' | 'profile_update' | 'failed_login' | 'suspicious_activity'
  title: string
  description: string
  timestamp: string
  ipAddress: string
  location: string
  device: string
  status: 'success' | 'warning' | 'error'
  riskLevel: 'low' | 'medium' | 'high'
}

export function SecurityAuditLog() {
  const [events, setEvents] = React.useState<SecurityEvent[]>([])
  const [filteredEvents, setFilteredEvents] = React.useState<SecurityEvent[]>([])
  const [isLoading, setIsLoading] = React.useState(true)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [eventTypeFilter, setEventTypeFilter] = React.useState('all')
  const [riskFilter, setRiskFilter] = React.useState('all')

  React.useEffect(() => {
    const fetchSecurityEvents = async () => {
      try {
        // TODO: Implement security events API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const mockEvents: SecurityEvent[] = [
          {
            id: '1',
            type: 'login',
            title: 'Successful Login',
            description: 'User signed in successfully',
            timestamp: new Date().toISOString(),
            ipAddress: '*************',
            location: 'San Francisco, CA',
            device: 'Chrome on MacBook Pro',
            status: 'success',
            riskLevel: 'low'
          },
          {
            id: '2',
            type: 'profile_update',
            title: 'Profile Updated',
            description: 'Phone number changed',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            ipAddress: '*************',
            location: 'San Francisco, CA',
            device: 'Chrome on MacBook Pro',
            status: 'success',
            riskLevel: 'low'
          },
          {
            id: '3',
            type: 'failed_login',
            title: 'Failed Login Attempt',
            description: 'Invalid password entered 3 times',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            ipAddress: '************',
            location: 'Unknown Location',
            device: 'Firefox on Unknown Device',
            status: 'error',
            riskLevel: 'high'
          },
          {
            id: '4',
            type: '2fa_enabled',
            title: 'Two-Factor Authentication Enabled',
            description: 'TOTP authentication method activated',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            ipAddress: '*************',
            location: 'San Francisco, CA',
            device: 'Chrome on MacBook Pro',
            status: 'success',
            riskLevel: 'low'
          },
          {
            id: '5',
            type: 'suspicious_activity',
            title: 'Suspicious Login Location',
            description: 'Login from unusual geographic location',
            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            ipAddress: '*************',
            location: 'Moscow, Russia',
            device: 'Safari on iPhone',
            status: 'warning',
            riskLevel: 'medium'
          }
        ]
        
        setEvents(mockEvents)
        setFilteredEvents(mockEvents)
      } catch (error) {
        console.error('Failed to fetch security events:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSecurityEvents()
  }, [])

  React.useEffect(() => {
    let filtered = events

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.device.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply event type filter
    if (eventTypeFilter !== 'all') {
      filtered = filtered.filter(event => event.type === eventTypeFilter)
    }

    // Apply risk filter
    if (riskFilter !== 'all') {
      filtered = filtered.filter(event => event.riskLevel === riskFilter)
    }

    setFilteredEvents(filtered)
  }, [events, searchTerm, eventTypeFilter, riskFilter])

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <LogIn className="h-4 w-4" />
      case 'logout':
        return <LogOut className="h-4 w-4" />
      case 'password_change':
        return <Key className="h-4 w-4" />
      case '2fa_enabled':
      case '2fa_disabled':
        return <Shield className="h-4 w-4" />
      case 'profile_update':
        return <Settings className="h-4 w-4" />
      case 'failed_login':
        return <XCircle className="h-4 w-4" />
      case 'suspicious_activity':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'warning':
        return 'text-amber-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getRiskBadgeVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high':
        return 'destructive'
      case 'medium':
        return 'secondary'
      case 'low':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  const exportAuditLog = () => {
    const csvContent = [
      ['Timestamp', 'Event Type', 'Title', 'Description', 'IP Address', 'Location', 'Device', 'Status', 'Risk Level'],
      ...filteredEvents.map(event => [
        event.timestamp,
        event.type,
        event.title,
        event.description,
        event.ipAddress,
        event.location,
        event.device,
        event.status,
        event.riskLevel
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `security-audit-log-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
  }

  if (isLoading) {
    return <LoadingState message="Loading security audit log..." />
  }

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Audit Log
              </CardTitle>
              <CardDescription>
                Complete history of security-related events for your account
              </CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={exportAuditLog}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Event Type Filter */}
            <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Event Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Events</SelectItem>
                <SelectItem value="login">Login</SelectItem>
                <SelectItem value="logout">Logout</SelectItem>
                <SelectItem value="password_change">Password Change</SelectItem>
                <SelectItem value="2fa_enabled">2FA Enabled</SelectItem>
                <SelectItem value="2fa_disabled">2FA Disabled</SelectItem>
                <SelectItem value="profile_update">Profile Update</SelectItem>
                <SelectItem value="failed_login">Failed Login</SelectItem>
                <SelectItem value="suspicious_activity">Suspicious Activity</SelectItem>
              </SelectContent>
            </Select>

            {/* Risk Level Filter */}
            <Select value={riskFilter} onValueChange={setRiskFilter}>
              <SelectTrigger className="w-full md:w-[140px]">
                <SelectValue placeholder="Risk Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Risks</SelectItem>
                <SelectItem value="low">Low Risk</SelectItem>
                <SelectItem value="medium">Medium Risk</SelectItem>
                <SelectItem value="high">High Risk</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Events Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>
                {filteredEvents.length} of {events.length} events shown
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8">
              <Filter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium mb-2">No events found</h3>
              <p className="text-sm text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Location & Device</TableHead>
                    <TableHead>Risk Level</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvents.map((event) => {
                    const { date, time } = formatTimestamp(event.timestamp)
                    return (
                      <TableRow key={event.id} className={event.riskLevel === 'high' ? 'bg-red-50' : ''}>
                        <TableCell>
                          <div className="flex items-start gap-3">
                            <div className={`mt-1 ${getStatusColor(event.status)}`}>
                              {getEventIcon(event.type)}
                            </div>
                            <div>
                              <div className="font-medium">{event.title}</div>
                              <div className="text-sm text-muted-foreground">
                                {event.description}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {date}
                            </div>
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              {time}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="flex items-center gap-1 mb-1">
                              <MapPin className="h-3 w-3" />
                              {event.location}
                            </div>
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <Monitor className="h-3 w-3" />
                              {event.device}
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {event.ipAddress}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRiskBadgeVariant(event.riskLevel)}>
                            {event.riskLevel} risk
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className={`flex items-center gap-1 text-sm ${getStatusColor(event.status)}`}>
                            {event.status === 'success' && <CheckCircle className="h-4 w-4" />}
                            {event.status === 'warning' && <AlertTriangle className="h-4 w-4" />}
                            {event.status === 'error' && <XCircle className="h-4 w-4" />}
                            {event.status}
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="font-medium">Successful Events</span>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {events.filter(e => e.status === 'success').length}
              </div>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <span className="font-medium">Warning Events</span>
              </div>
              <div className="text-2xl font-bold text-amber-600">
                {events.filter(e => e.status === 'warning').length}
              </div>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <span className="font-medium">Critical Events</span>
              </div>
              <div className="text-2xl font-bold text-red-600">
                {events.filter(e => e.status === 'error').length}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
