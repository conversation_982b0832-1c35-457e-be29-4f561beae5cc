'use client'

import { ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Lock, Crown, Zap } from 'lucide-react'
import { 
  validateOrganizationAccess,
  getUpgradeRequirement,
  type AccountTier 
} from '@/lib/services/tier-gating'

interface TierGatedFeatureProps {
  children: ReactNode
  requiredTier: AccountTier
  currentTier: AccountTier
  feature: 'organizationAccess' | 'createOrganization' | 'adminAccess' | 'inviteMembers'
  fallback?: ReactNode
  showUpgradePrompt?: boolean
  compact?: boolean
  onUpgradeClick?: () => void
}

const featureConfig = {
  organizationAccess: {
    title: 'Team Collaboration',
    description: 'Join your organization and collaborate with team members',
    action: 'join' as const,
    icon: Crown,
    gradient: 'from-blue-50 to-indigo-50'
  },
  createOrganization: {
    title: 'Organization Management',
    description: 'Create and manage multiple organizations',
    action: 'create' as const,
    icon: Crown,
    gradient: 'from-purple-50 to-pink-50'
  },
  adminAccess: {
    title: 'Administration',
    description: 'Manage team settings and permissions',
    action: 'admin' as const,
    icon: Crown,
    gradient: 'from-amber-50 to-orange-50'
  },
  inviteMembers: {
    title: 'Team Invitations',
    description: 'Invite and manage team members',
    action: 'invite' as const,
    icon: Zap,
    gradient: 'from-green-50 to-emerald-50'
  }
}

export function TierGatedFeature({
  children,
  requiredTier,
  currentTier,
  feature,
  fallback,
  showUpgradePrompt = true,
  compact = false,
  onUpgradeClick
}: TierGatedFeatureProps) {
  const config = featureConfig[feature]
  const validation = validateOrganizationAccess(config.action, currentTier)
  
  // If user has access, render children
  if (validation.allowed) {
    return <>{children}</>
  }

  // If custom fallback provided, use it
  if (fallback) {
    return <>{fallback}</>
  }

  // If upgrade prompt disabled, render nothing
  if (!showUpgradePrompt) {
    return null
  }

  const upgradeRequirement = getUpgradeRequirement(currentTier, config.action)
  const IconComponent = config.icon

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 border border-dashed border-muted-foreground/40 rounded-lg bg-muted/20">
        <div className="flex items-center gap-2">
          <Lock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            {config.title} requires {requiredTier} plan
          </span>
        </div>
        <Button size="sm" variant="outline" onClick={onUpgradeClick}>
          Upgrade
        </Button>
      </div>
    )
  }

  return (
    <Card className={`relative overflow-hidden bg-gradient-to-r ${config.gradient}`}>
      <CardHeader className="relative">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <IconComponent className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">{config.title}</CardTitle>
          </div>
          <Badge variant="secondary" className="bg-white/80">
            <Lock className="h-3 w-3 mr-1" />
            {requiredTier} Plan
          </Badge>
        </div>
        <CardDescription className="text-base">
          {config.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current limitation message */}
        <div className="p-3 bg-white/60 rounded-lg border border-white/80">
          <p className="text-sm text-muted-foreground">
            {validation.message}
          </p>
        </div>

        {/* Upgrade benefits */}
        {upgradeRequirement && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">What you'll unlock:</h4>
            <ul className="space-y-2">
              {upgradeRequirement.benefits.slice(0, 3).map((benefit, index) => (
                <li key={index} className="flex items-center gap-2 text-sm">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex gap-2 pt-2">
          <Button 
            onClick={onUpgradeClick}
            className="flex-1"
            size="sm"
          >
            <Crown className="h-4 w-4 mr-2" />
            Upgrade to {requiredTier}
          </Button>
          <Button 
            variant="ghost" 
            size="sm"
            className="text-muted-foreground hover:text-foreground"
          >
            Learn More
          </Button>
        </div>

        {/* Pricing hint */}
        {upgradeRequirement?.pricing && (
          <p className="text-xs text-center text-muted-foreground">
            Starting at ${upgradeRequirement.pricing.annually}/month
          </p>
        )}
      </CardContent>
    </Card>
  )
}

// Convenience wrapper for organization features
export function OrgGatedFeature({
  children,
  currentTier,
  onUpgradeClick,
  ...props
}: Omit<TierGatedFeatureProps, 'requiredTier' | 'feature'> & {
  currentTier: AccountTier
  onUpgradeClick?: () => void
}) {
  return (
    <TierGatedFeature
      requiredTier="team"
      feature="organizationAccess"
      currentTier={currentTier}
      onUpgradeClick={onUpgradeClick}
      {...props}
    >
      {children}
    </TierGatedFeature>
  )
}

// Convenience wrapper for admin features
export function AdminGatedFeature({
  children,
  currentTier,
  onUpgradeClick,
  ...props
}: Omit<TierGatedFeatureProps, 'requiredTier' | 'feature'> & {
  currentTier: AccountTier
  onUpgradeClick?: () => void
}) {
  return (
    <TierGatedFeature
      requiredTier="team"
      feature="adminAccess"
      currentTier={currentTier}
      onUpgradeClick={onUpgradeClick}
      {...props}
    >
      {children}
    </TierGatedFeature>
  )
}
