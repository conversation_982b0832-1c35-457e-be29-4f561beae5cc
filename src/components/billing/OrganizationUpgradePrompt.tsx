'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Building, Crown, Users, ArrowRight, X } from 'lucide-react'
import { 
  getContextualUpgradeMessage,
  getUpgradeRequirement,
  type AccountTier 
} from '@/lib/services/tier-gating'

interface OrganizationUpgradePromptProps {
  currentTier: AccountTier
  organizationName?: string
  context: 'domain_detection' | 'join_attempt' | 'admin_attempt' | 'create_attempt'
  memberCount?: number
  variant?: 'card' | 'banner' | 'inline'
  dismissible?: boolean
  onUpgrade: () => void
  onDismiss?: () => void
}

export function OrganizationUpgradePrompt({
  currentTier,
  organizationName,
  context,
  memberCount,
  variant = 'card',
  dismissible = true,
  onUpgrade,
  onDismiss
}: OrganizationUpgradePromptProps) {
  const [isDismissed, setIsDismissed] = useState(false)

  if (isDismissed) return null

  const upgradeRequirement = getUpgradeRequirement(currentTier, 
    context === 'create_attempt' ? 'create_organization' : 'join_organization'
  )
  
  if (!upgradeRequirement) return null

  const contextualMessage = getContextualUpgradeMessage(context, organizationName)

  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }

  const urgencyColors = {
    high: 'border-red-200 bg-red-50',
    medium: 'border-amber-200 bg-amber-50', 
    low: 'border-blue-200 bg-blue-50'
  }

  const urgencyBadges = {
    high: 'bg-red-100 text-red-800',
    medium: 'bg-amber-100 text-amber-800',
    low: 'bg-blue-100 text-blue-800'
  }

  if (variant === 'banner') {
    return (
      <div className={`border-l-4 p-4 ${urgencyColors[upgradeRequirement.urgency]} border-l-primary`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <Crown className="h-5 w-5 text-primary mt-0.5" />
            <div>
              <h4 className="font-medium text-primary">
                {context === 'domain_detection' && organizationName
                  ? `${organizationName} is on TalentHUB!`
                  : 'Upgrade Required'}
              </h4>
              <p className="text-sm text-muted-foreground mt-1">
                {contextualMessage}
              </p>
              <div className="flex items-center gap-2 mt-2">
                <Button size="sm" onClick={onUpgrade}>
                  Upgrade to {upgradeRequirement.toTier}
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
                {dismissible && (
                  <Button size="sm" variant="ghost" onClick={handleDismiss}>
                    Later
                  </Button>
                )}
              </div>
            </div>
          </div>
          {dismissible && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handleDismiss}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    )
  }

  if (variant === 'inline') {
    return (
      <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border border-dashed">
        <div className="flex items-center gap-2">
          <Crown className="h-4 w-4 text-primary" />
          <span className="text-sm">
            {upgradeRequirement.toTier} plan required for team features
          </span>
        </div>
        <Button size="sm" variant="outline" onClick={onUpgrade}>
          Upgrade
        </Button>
      </div>
    )
  }

  // Default card variant
  return (
    <Card className={`relative ${urgencyColors[upgradeRequirement.urgency]}`}>
      {dismissible && (
        <Button
          size="sm"
          variant="ghost"
          onClick={handleDismiss}
          className="absolute top-2 right-2 text-muted-foreground hover:text-foreground"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
      
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Crown className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">
            {context === 'domain_detection' && organizationName
              ? `Join ${organizationName}`
              : `Upgrade to ${upgradeRequirement.toTier}`}
          </CardTitle>
          <Badge className={urgencyBadges[upgradeRequirement.urgency]}>
            {upgradeRequirement.urgency} priority
          </Badge>
        </div>
        <CardDescription>
          {contextualMessage}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Organization info */}
        {organizationName && (
          <div className="flex items-center gap-3 p-3 bg-white/60 rounded-lg">
            <Building className="h-5 w-5 text-blue-600" />
            <div>
              <p className="font-medium">{organizationName}</p>
              {memberCount && (
                <p className="text-sm text-muted-foreground">
                  <Users className="h-3 w-3 inline mr-1" />
                  {memberCount} team members
                </p>
              )}
            </div>
          </div>
        )}

        {/* Key benefits */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">What you'll unlock:</h4>
          <ul className="space-y-1">
            {upgradeRequirement.benefits.slice(0, 3).map((benefit, index) => (
              <li key={index} className="text-sm flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        {/* Pricing */}
        {upgradeRequirement.pricing && (
          <div className="p-3 bg-white/60 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  ${upgradeRequirement.pricing.annually}/month
                </p>
                <p className="text-xs text-muted-foreground">
                  Billed annually • Save 20%
                </p>
              </div>
              <Badge variant="secondary">
                Professional
              </Badge>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex gap-2">
          <Button onClick={onUpgrade} className="flex-1">
            <Crown className="h-4 w-4 mr-2" />
            Upgrade Now
          </Button>
          {dismissible && (
            <Button variant="outline" onClick={handleDismiss}>
              Later
            </Button>
          )}
        </div>

        {/* Trust indicators */}
        <div className="text-xs text-center text-muted-foreground">
          <p>✓ 30-day money back guarantee • ✓ Cancel anytime</p>
        </div>
      </CardContent>
    </Card>
  )
}
