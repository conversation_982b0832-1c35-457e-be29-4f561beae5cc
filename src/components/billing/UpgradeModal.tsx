/**
 * UpgradeModal Component
 * 
 * Professional upgrade modal integrating Clerk's PricingTable with
 * TalentHUB's design system. Provides contextual upgrade flows
 * based on user needs and current usage patterns.
 * 
 * Features:
 * - Clerk PricingTable integration
 * - Contextual upgrade messaging
 * - Usage-based recommendations
 * - Professional TalentHUB styling
 * - Mobile-responsive design
 */

'use client'

import { useState } from 'react'
import { PricingTable } from '@clerk/nextjs'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogClose 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  Crown, 
  Zap, 
  X, 
  CheckCircle, 
  TrendingUp, 
  Users, 
  Building2,
  AlertTriangle
} from 'lucide-react'
import { useSubscription, useUpgradeFlow } from '@/hooks/useSubscription'
import type { AccountTier } from '@/lib/services/tier-gating'

interface UpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  context?: 'general' | 'feature_access' | 'usage_limit' | 'organization' | 'api_access'
  feature?: string
  usageType?: string
}

/**
 * Main upgrade modal component
 */
export function UpgradeModal({
  isOpen,
  onClose,
  context = 'general',
  feature,
  usageType
}: UpgradeModalProps) {
  const { subscription, usage } = useSubscription()
  const { handleUpgradeSuccess } = useUpgradeFlow()
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  // Close modal and handle upgrade success
  const handleClose = () => {
    if (selectedPlan) {
      handleUpgradeSuccess()
    }
    onClose()
  }

  // Get contextual content based on upgrade reason
  const contextualContent = getContextualContent(
    context, 
    subscription?.talentHubTier || 'free',
    feature,
    usageType,
    usage
  )

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
        {/* Header */}
        <div className="sticky top-0 bg-background border-b p-6 z-10">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <contextualContent.icon className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <DialogTitle className="text-xl font-semibold">
                    {contextualContent.title}
                  </DialogTitle>
                  <DialogDescription className="text-base mt-1">
                    {contextualContent.description}
                  </DialogDescription>
                </div>
              </div>
              <DialogClose asChild>
                <Button variant="ghost" size="icon" className="shrink-0">
                  <X className="h-4 w-4" />
                </Button>
              </DialogClose>
            </div>
          </DialogHeader>

          {/* Context-specific alert */}
          {contextualContent.alert && (
            <div className={`mt-4 p-4 rounded-lg border ${contextualContent.alert.variant === 'warning' 
              ? 'bg-amber-50 border-amber-200' 
              : 'bg-blue-50 border-blue-200'}`}>
              <div className="flex items-center gap-2">
                {contextualContent.alert.variant === 'warning' ? (
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                ) : (
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                )}
                <p className={`text-sm font-medium ${
                  contextualContent.alert.variant === 'warning' 
                    ? 'text-amber-900' 
                    : 'text-blue-900'}`}>
                  {contextualContent.alert.message}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="p-6 space-y-6">
          {/* Benefits Preview */}
          {contextualContent.benefits && (
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-4">
                <h3 className="font-semibold text-blue-900 mb-3">
                  What you'll unlock:
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {contextualContent.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-600 shrink-0" />
                      <span className="text-sm text-blue-800">{benefit}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Usage Statistics (if relevant) */}
          {context === 'usage_limit' && usage && usageType && (
            <UsageStatistics usage={usage} usageType={usageType} />
          )}

          {/* Clerk Pricing Table */}
          <div className="rounded-lg border bg-white overflow-hidden">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="font-semibold text-gray-900">Choose your plan</h3>
              <p className="text-sm text-gray-600 mt-1">
                Start your upgrade immediately with secure Stripe checkout
              </p>
            </div>
            
            <div className="p-4">
              <PricingTable />
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Cancel anytime</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Secure payment</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Instant access</span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t bg-gray-50 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">
                Questions about pricing or features?
              </p>
              <Button variant="link" className="h-auto p-0 text-sm">
                Contact our team
              </Button>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Crown className="h-3 w-3 mr-1" />
              30-day money-back guarantee
            </Badge>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Usage statistics display for usage-based upgrades
 */
function UsageStatistics({ 
  usage, 
  usageType 
}: { 
  usage: any
  usageType: string 
}) {
  const usageData = usage[usageType]
  const percentage = usageData.limit ? (usageData.current / usageData.limit) * 100 : 0

  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-amber-900">Current Usage</h3>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800">
            {Math.round(percentage)}% used
          </Badge>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-amber-700">
              {usageType.charAt(0).toUpperCase() + usageType.slice(1)}
            </span>
            <span className="font-medium text-amber-900">
              {usageData.current} / {usageData.limit || '∞'}
            </span>
          </div>
          <Progress 
            value={Math.min(percentage, 100)} 
            className="bg-amber-200"
          />
        </div>
        
        <p className="text-xs text-amber-700 mt-2">
          Upgrade to remove all limits and get unlimited access
        </p>
      </CardContent>
    </Card>
  )
}

/**
 * Gets contextual content based on upgrade reason
 */
function getContextualContent(
  context: string,
  currentTier: AccountTier,
  feature?: string,
  usageType?: string,
  usage?: any
) {
  const baseContent = {
    general: {
      title: 'Upgrade Your TalentHUB Plan',
      description: 'Unlock advanced features and remove limitations',
      icon: Crown,
      benefits: [
        'Unlimited candidate records',
        'Advanced search and filtering',
        'API access for integrations',
        'Export and reporting tools',
        'Priority customer support',
        'Advanced analytics dashboard'
      ]
    },
    
    feature_access: {
      title: 'Unlock Premium Features',
      description: 'This feature requires a paid subscription to access',
      icon: Zap,
      alert: {
        variant: 'info',
        message: 'Upgrade now to immediately access this feature and many more.'
      },
      benefits: [
        'Immediate access to requested feature',
        'All premium features included',
        'No feature restrictions',
        'Advanced customization options'
      ]
    },
    
    usage_limit: {
      title: 'You\'ve Hit Your Usage Limit',
      description: 'Upgrade to remove all limits and continue growing',
      icon: TrendingUp,
      alert: {
        variant: 'warning',
        message: 'Your current plan limits have been reached. Upgrade to continue using TalentHUB without restrictions.'
      },
      benefits: [
        'Remove all usage limits',
        'Unlimited records and data',
        'Scale without restrictions',
        'Advanced usage analytics'
      ]
    },
    
    organization: {
      title: 'Join Your Team on TalentHUB',
      description: 'Collaborate with your organization and team members',
      icon: Users,
      alert: {
        variant: 'info',
        message: 'Your organization is already on TalentHUB. Upgrade to join them and start collaborating.'
      },
      benefits: [
        'Join your company team',
        'Shared candidate database',
        'Team collaboration tools',
        'Role-based permissions',
        'Organization-wide insights'
      ]
    },
    
    api_access: {
      title: 'API Access Required',
      description: 'Integrate TalentHUB with your existing tools and workflows',
      icon: Building2,
      benefits: [
        'Full REST API access',
        '10,000+ API calls per month',
        'Webhook integrations',
        'Custom automations',
        'Third-party integrations'
      ]
    }
  }

  return baseContent[context as keyof typeof baseContent] || baseContent.general
}

/**
 * Convenience hook for upgrade modal management
 */
export function useUpgradeModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [context, setContext] = useState<UpgradeModalProps['context']>('general')
  const [feature, setFeature] = useState<string>()
  const [usageType, setUsageType] = useState<string>()

  const openUpgradeModal = (
    upgradeContext: UpgradeModalProps['context'] = 'general',
    upgradeFeature?: string,
    upgradeUsageType?: string
  ) => {
    setContext(upgradeContext)
    setFeature(upgradeFeature)
    setUsageType(upgradeUsageType)
    setIsOpen(true)
  }

  const closeUpgradeModal = () => {
    setIsOpen(false)
    setContext('general')
    setFeature(undefined)
    setUsageType(undefined)
  }

  return {
    isOpen,
    context,
    feature,
    usageType,
    openUpgradeModal,
    closeUpgradeModal,
    UpgradeModal: () => (
      <UpgradeModal
        isOpen={isOpen}
        onClose={closeUpgradeModal}
        context={context}
        feature={feature}
        usageType={usageType}
      />
    )
  }
}
