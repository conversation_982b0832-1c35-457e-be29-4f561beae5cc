/**
 * FeatureGate Component
 * 
 * Enhanced feature gating component that integrates Clerk Billing with
 * TalentHUB's existing tier system. Provides seamless feature access
 * control with professional upgrade prompts.
 * 
 * Features:
 * - Clerk Billing integration
 * - Professional upgrade prompts
 * - Usage-based gating
 * - Real-time subscription status
 * - TalentHUB styling consistency
 */

'use client'

import { ReactNode } from 'react'
import { Protect } from '@clerk/nextjs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Lock, Crown, Zap, TrendingUp, AlertTriangle } from 'lucide-react'
import { useSubscription, useFeatureAccess, useUpgradeFlow } from '@/hooks/useSubscription'
import { CLERK_FEATURE_SLUGS } from '@/lib/billing/clerk-integration'
import type { AccountTier } from '@/lib/services/tier-gating'

interface FeatureGateProps {
  children: ReactNode
  feature?: string
  plan?: string
  tier?: AccountTier
  usageType?: 'records' | 'apiCalls' | 'storage'
  fallback?: ReactNode
  showUpgradePrompt?: boolean
  compact?: boolean
  onUpgradeClick?: () => void
}

/**
 * Main FeatureGate component with Clerk integration
 */
export function FeatureGate({
  children,
  feature,
  plan,
  tier,
  usageType,
  fallback,
  showUpgradePrompt = true,
  compact = false,
  onUpgradeClick
}: FeatureGateProps) {
  const { subscription, usage, isLoading } = useSubscription()
  const { triggerUpgrade } = useUpgradeFlow()

  // If using Clerk feature/plan protection
  if (feature || plan) {
    return (
      <Protect
        feature={feature}
        plan={plan}
        fallback={
          fallback || (showUpgradePrompt ? (
            <UpgradePrompt
              feature={feature}
              plan={plan}
              compact={compact}
              onUpgradeClick={onUpgradeClick || (() => triggerUpgrade('feature_access'))}
            />
          ) : null)
        }
      >
        {children}
      </Protect>
    )
  }

  // If using tier-based protection
  if (tier) {
    const hasAccess = subscription?.talentHubTier === tier || 
                     (tier === 'pro' && ['team', 'enterprise'].includes(subscription?.talentHubTier || '')) ||
                     (tier === 'team' && subscription?.talentHubTier === 'enterprise')

    if (hasAccess) {
      return <>{children}</>
    }

    if (fallback) {
      return <>{fallback}</>
    }

    if (!showUpgradePrompt) {
      return null
    }

    return (
      <TierUpgradePrompt
        requiredTier={tier}
        currentTier={subscription?.talentHubTier || 'free'}
        compact={compact}
        onUpgradeClick={onUpgradeClick || (() => triggerUpgrade('tier_access'))}
      />
    )
  }

  // If using usage-based protection
  if (usageType && usage) {
    const usageData = usage[usageType]
    const isOverLimit = usageData.limit !== null && usageData.current >= usageData.limit

    if (!isOverLimit) {
      return <>{children}</>
    }

    if (fallback) {
      return <>{fallback}</>
    }

    if (!showUpgradePrompt) {
      return null
    }

    return (
      <UsageLimitPrompt
        usageType={usageType}
        usageData={usageData}
        compact={compact}
        onUpgradeClick={onUpgradeClick || (() => triggerUpgrade('usage_limit'))}
      />
    )
  }

  // Default: render children if no restrictions
  return <>{children}</>
}

/**
 * Upgrade prompt for feature/plan access
 */
function UpgradePrompt({
  feature,
  plan,
  compact,
  onUpgradeClick
}: {
  feature?: string
  plan?: string
  compact: boolean
  onUpgradeClick: () => void
}) {
  const featureConfig = getFeatureConfig(feature, plan)

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 border border-dashed border-muted-foreground/40 rounded-lg bg-muted/20">
        <div className="flex items-center gap-2">
          <Lock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            {featureConfig.title} requires {featureConfig.requiredPlan}
          </span>
        </div>
        <Button size="sm" variant="outline" onClick={onUpgradeClick}>
          Upgrade
        </Button>
      </div>
    )
  }

  return (
    <Card className={`relative overflow-hidden bg-gradient-to-r ${featureConfig.gradient}`}>
      <CardHeader className="relative">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <featureConfig.icon className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">{featureConfig.title}</CardTitle>
          </div>
          <Badge variant="secondary" className="bg-white/80">
            <Lock className="h-3 w-3 mr-1" />
            {featureConfig.requiredPlan}
          </Badge>
        </div>
        <CardDescription className="text-base">
          {featureConfig.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="p-3 bg-white/60 rounded-lg border border-white/80">
          <p className="text-sm text-muted-foreground">
            Upgrade to unlock this feature and enhance your TalentHUB experience.
          </p>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium text-sm">What you'll unlock:</h4>
          <ul className="space-y-2">
            {featureConfig.benefits.map((benefit, index) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex gap-2 pt-2">
          <Button onClick={onUpgradeClick} className="flex-1" size="sm">
            <Crown className="h-4 w-4 mr-2" />
            Upgrade Now
          </Button>
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
            Learn More
          </Button>
        </div>

        <p className="text-xs text-center text-muted-foreground">
          Starting at $9/month • Cancel anytime
        </p>
      </CardContent>
    </Card>
  )
}

/**
 * Upgrade prompt for tier access
 */
function TierUpgradePrompt({
  requiredTier,
  currentTier,
  compact,
  onUpgradeClick
}: {
  requiredTier: AccountTier
  currentTier: AccountTier
  compact: boolean
  onUpgradeClick: () => void
}) {
  const tierConfig = getTierConfig(requiredTier)

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 border border-dashed border-muted-foreground/40 rounded-lg bg-muted/20">
        <div className="flex items-center gap-2">
          <Lock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            Requires {tierConfig.displayName} plan
          </span>
        </div>
        <Button size="sm" variant="outline" onClick={onUpgradeClick}>
          Upgrade
        </Button>
      </div>
    )
  }

  return (
    <Card className={`relative overflow-hidden bg-gradient-to-r ${tierConfig.gradient}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">{tierConfig.title}</CardTitle>
          </div>
          <Badge variant="secondary" className="bg-white/80">
            <Lock className="h-3 w-3 mr-1" />
            {tierConfig.displayName}
          </Badge>
        </div>
        <CardDescription className="text-base">
          {tierConfig.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="p-3 bg-white/60 rounded-lg border border-white/80">
          <p className="text-sm text-muted-foreground">
            Your current plan: <strong>{currentTier}</strong>
          </p>
        </div>

        <div className="flex gap-2">
          <Button onClick={onUpgradeClick} className="flex-1" size="sm">
            <TrendingUp className="h-4 w-4 mr-2" />
            Upgrade to {tierConfig.displayName}
          </Button>
        </div>

        <p className="text-xs text-center text-muted-foreground">
          ${tierConfig.price}/month • Cancel anytime
        </p>
      </CardContent>
    </Card>
  )
}

/**
 * Usage limit prompt
 */
function UsageLimitPrompt({
  usageType,
  usageData,
  compact,
  onUpgradeClick
}: {
  usageType: string
  usageData: { current: number; limit: number | null; percentage: number }
  compact: boolean
  onUpgradeClick: () => void
}) {
  const usageConfig = getUsageConfig(usageType)

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 border border-dashed border-amber-200 rounded-lg bg-amber-50">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <span className="text-sm text-amber-800">
            {usageConfig.title} limit reached
          </span>
        </div>
        <Button size="sm" variant="outline" onClick={onUpgradeClick}>
          Upgrade
        </Button>
      </div>
    )
  }

  return (
    <Card className="relative overflow-hidden bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-600" />
            <CardTitle className="text-lg text-amber-900">Usage Limit Reached</CardTitle>
          </div>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800">
            {usageData.current}/{usageData.limit} {usageConfig.unit}
          </Badge>
        </div>
        <CardDescription className="text-amber-700">
          You've reached your {usageConfig.title.toLowerCase()} limit for this month.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-amber-700">Current Usage</span>
            <span className="font-medium text-amber-900">
              {usageData.current}/{usageData.limit} {usageConfig.unit}
            </span>
          </div>
          <Progress value={100} className="bg-amber-200" />
        </div>

        <div className="p-3 bg-white/60 rounded-lg border border-amber-200">
          <p className="text-sm text-amber-800">
            Upgrade to get unlimited {usageConfig.title.toLowerCase()} and unlock advanced features.
          </p>
        </div>

        <div className="flex gap-2">
          <Button onClick={onUpgradeClick} className="flex-1" size="sm">
            <Zap className="h-4 w-4 mr-2" />
            Upgrade for Unlimited
          </Button>
        </div>

        <p className="text-xs text-center text-amber-700">
          Starting at $9/month • Immediate access
        </p>
      </CardContent>
    </Card>
  )
}

/**
 * Configuration helpers
 */
function getFeatureConfig(feature?: string, plan?: string) {
  const configs = {
    [CLERK_FEATURE_SLUGS.UNLIMITED_RECORDS]: {
      title: 'Unlimited Records',
      description: 'Remove limits and manage unlimited candidates and jobs',
      requiredPlan: 'Pro Plan',
      icon: Zap,
      gradient: 'from-blue-50 to-indigo-50',
      benefits: ['Unlimited candidate records', 'Unlimited job postings', 'Advanced search filters', 'Bulk operations']
    },
    [CLERK_FEATURE_SLUGS.API_ACCESS]: {
      title: 'API Access',
      description: 'Integrate TalentHUB with your existing tools and workflows',
      requiredPlan: 'Pro Plan',
      icon: Zap,
      gradient: 'from-green-50 to-emerald-50',
      benefits: ['Full REST API access', '10,000 API calls/month', 'Webhook integrations', 'Custom automations']
    },
    [CLERK_FEATURE_SLUGS.ORGANIZATION_ACCESS]: {
      title: 'Team Collaboration',
      description: 'Join your organization and collaborate with team members',
      requiredPlan: 'Team Plan',
      icon: Crown,
      gradient: 'from-purple-50 to-pink-50',
      benefits: ['Join your company team', 'Shared candidate database', 'Team workflows', 'Role-based permissions']
    }
  }

  const key = feature || plan || 'default'
  return configs[key as keyof typeof configs] || {
    title: 'Premium Feature',
    description: 'This feature requires a paid subscription',
    requiredPlan: 'Pro Plan',
    icon: Lock,
    gradient: 'from-gray-50 to-slate-50',
    benefits: ['Enhanced functionality', 'Professional features', 'Priority support', 'Advanced capabilities']
  }
}

function getTierConfig(tier: AccountTier) {
  const configs = {
    pro: { displayName: 'Pro', title: 'Individual Pro Features', description: 'Advanced features for individual recruiters', price: 9, gradient: 'from-blue-50 to-indigo-50' },
    team: { displayName: 'Team', title: 'Team Collaboration', description: 'Collaborate with your team and organization', price: 19, gradient: 'from-purple-50 to-pink-50' },
    enterprise: { displayName: 'Enterprise', title: 'Enterprise Features', description: 'Full-featured plan for large organizations', price: 49, gradient: 'from-amber-50 to-orange-50' }
  }
  return configs[tier] || configs.pro
}

function getUsageConfig(usageType: string) {
  const configs = {
    records: { title: 'Records', unit: 'records' },
    apiCalls: { title: 'API Calls', unit: 'calls' },
    storage: { title: 'Storage', unit: 'GB' }
  }
  return configs[usageType as keyof typeof configs] || { title: 'Usage', unit: 'items' }
}

// Convenience components
export function ProGate({ children, fallback, compact = false, onUpgradeClick }: Omit<FeatureGateProps, 'feature'>) {
  return <FeatureGate feature={CLERK_FEATURE_SLUGS.UNLIMITED_RECORDS} fallback={fallback} compact={compact} onUpgradeClick={onUpgradeClick}>{children}</FeatureGate>
}

export function TeamGate({ children, fallback, compact = false, onUpgradeClick }: Omit<FeatureGateProps, 'feature'>) {
  return <FeatureGate feature={CLERK_FEATURE_SLUGS.ORGANIZATION_ACCESS} fallback={fallback} compact={compact} onUpgradeClick={onUpgradeClick}>{children}</FeatureGate>
}

export function APIGate({ children, fallback, compact = false, onUpgradeClick }: Omit<FeatureGateProps, 'feature'>) {
  return <FeatureGate feature={CLERK_FEATURE_SLUGS.API_ACCESS} fallback={fallback} compact={compact} onUpgradeClick={onUpgradeClick}>{children}</FeatureGate>
}

export function UsageGate({ children, usageType, fallback, compact = false, onUpgradeClick }: { children: ReactNode; usageType: 'records' | 'apiCalls' | 'storage'; fallback?: ReactNode; compact?: boolean; onUpgradeClick?: () => void }) {
  return <FeatureGate usageType={usageType} fallback={fallback} compact={compact} onUpgradeClick={onUpgradeClick}>{children}</FeatureGate>
}
