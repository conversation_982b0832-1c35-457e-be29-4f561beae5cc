// Main preferences modal component
export { PreferencesModal } from './PreferencesModal'

// Hook for managing preferences state
export { usePreferences } from './hooks/usePreferences'

// Section components
export { ProfileSection } from './sections/ProfileSection'
export { DisplaySection } from './sections/DisplaySection'
export { AccountSection } from './sections/AccountSection'

// Field components
export { UsernameField } from './fields/UsernameField'
export { ThemeSelector } from './fields/ThemeSelector'
export { TimezoneSelector } from './fields/TimezoneSelector'

// Types
export type { PreferencesFormState } from './hooks/usePreferences'