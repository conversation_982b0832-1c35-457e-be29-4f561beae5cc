'use client'

import { Crown, Building2, ExternalLink, CheckCircle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { UserPreferencesWithOrg } from '@/lib/services/user-preferences'
import { getFeatureAvailability } from '@/lib/services/user-preferences'

interface AccountSectionProps {
  preferences: UserPreferencesWithOrg | null
  disabled?: boolean
  className?: string
}

/**
 * Account information section showing subscription tier and organization details
 */
export function AccountSection({
  preferences,
  disabled = false,
  className
}: AccountSectionProps) {
  const accountTier = preferences?.accountTier || 'free'
  const features = getFeatureAvailability(accountTier)
  const organization = preferences?.organization

  const tierConfig = {
    free: {
      name: 'Free Plan',
      color: 'bg-gray-100 text-gray-800',
      icon: null,
      description: 'Get started with basic features'
    },
    pro: {
      name: 'Pro Plan',
      color: 'bg-blue-100 text-blue-800',
      icon: Crown,
      description: 'Enhanced features for professionals'
    },
    team: {
      name: 'Team Plan',
      color: 'bg-purple-100 text-purple-800',
      icon: Crown,
      description: 'Advanced collaboration tools'
    },
    enterprise: {
      name: 'Enterprise Plan',
      color: 'bg-yellow-100 text-yellow-800',
      icon: Crown,
      description: 'Full-featured enterprise solution'
    }
  } as const

  const currentTier = tierConfig[accountTier] || tierConfig.free
  const TierIcon = currentTier.icon

  return (
    <div className={cn('space-y-6', className)}>
      {/* Section Header */}
      <div>
        <h3 className="text-lg font-semibold">Account Information</h3>
        <p className="text-sm text-muted-foreground">
          Your subscription details and organization association
        </p>
      </div>

      <Separator />

      {/* Account Tier */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">Account Type</h4>
            <p className="text-xs text-muted-foreground">
              {currentTier.description}
            </p>
          </div>
          <Badge className={cn('flex items-center gap-1', currentTier.color)}>
            {TierIcon && <TierIcon className="h-3 w-3" />}
            {currentTier.name}
          </Badge>
        </div>

        {/* Feature Overview */}
        <Card>
          <CardContent className="p-4">
            <h5 className="font-medium text-sm mb-3">Plan Features</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
              <FeatureItem
                label="Records Limit"
                value={features.maxRecords === -1 ? 'Unlimited' : features.maxRecords.toString()}
                included={true}
              />
              <FeatureItem
                label="API Access"
                value={features.apiAccess ? 'Included' : 'Not Available'}
                included={features.apiAccess}
              />
              <FeatureItem
                label="Custom Fields"
                value={features.customFields === -1 ? 'Unlimited' : features.customFields.toString()}
                included={features.customFields > 0}
              />
              <FeatureItem
                label="Integrations"
                value={features.integrations === -1 ? 'Unlimited' : features.integrations.toString()}
                included={features.integrations > 0}
              />
              <FeatureItem
                label="Advanced Search"
                value={features.advancedSearch ? 'Enabled' : 'Basic Only'}
                included={features.advancedSearch}
              />
              <FeatureItem
                label="Analytics"
                value={features.analytics ? 'Full Analytics' : 'Basic Stats'}
                included={features.analytics}
              />
            </div>
          </CardContent>
        </Card>

        {/* Upgrade Button */}
        {accountTier === 'free' && (
          <Button className="w-full" disabled={disabled}>
            <Crown className="h-4 w-4 mr-2" />
            Upgrade to Pro
            <ExternalLink className="h-3 w-3 ml-2" />
          </Button>
        )}
      </div>

      <Separator />

      {/* Organization Information */}
      <div className="space-y-4">
        <h4 className="font-medium">Organization</h4>
        
        {organization ? (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Building2 className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h5 className="font-medium">{organization.name}</h5>
                    <p className="text-xs text-muted-foreground">
                      {organization.type.replace('_', ' ').charAt(0).toUpperCase() + 
                       organization.type.replace('_', ' ').slice(1)} Organization
                    </p>
                    <Badge variant="secondary" className="mt-1 text-xs">
                      {preferences?.isOrgMember ? 'Member' : 'Domain Association'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="text-center py-6 border-2 border-dashed border-muted rounded-lg">
            <Building2 className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm font-medium">No Organization</p>
            <p className="text-xs text-muted-foreground mb-3">
              Domain-based association available
            </p>
            <Button variant="outline" size="sm" disabled={disabled}>
              Join Organization
              <ExternalLink className="h-3 w-3 ml-2" />
            </Button>
          </div>
        )}
      </div>

      {/* Subscription Status */}
      {preferences && (
        <div className="text-xs text-muted-foreground">
          <p>
            Status: <span className="capitalize">{preferences.subscriptionStatus}</span>
          </p>
          <p>
            Last updated: {new Date(preferences.updatedAt).toLocaleDateString()}
          </p>
        </div>
      )}
    </div>
  )
}

/**
 * Feature item component for displaying plan features
 */
function FeatureItem({
  label,
  value,
  included
}: {
  label: string
  value: string
  included: boolean
}) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-muted-foreground">{label}</span>
      <div className="flex items-center space-x-1">
        {included ? (
          <CheckCircle className="h-3 w-3 text-green-500" />
        ) : (
          <X className="h-3 w-3 text-red-500" />
        )}
        <span className={cn(
          'text-xs font-medium',
          included ? 'text-foreground' : 'text-muted-foreground'
        )}>
          {value}
        </span>
      </div>
    </div>
  )
}