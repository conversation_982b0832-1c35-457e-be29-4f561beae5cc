'use client'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { UsernameField } from '@/components/preferences/fields/UsernameField'
import { cn } from '@/lib/utils'
import type { PreferencesFormState } from '@/components/preferences/hooks/usePreferences'
import type { UsernameValidationResult } from '@/lib/services/username-validation'

interface ProfileSectionProps {
  formState: PreferencesFormState
  onFieldChange: (field: keyof PreferencesFormState, value: any) => void
  onUsernameValidation?: (result: UsernameValidationResult | null) => void
  disabled?: boolean
  className?: string
}

/**
 * Profile information section for preferences modal
 * Handles identity and username management with real-time validation
 */
export function ProfileSection({
  formState,
  onFieldChange,
  onUsernameValidation,
  disabled = false,
  className
}: ProfileSectionProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {/* Section Header */}
      <div>
        <h3 className="text-lg font-semibold">Profile Information</h3>
        <p className="text-sm text-muted-foreground">
          Manage your identity and how others see you on TalentHUB
        </p>
      </div>

      <Separator />

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Name Fields Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* First Name */}
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium">
              First Name *
            </Label>
            <Input
              id="firstName"
              type="text"
              value={formState.firstName}
              onChange={(e) => onFieldChange('firstName', e.target.value)}
              disabled={disabled}
              placeholder="Enter first name"
              className={cn(
                formState.errors.firstName?.length && 'border-red-500 focus:border-red-500'
              )}
            />
            {formState.errors.firstName?.map((error, index) => (
              <p key={index} className="text-xs text-red-600">
                {error}
              </p>
            ))}
          </div>

          {/* Last Name */}
          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-sm font-medium">
              Last Name *
            </Label>
            <Input
              id="lastName"
              type="text"
              value={formState.lastName}
              onChange={(e) => onFieldChange('lastName', e.target.value)}
              disabled={disabled}
              placeholder="Enter last name"
              className={cn(
                formState.errors.lastName?.length && 'border-red-500 focus:border-red-500'
              )}
            />
            {formState.errors.lastName?.map((error, index) => (
              <p key={index} className="text-xs text-red-600">
                {error}
              </p>
            ))}
          </div>
        </div>

        {/* Username Field with Real-time Validation */}
        <UsernameField
          value={formState.username}
          onChange={(value) => onFieldChange('username', value)}
          onValidationChange={onUsernameValidation}
          firstName={formState.firstName}
          lastName={formState.lastName}
          disabled={disabled}
        />

        {/* Display Name */}
        <div className="space-y-2">
          <Label htmlFor="displayName" className="text-sm font-medium">
            Display Name
            <span className="text-xs text-muted-foreground ml-1">(Optional)</span>
          </Label>
          <Input
            id="displayName"
            type="text"
            value={formState.displayName}
            onChange={(e) => onFieldChange('displayName', e.target.value)}
            disabled={disabled}
            placeholder="e.g., Senior Recruiter, Tech Lead"
            className={cn(
              formState.errors.displayName?.length && 'border-red-500 focus:border-red-500'
            )}
          />
          <p className="text-xs text-muted-foreground">
            Optional title or role that appears with your name
          </p>
          {formState.errors.displayName?.map((error, index) => (
            <p key={index} className="text-xs text-red-600">
              {error}
            </p>
          ))}
        </div>
      </div>

      {/* Profile Preview */}
      <div className="rounded-lg border bg-muted/30 p-4">
        <p className="text-xs font-medium text-muted-foreground mb-2">Preview</p>
        <div className="flex items-center space-x-3">
          {/* Avatar placeholder */}
          <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
            <span className="text-sm font-medium text-primary">
              {getInitials(formState.firstName, formState.lastName)}
            </span>
          </div>
          
          {/* Name display */}
          <div>
            <div className="font-medium text-sm">
              {getDisplayName(formState.firstName, formState.lastName, formState.displayName)}
            </div>
            <div className="text-xs text-muted-foreground">
              @{formState.username || 'username'}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Get user initials for avatar
 */
function getInitials(firstName: string, lastName: string): string {
  const first = firstName.trim().charAt(0).toUpperCase()
  const last = lastName.trim().charAt(0).toUpperCase()
  return `${first}${last}` || 'U'
}

/**
 * Get formatted display name
 */
function getDisplayName(firstName: string, lastName: string, displayName?: string): string {
  const fullName = `${firstName.trim()} ${lastName.trim()}`.trim()
  
  if (!fullName) return 'Your Name'
  
  if (displayName?.trim()) {
    return `${fullName} • ${displayName.trim()}`
  }
  
  return fullName
}