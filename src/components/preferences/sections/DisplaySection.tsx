'use client'

import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { ThemeSelector } from '@/components/preferences/fields/ThemeSelector'
import { TimezoneSelector } from '@/components/preferences/fields/TimezoneSelector'
import { cn } from '@/lib/utils'
import type { PreferencesFormState } from '@/components/preferences/hooks/usePreferences'

interface DisplaySectionProps {
  formState: PreferencesFormState
  onFieldChange: (field: keyof PreferencesFormState, value: any) => void
  disabled?: boolean
  className?: string
}

/**
 * Display preferences section for theme, timezone, and process context
 */
export function DisplaySection({
  formState,
  onFieldChange,
  disabled = false,
  className
}: DisplaySectionProps) {
  const processOptions = [
    {
      value: 'recruitment' as const,
      label: 'Recruitment',
      description: 'Focus on job placement workflow'
    },
    {
      value: 'bench_sales' as const,
      label: 'Bench Sales',
      description: 'Focus on internal resource workflow'
    },
    {
      value: 'both' as const,
      label: 'Both',
      description: 'Access to all features'
    }
  ]

  return (
    <div className={cn('space-y-6', className)}>
      {/* Section Header */}
      <div>
        <h3 className="text-lg font-semibold">Display Preferences</h3>
        <p className="text-sm text-muted-foreground">
          Customize your interface appearance and workflow focus
        </p>
      </div>

      <Separator />

      {/* Theme Selection */}
      <ThemeSelector
        value={formState.theme}
        onChange={(value) => onFieldChange('theme', value)}
        disabled={disabled}
      />

      <Separator className="my-6" />

      {/* Timezone Selection */}
      <TimezoneSelector
        value={formState.timezone}
        onChange={(value) => onFieldChange('timezone', value)}
        disabled={disabled}
      />

      <Separator className="my-6" />

      {/* Process Context */}
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Process Focus</Label>
          <p className="text-xs text-muted-foreground">
            Choose your primary workflow or access both
          </p>
        </div>

        <RadioGroup
          value={formState.processContext}
          onValueChange={(value) => onFieldChange('processContext', value)}
          disabled={disabled}
          className="space-y-3"
        >
          {processOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-3">
              <RadioGroupItem 
                value={option.value} 
                id={option.value}
                className="mt-0.5"
              />
              <Label 
                htmlFor={option.value}
                className="flex-1 cursor-pointer"
              >
                <div className="flex flex-col">
                  <span className="font-medium">{option.label}</span>
                  <span className="text-xs text-muted-foreground">
                    {option.description}
                  </span>
                </div>
              </Label>
            </div>
          ))}
        </RadioGroup>

        {/* Process Focus Preview */}
        <div className="rounded-lg border bg-muted/30 p-4">
          <p className="text-xs font-medium text-muted-foreground mb-2">
            Workflow Preview
          </p>
          <ProcessPreview processContext={formState.processContext} />
        </div>
      </div>
    </div>
  )
}

/**
 * Process context preview component
 */
function ProcessPreview({ 
  processContext 
}: { 
  processContext: 'recruitment' | 'bench_sales' | 'both' 
}) {
  const getPreviewContent = () => {
    switch (processContext) {
      case 'recruitment':
        return {
          title: 'Recruitment Workflow',
          features: ['Job Posting Management', 'Candidate Pipeline', 'Client Communications'],
          color: 'bg-blue-100 text-blue-800 border-blue-200'
        }
      case 'bench_sales':
        return {
          title: 'Bench Sales Workflow', 
          features: ['Resource Inventory', 'Vendor Management', 'Proposal Generation'],
          color: 'bg-green-100 text-green-800 border-green-200'
        }
      case 'both':
        return {
          title: 'Unified Workflow',
          features: ['All Features Available', 'Process Switching', 'Cross-Process Analytics'],
          color: 'bg-purple-100 text-purple-800 border-purple-200'
        }
      default:
        return {
          title: 'Select a Focus',
          features: ['Choose your workflow above'],
          color: 'bg-gray-100 text-gray-800 border-gray-200'
        }
    }
  }

  const preview = getPreviewContent()

  return (
    <div className={cn('rounded-md border p-3', preview.color)}>
      <h4 className="font-medium text-sm mb-2">{preview.title}</h4>
      <ul className="space-y-1">
        {preview.features.map((feature, index) => (
          <li key={index} className="text-xs flex items-center">
            <span className="w-1 h-1 rounded-full bg-current mr-2" />
            {feature}
          </li>
        ))}
      </ul>
    </div>
  )
}