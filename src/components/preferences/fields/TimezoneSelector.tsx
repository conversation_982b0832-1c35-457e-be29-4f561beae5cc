'use client'

import { useState, useMemo } from 'react'
import { Check, Clock, Search, MapPin } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { cn } from '@/lib/utils'
import { TIMEZONES_BY_REGION } from '@/lib/timezone'

interface TimezoneSelectorProps {
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  className?: string
}

/**
 * Smart timezone selector with search and regional grouping
 */
export function TimezoneSelector({
  value,
  onChange,
  disabled = false,
  className
}: TimezoneSelectorProps) {
  const [open, setOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')

  // Get display name for selected timezone
  const selectedTimezone = useMemo(() => {
    const selected = TIMEZONES_BY_REGION
      .flatMap(region => region.timezones)
      .find(tz => tz.value === value)
    
    return selected || {
      label: value,
      value: value,
      offset: getTimezoneOffset(value),
      city: value.split('/').pop()?.replace(/_/g, ' ') || value
    }
  }, [value])

  // Get current time in selected timezone
  const getCurrentTime = (timezone: string) => {
    try {
      const now = new Date()
      return now.toLocaleTimeString('en-US', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return '--:--'
    }
  }

  // Filter timezones based on search
  const filteredRegions = useMemo(() => {
    if (!searchValue) return TIMEZONES_BY_REGION

    const searchLower = searchValue.toLowerCase()
    return TIMEZONES_BY_REGION
      .map(region => ({
        ...region,
        timezones: region.timezones.filter(tz =>
          tz.label.toLowerCase().includes(searchLower) ||
          tz.city.toLowerCase().includes(searchLower) ||
          tz.value.toLowerCase().includes(searchLower)
        )
      }))
      .filter(region => region.timezones.length > 0)
  }, [searchValue])

  const handleSelect = (timezone: string) => {
    onChange(timezone)
    setOpen(false)
    setSearchValue('')
  }

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <Label className="text-sm font-medium">Timezone</Label>

      {/* Timezone Selector */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-auto p-3"
            disabled={disabled}
          >
            <div className="flex items-center space-x-3">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="text-left">
                <div className="font-medium">{selectedTimezone.city}</div>
                <div className="text-xs text-muted-foreground">
                  {selectedTimezone.offset} • {getCurrentTime(value)}
                </div>
              </div>
            </div>
            <div className="text-xs text-muted-foreground">
              {getCurrentTime(value)}
            </div>
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-[400px] p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="Search timezones..."
                value={searchValue}
                onValueChange={setSearchValue}
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            
            <CommandList className="max-h-[300px]">
              <CommandEmpty>No timezone found.</CommandEmpty>
              
              {filteredRegions.map((region) => (
                <CommandGroup key={region.region} heading={region.region}>
                  {region.timezones.map((timezone) => (
                    <CommandItem
                      key={timezone.value}
                      value={timezone.value}
                      onSelect={() => handleSelect(timezone.value)}
                      className="flex items-center justify-between px-3 py-2"
                    >
                      <div className="flex items-center space-x-3">
                        <MapPin className="h-3 w-3 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{timezone.city}</div>
                          <div className="text-xs text-muted-foreground">
                            {timezone.offset}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <div className="text-xs text-muted-foreground">
                          {getCurrentTime(timezone.value)}
                        </div>
                        {value === timezone.value && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Current Time Display */}
      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
        <Clock className="h-3 w-3" />
        <span>Current time: {getCurrentTime(value)}</span>
      </div>
    </div>
  )
}

/**
 * Get timezone offset string
 */
function getTimezoneOffset(timezone: string): string {
  try {
    const now = new Date()
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000))
    const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }))
    const offset = (targetTime.getTime() - utc.getTime()) / (1000 * 60 * 60)
    
    const sign = offset >= 0 ? '+' : ''
    const hours = Math.floor(Math.abs(offset))
    const minutes = Math.round((Math.abs(offset) - hours) * 60)
    
    return `UTC${sign}${offset >= 0 ? hours : -hours}${minutes > 0 ? `:${minutes.toString().padStart(2, '0')}` : ''}`
  } catch {
    return 'UTC+0'
  }
}