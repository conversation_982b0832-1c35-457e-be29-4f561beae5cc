'use client'

import { useState, useEffect, useCallback } from 'react'
import { Check, X, AlertCircle, Loader2 } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { 
  createDebouncedUsernameValidator,
  type UsernameValidationResult 
} from '@/lib/services/username-validation'

interface UsernameFieldProps {
  value: string
  onChange: (value: string) => void
  onValidationChange?: (result: UsernameValidationResult | null) => void
  firstName?: string
  lastName?: string
  disabled?: boolean
  className?: string
}

/**
 * Username input field with real-time validation and suggestions
 */
export function UsernameField({
  value,
  onChange,
  onValidationChange,
  firstName,
  lastName,
  disabled = false,
  className
}: UsernameFieldProps) {
  const [validation, setValidation] = useState<UsernameValidationResult | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Create debounced validator
  const debouncedValidator = useCallback(
    createDebouncedUsernameValidator(300),
    []
  )

  /**
   * Handle validation result
   */
  const handleValidationResult = useCallback((result: UsernameValidationResult) => {
    setValidation(result)
    setIsValidating(false)
    onValidationChange?.(result)
  }, [onValidationChange])

  /**
   * Handle username change with validation
   */
  const handleChange = useCallback((newValue: string) => {
    onChange(newValue)
    
    if (newValue.trim()) {
      setIsValidating(true)
      setValidation(null)
      debouncedValidator(newValue, handleValidationResult)
    } else {
      setValidation(null)
      setIsValidating(false)
      onValidationChange?.(null)
    }
  }, [onChange, debouncedValidator, handleValidationResult, onValidationChange])

  /**
   * Apply suggested username
   */
  const applySuggestion = useCallback((suggestion: string) => {
    handleChange(suggestion)
    setShowSuggestions(false)
  }, [handleChange])

  /**
   * Toggle suggestions visibility
   */
  const toggleSuggestions = useCallback(() => {
    setShowSuggestions(prev => !prev)
  }, [])

  // Show suggestions when validation has suggestions and field has errors
  useEffect(() => {
    if (validation?.suggestions?.length && !validation.isValid) {
      setShowSuggestions(true)
    }
  }, [validation])

  // Determine validation state
  const getValidationState = () => {
    if (!value.trim()) return 'neutral'
    if (isValidating) return 'validating'
    if (validation?.isValid) return 'valid'
    if (validation && !validation.isValid) return 'invalid'
    return 'neutral'
  }

  const validationState = getValidationState()

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <Label htmlFor="username" className="text-sm font-medium">
        Username *
      </Label>

      {/* Input with validation indicator */}
      <div className="relative">
        <Input
          id="username"
          type="text"
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled}
          placeholder="Enter username"
          className={cn(
            'pr-10',
            validationState === 'valid' && 'border-green-500 focus:border-green-500',
            validationState === 'invalid' && 'border-red-500 focus:border-red-500'
          )}
        />
        
        {/* Validation indicator */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {validationState === 'validating' && (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
          {validationState === 'valid' && (
            <Check className="h-4 w-4 text-green-500" />
          )}
          {validationState === 'invalid' && (
            <X className="h-4 w-4 text-red-500" />
          )}
        </div>
      </div>

      {/* Validation message */}
      {validation && (
        <div className="space-y-1">
          {validation.isValid ? (
            <p className="text-xs text-green-600 flex items-center gap-1">
              <Check className="h-3 w-3" />
              Available
            </p>
          ) : (
            <div className="space-y-1">
              {validation.errors.map((error, index) => (
                <p key={index} className="text-xs text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3 flex-shrink-0" />
                  {error}
                </p>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Suggestions */}
      {validation?.suggestions?.length && showSuggestions && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-xs text-muted-foreground">Suggestions:</p>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={toggleSuggestions}
              className="h-6 px-2 text-xs"
            >
              Hide
            </Button>
          </div>
          <div className="flex flex-wrap gap-1">
            {validation.suggestions.slice(0, 5).map((suggestion, index) => (
              <Button
                key={index}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => applySuggestion(suggestion)}
                className="h-7 px-2 text-xs hover:bg-primary hover:text-primary-foreground"
                disabled={disabled}
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Show suggestions button when hidden */}
      {validation?.suggestions?.length && !showSuggestions && !validation.isValid && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={toggleSuggestions}
          className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700"
        >
          Show suggestions ({validation.suggestions.length})
        </Button>
      )}
    </div>
  )
}