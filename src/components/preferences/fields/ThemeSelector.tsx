'use client'

import { Monitor, Moon, Sun } from 'lucide-react'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '@/lib/utils'

interface ThemeSelectorProps {
  value: 'light' | 'dark' | 'system'
  onChange: (value: 'light' | 'dark' | 'system') => void
  disabled?: boolean
  className?: string
}

/**
 * Theme selector with live preview functionality
 */
export function ThemeSelector({
  value,
  onChange,
  disabled = false,
  className
}: ThemeSelectorProps) {
  const themes = [
    {
      value: 'light' as const,
      label: 'Light',
      icon: Sun,
      description: 'Clean, bright interface'
    },
    {
      value: 'dark' as const,
      label: 'Dark',
      icon: Moon,
      description: 'Easy on the eyes'
    },
    {
      value: 'system' as const,
      label: 'System',
      icon: Monitor,
      description: 'Match device preference'
    }
  ]

  return (
    <div className={cn('space-y-4', className)}>
      {/* Label */}
      <Label className="text-sm font-medium">Theme</Label>

      {/* Radio Group */}
      <RadioGroup
        value={value}
        onValueChange={onChange}
        disabled={disabled}
        className="grid grid-cols-3 gap-3"
      >
        {themes.map((theme) => {
          const Icon = theme.icon
          const isSelected = value === theme.value
          
          return (
            <div key={theme.value} className="relative">
              <RadioGroupItem
                value={theme.value}
                id={theme.value}
                className="peer sr-only"
              />
              <Label
                htmlFor={theme.value}
                className={cn(
                  'flex flex-col items-center justify-center rounded-lg border-2 border-muted bg-background p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer transition-all',
                  disabled && 'opacity-50 cursor-not-allowed',
                  isSelected && 'border-primary bg-primary/5'
                )}
              >
                <Icon className={cn(
                  'h-5 w-5 mb-2',
                  isSelected ? 'text-primary' : 'text-muted-foreground'
                )} />
                <span className={cn(
                  'text-sm font-medium',
                  isSelected ? 'text-primary' : 'text-foreground'
                )}>
                  {theme.label}
                </span>
                <span className="text-xs text-muted-foreground text-center mt-1">
                  {theme.description}
                </span>
              </Label>
            </div>
          )
        })}
      </RadioGroup>

      {/* Live Preview Box */}
      <ThemePreview selectedTheme={value} />
    </div>
  )
}

/**
 * Live preview component showing theme appearance
 */
function ThemePreview({ selectedTheme }: { selectedTheme: 'light' | 'dark' | 'system' }) {
  // Determine actual theme to preview (for system, we'll show current preference)
  const previewTheme = selectedTheme === 'system' 
    ? (typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
    : selectedTheme

  return (
    <div className="rounded-lg border p-3 bg-muted/30">
      <p className="text-xs font-medium text-muted-foreground mb-2">Preview</p>
      
      {/* Mini UI Preview */}
      <div className={cn(
        'rounded-md border p-3 transition-colors',
        previewTheme === 'dark' 
          ? 'bg-gray-900 border-gray-700' 
          : 'bg-white border-gray-200'
      )}>
        {/* Header */}
        <div className={cn(
          'flex items-center justify-between mb-2',
          previewTheme === 'dark' ? 'text-gray-100' : 'text-gray-900'
        )}>
          <div className="flex items-center space-x-2">
            <div className={cn(
              'w-6 h-6 rounded',
              previewTheme === 'dark' ? 'bg-blue-600' : 'bg-blue-500'
            )} />
            <span className="text-xs font-medium">TalentHUB</span>
          </div>
          <div className={cn(
            'w-2 h-2 rounded-full',
            previewTheme === 'dark' ? 'bg-green-400' : 'bg-green-500'
          )} />
        </div>

        {/* Content */}
        <div className="space-y-1">
          <div className={cn(
            'h-2 rounded',
            previewTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
          )} />
          <div className={cn(
            'h-2 w-3/4 rounded',
            previewTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
          )} />
        </div>

        {/* Button */}
        <div className="mt-2">
          <div className={cn(
            'inline-block px-2 py-1 rounded text-xs',
            previewTheme === 'dark' 
              ? 'bg-blue-600 text-white' 
              : 'bg-blue-500 text-white'
          )}>
            Button
          </div>
        </div>
      </div>

      {/* Theme info */}
      <p className="text-xs text-muted-foreground mt-2">
        {selectedTheme === 'system' 
          ? `Currently showing ${previewTheme} (matches system preference)`
          : `Showing ${selectedTheme} theme`
        }
      </p>
    </div>
  )
}