'use client'

import { useState } from 'react'
import { Settings, Save, X, Loader2 } from 'lucide-react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { usePreferences } from '@/components/preferences/hooks/usePreferences'
import { ProfileSection } from '@/components/preferences/sections/ProfileSection'
import { DisplaySection } from '@/components/preferences/sections/DisplaySection'
import { AccountSection } from '@/components/preferences/sections/AccountSection'
import { cn } from '@/lib/utils'

interface PreferencesModalProps {
  children?: React.ReactNode
  defaultOpen?: boolean
  onOpenChange?: (open: boolean) => void
  className?: string
}

/**
 * Main preferences modal with Clerk-style design and comprehensive user management
 */
export function PreferencesModal({
  children,
  defaultOpen = false,
  onOpenChange,
  className
}: PreferencesModalProps) {
  const [open, setOpen] = useState(defaultOpen)
  const [activeTab, setActiveTab] = useState('profile')
  
  const {
    preferences,
    formState,
    usernameValidation,
    isValidatingUsername,
    isLoading,
    error,
    updateField,
    validateUsername,
    savePreferences,
    resetForm
  } = usePreferences()

  /**
   * Handle modal open/close
   */
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    onOpenChange?.(newOpen)
    
    // Reset form when closing without saving
    if (!newOpen && formState.isDirty) {
      resetForm()
    }
  }

  /**
   * Handle form submission
   */
  const handleSave = async () => {
    const result = await savePreferences()
    
    if (result.success) {
      handleOpenChange(false)
    }
    // Error handling is managed by the hook
  }

  /**
   * Handle username validation with debouncing
   */
  const handleUsernameChange = (value: string) => {
    updateField('username', value)
    if (value.trim()) {
      validateUsername(value)
    }
  }

  /**
   * Check if form can be saved
   */
  const canSave = () => {
    return (
      formState.isDirty &&
      formState.firstName.trim() &&
      formState.lastName.trim() &&
      formState.username.trim() &&
      usernameValidation?.isValid !== false &&
      !isValidatingUsername &&
      !formState.isSubmitting
    )
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children || (
          <Button 
            variant="ghost" 
            size="icon"
            className={cn('h-8 w-8', className)}
          >
            <Settings className="h-4 w-4" />
            <span className="sr-only">Open preferences</span>
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[90vh] p-0">
        {/* Header */}
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Preferences
          </DialogTitle>
          <DialogDescription>
            Manage your profile, display settings, and account information
          </DialogDescription>
        </DialogHeader>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2 text-sm text-muted-foreground">
              Loading preferences...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="px-6 py-2">
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* Content */}
        {!isLoading && (
          <>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
              <div className="px-6 py-2 border-b">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="profile">Profile</TabsTrigger>
                  <TabsTrigger value="display">Display</TabsTrigger>
                  <TabsTrigger value="account">Account</TabsTrigger>
                </TabsList>
              </div>

              <ScrollArea className="h-[500px]">
                <div className="px-6 py-6">
                  <TabsContent value="profile" className="mt-0">
                    <ProfileSection
                      formState={formState}
                      onFieldChange={updateField}
                      onUsernameValidation={(result) => {
                        // Username validation is handled by the hook
                      }}
                      disabled={formState.isSubmitting}
                    />
                  </TabsContent>

                  <TabsContent value="display" className="mt-0">
                    <DisplaySection
                      formState={formState}
                      onFieldChange={updateField}
                      disabled={formState.isSubmitting}
                    />
                  </TabsContent>

                  <TabsContent value="account" className="mt-0">
                    <AccountSection
                      preferences={preferences}
                      disabled={formState.isSubmitting}
                    />
                  </TabsContent>
                </div>
              </ScrollArea>
            </Tabs>

            {/* Footer */}
            <div className="flex items-center justify-between px-6 py-4 border-t bg-muted/30">
              <div className="text-xs text-muted-foreground">
                {formState.isDirty && 'You have unsaved changes'}
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  onClick={() => handleOpenChange(false)}
                  disabled={formState.isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                
                <Button
                  onClick={handleSave}
                  disabled={!canSave()}
                  className="min-w-[100px]"
                >
                  {formState.isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}