'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  Building2, 
  Users, 
  X, 
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import type { OrganizationDetectionWithRequests } from '@/lib/services/org-association'

interface OrgDetectionNotificationProps {
  detectionResult: OrganizationDetectionWithRequests
  userEmail: string
  onDismiss?: () => void
  onJoinSuccess?: () => void
}

interface MultipleOrgsNotificationProps {
  organizations: Array<{
    id: string
    name: string
    clerk_org_id: string
    slug: string
  }>
  userEmail: string
  onDismiss?: () => void
  onJoinSuccess?: () => void
}

interface OrgRequestStatusNotificationProps {
  requests: Array<{
    id: string
    status: 'pending' | 'approved' | 'rejected'
    organization: {
      name: string
      slug: string
    }
    created_at: string
  }>
  onDismiss?: () => void
}

export function OrgDetectionNotification({
  detectionResult,
  userEmail,
  onDismiss,
  onJoinSuccess
}: OrgDetectionNotificationProps) {
  const [isJoining, setIsJoining] = useState(false)
  const [joinError, setJoinError] = useState<string | null>(null)

  const handleJoinOrganization = async () => {
    if (!detectionResult.organization) return
    
    setIsJoining(true)
    setJoinError(null)

    try {
      // Implementation would call join organization service
      // For now, just simulate success
      setTimeout(() => {
        setIsJoining(false)
        onJoinSuccess?.()
        onDismiss?.()
      }, 1000)
    } catch (error) {
      setIsJoining(false)
      setJoinError(error instanceof Error ? error.message : 'Failed to join organization')
    }
  }

  const handleDismiss = () => {
    onDismiss?.()
  }

  if (!detectionResult.found || !detectionResult.organization) {
    return null
  }

  const organization = detectionResult.organization

  return (
    <Alert className="border-blue-200 bg-blue-50">
      <Building2 className="h-4 w-4 text-blue-600" />
      <div className="flex-1">
        <AlertDescription>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="font-medium text-blue-900 mb-2">
                Organization Detected
              </div>
              <div className="text-sm text-blue-800 mb-3">
                We found that your email domain <span className="font-mono text-blue-900">{userEmail.split('@')[1]}</span> is associated with <span className="font-semibold">{organization.name}</span>.
              </div>
              
              <div className="flex items-center gap-2 mb-3">
                <Badge variant="outline" className="border-blue-300 text-blue-700">
                  <Building2 className="w-3 h-3 mr-1" />
                  {organization.name}
                </Badge>
                <Badge variant="outline" className="border-blue-300 text-blue-700">
                  <Users className="w-3 h-3 mr-1" />
                  {organization.slug}
                </Badge>
              </div>

              {joinError && (
                <Alert className="border-red-200 bg-red-50 mb-3">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    {joinError}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex items-center gap-2">
                <Button
                  onClick={handleJoinOrganization}
                  disabled={isJoining}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isJoining ? (
                    <>
                      <Clock className="w-3 h-3 mr-1 animate-spin" />
                      Joining...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Join Organization
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleDismiss}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  Dismiss
                </Button>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="ml-2 h-6 w-6 p-0 text-blue-600 hover:bg-blue-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </AlertDescription>
      </div>
    </Alert>
  )
}

export function MultipleOrgsNotification({
  organizations,
  userEmail,
  onDismiss,
  onJoinSuccess
}: MultipleOrgsNotificationProps) {
  const [isJoining, setIsJoining] = useState<string | null>(null)
  const [joinError, setJoinError] = useState<string | null>(null)

  const handleJoinOrganization = async (orgId: string) => {
    setIsJoining(orgId)
    setJoinError(null)

    try {
      // Implementation would call join organization service
      setTimeout(() => {
        setIsJoining(null)
        onJoinSuccess?.()
        onDismiss?.()
      }, 1000)
    } catch (error) {
      setIsJoining(null)
      setJoinError(error instanceof Error ? error.message : 'Failed to join organization')
    }
  }

  return (
    <Alert className="border-blue-200 bg-blue-50">
      <Building2 className="h-4 w-4 text-blue-600" />
      <div className="flex-1">
        <AlertDescription>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="font-medium text-blue-900 mb-2">
                Multiple Organizations Found
              </div>
              <div className="text-sm text-blue-800 mb-3">
                Your email domain <span className="font-mono text-blue-900">{userEmail.split('@')[1]}</span> is associated with multiple organizations.
              </div>
              
              {joinError && (
                <Alert className="border-red-200 bg-red-50 mb-3">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    {joinError}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                {organizations.map((org) => (
                  <div key={org.id} className="flex items-center justify-between p-2 border border-blue-200 rounded">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="border-blue-300 text-blue-700">
                        <Building2 className="w-3 h-3 mr-1" />
                        {org.name}
                      </Badge>
                    </div>
                    <Button
                      onClick={() => handleJoinOrganization(org.id)}
                      disabled={isJoining === org.id}
                      size="sm"
                      variant="outline"
                      className="border-blue-300 text-blue-700 hover:bg-blue-100"
                    >
                      {isJoining === org.id ? (
                        <>
                          <Clock className="w-3 h-3 mr-1 animate-spin" />
                          Joining...
                        </>
                      ) : (
                        'Join'
                      )}
                    </Button>
                  </div>
                ))}
              </div>

              <div className="mt-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={onDismiss}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  Dismiss
                </Button>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="ml-2 h-6 w-6 p-0 text-blue-600 hover:bg-blue-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </AlertDescription>
      </div>
    </Alert>
  )
}

export function OrgRequestStatusNotification({
  requests,
  onDismiss
}: OrgRequestStatusNotificationProps) {
  if (requests.length === 0) return null

  return (
    <Alert className="border-blue-200 bg-blue-50">
      <Clock className="h-4 w-4 text-blue-600" />
      <div className="flex-1">
        <AlertDescription>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="font-medium text-blue-900 mb-2">
                Organization Requests Status
              </div>
              
              <div className="space-y-2">
                {requests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-2 border border-blue-200 rounded">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-blue-900">
                        {request.organization.name}
                      </span>
                      <Badge 
                        variant={
                          request.status === 'approved' ? 'default' :
                          request.status === 'rejected' ? 'destructive' : 'secondary'
                        }
                      >
                        {request.status === 'approved' && <CheckCircle className="w-3 h-3 mr-1" />}
                        {request.status === 'rejected' && <XCircle className="w-3 h-3 mr-1" />}
                        {request.status === 'pending' && <Clock className="w-3 h-3 mr-1" />}
                        {request.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={onDismiss}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  Dismiss
                </Button>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="ml-2 h-6 w-6 p-0 text-blue-600 hover:bg-blue-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </AlertDescription>
      </div>
    </Alert>
  )
}
