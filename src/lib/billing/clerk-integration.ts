/**
 * Clerk Billing Integration
 * 
 * Centralizes Clerk Billing configuration and provides utilities for
 * integrating Clerk's subscription system with TalentHUB's tier system.
 * 
 * Features:
 * - Plan mapping between TalentHUB and Clerk
 * - Feature slug definitions for access control
 * - Subscription status utilities
 * - Billing portal integration
 * - Usage tracking constants
 */

import { auth } from '@clerk/nextjs/server'
import type { AccountTier } from '@/lib/services/tier-gating'

// Clerk Billing Plan Slugs (configured in Clerk Dashboard)
export const CLERK_PLAN_SLUGS = {
  INDIVIDUAL_PRO: 'individual_pro',
  TEAM_PLAN: 'team_plan', 
  ENTERPRISE_PLAN: 'enterprise_plan'
} as const

// Feature Slugs for Clerk Billing (configured in Clerk Dashboard)
export const CLERK_FEATURE_SLUGS = {
  // Individual Pro Features
  UNLIMITED_RECORDS: 'unlimited_records',
  API_ACCESS: 'api_access', 
  ADVANCED_SEARCH: 'advanced_search',
  ANALYTICS_DASHBOARD: 'analytics_dashboard',
  EXPORT_DATA: 'export_data',
  
  // Organization Features
  ORGANIZATION_ACCESS: 'organization_access',
  TEAM_COLLABORATION: 'team_collaboration',
  MEMBER_MANAGEMENT: 'member_management',
  DOMAIN_MANAGEMENT: 'domain_management',
  
  // Enterprise Features
  ENTERPRISE_ADMIN: 'enterprise_admin',
  UNLIMITED_ORGS: 'unlimited_orgs',
  CUSTOM_INTEGRATIONS: 'custom_integrations',
  PRIORITY_SUPPORT: 'priority_support',
  
  // Usage Tracking Features
  RECORD_CREATION: 'record_creation',
  API_CALLS: 'api_calls',
  STORAGE_USAGE: 'storage_usage'
} as const

// Plan Configuration
export interface PlanConfig {
  clerkSlug: string
  talentHubTier: AccountTier
  displayName: string
  description: string
  monthlyPrice: number
  annualPrice: number
  features: string[]
  limits: {
    records?: number
    apiCalls?: number
    storage?: number // in GB
    organizationMembers?: number
  }
}

export const SUBSCRIPTION_PLANS: Record<string, PlanConfig> = {
  [CLERK_PLAN_SLUGS.INDIVIDUAL_PRO]: {
    clerkSlug: CLERK_PLAN_SLUGS.INDIVIDUAL_PRO,
    talentHubTier: 'pro',
    displayName: 'Individual Pro',
    description: 'Advanced features for individual recruiters',
    monthlyPrice: 9,
    annualPrice: 7,
    features: [
      CLERK_FEATURE_SLUGS.UNLIMITED_RECORDS,
      CLERK_FEATURE_SLUGS.API_ACCESS,
      CLERK_FEATURE_SLUGS.ADVANCED_SEARCH,
      CLERK_FEATURE_SLUGS.ANALYTICS_DASHBOARD,
      CLERK_FEATURE_SLUGS.EXPORT_DATA
    ],
    limits: {
      records: -1, // unlimited
      apiCalls: 10000, // per month
      storage: 10 // GB
    }
  },
  
  [CLERK_PLAN_SLUGS.TEAM_PLAN]: {
    clerkSlug: CLERK_PLAN_SLUGS.TEAM_PLAN,
    talentHubTier: 'team',
    displayName: 'Team Plan',
    description: 'Collaboration features for small teams',
    monthlyPrice: 19,
    annualPrice: 15,
    features: [
      CLERK_FEATURE_SLUGS.UNLIMITED_RECORDS,
      CLERK_FEATURE_SLUGS.API_ACCESS,
      CLERK_FEATURE_SLUGS.ADVANCED_SEARCH,
      CLERK_FEATURE_SLUGS.ANALYTICS_DASHBOARD,
      CLERK_FEATURE_SLUGS.ORGANIZATION_ACCESS,
      CLERK_FEATURE_SLUGS.TEAM_COLLABORATION,
      CLERK_FEATURE_SLUGS.MEMBER_MANAGEMENT,
      CLERK_FEATURE_SLUGS.DOMAIN_MANAGEMENT
    ],
    limits: {
      records: -1,
      apiCalls: 50000,
      storage: 50,
      organizationMembers: 10
    }
  },
  
  [CLERK_PLAN_SLUGS.ENTERPRISE_PLAN]: {
    clerkSlug: CLERK_PLAN_SLUGS.ENTERPRISE_PLAN,
    talentHubTier: 'enterprise',
    displayName: 'Enterprise Plan',
    description: 'Full-featured plan for large organizations',
    monthlyPrice: 49,
    annualPrice: 39,
    features: [
      CLERK_FEATURE_SLUGS.UNLIMITED_RECORDS,
      CLERK_FEATURE_SLUGS.API_ACCESS,
      CLERK_FEATURE_SLUGS.ADVANCED_SEARCH,
      CLERK_FEATURE_SLUGS.ANALYTICS_DASHBOARD,
      CLERK_FEATURE_SLUGS.ORGANIZATION_ACCESS,
      CLERK_FEATURE_SLUGS.TEAM_COLLABORATION,
      CLERK_FEATURE_SLUGS.MEMBER_MANAGEMENT,
      CLERK_FEATURE_SLUGS.DOMAIN_MANAGEMENT,
      CLERK_FEATURE_SLUGS.ENTERPRISE_ADMIN,
      CLERK_FEATURE_SLUGS.UNLIMITED_ORGS,
      CLERK_FEATURE_SLUGS.CUSTOM_INTEGRATIONS,
      CLERK_FEATURE_SLUGS.PRIORITY_SUPPORT
    ],
    limits: {
      records: -1,
      apiCalls: -1, // unlimited
      storage: -1, // unlimited
      organizationMembers: -1 // unlimited
    }
  }
}

// Subscription Status Types
export interface SubscriptionStatus {
  isActive: boolean
  planSlug?: string
  talentHubTier: AccountTier
  features: string[]
  limits: PlanConfig['limits']
  trialEndsAt?: Date
  nextBillingDate?: Date
  cancelAtPeriodEnd?: boolean
}

/**
 * Maps Clerk plan slug to TalentHUB AccountTier
 */
export function mapClerkPlanToTier(clerkPlanSlug: string | null): AccountTier {
  if (!clerkPlanSlug) return 'free'
  
  const plan = Object.values(SUBSCRIPTION_PLANS).find(
    p => p.clerkSlug === clerkPlanSlug
  )
  
  return plan?.talentHubTier || 'free'
}

/**
 * Maps TalentHUB AccountTier to Clerk plan slug
 */
export function mapTierToClerkPlan(tier: AccountTier): string | null {
  if (tier === 'free') return null
  
  const plan = Object.values(SUBSCRIPTION_PLANS).find(
    p => p.talentHubTier === tier
  )
  
  return plan?.clerkSlug || null
}

/**
 * Gets plan configuration by Clerk slug
 */
export function getPlanConfig(clerkSlug: string): PlanConfig | null {
  return SUBSCRIPTION_PLANS[clerkSlug] || null
}

/**
 * Gets plan configuration by TalentHUB tier
 */
export function getPlanConfigByTier(tier: AccountTier): PlanConfig | null {
  const clerkSlug = mapTierToClerkPlan(tier)
  return clerkSlug ? SUBSCRIPTION_PLANS[clerkSlug] : null
}

/**
 * Checks if user has access to a specific feature via Clerk Billing
 */
export async function hasFeatureAccess(featureSlug: string): Promise<boolean> {
  try {
    const { has } = await auth()
    return has({ feature: featureSlug })
  } catch (error) {
    console.error('Error checking feature access:', error)
    return false
  }
}

/**
 * Checks if user has a specific plan via Clerk Billing
 */
export async function hasPlanAccess(planSlug: string): Promise<boolean> {
  try {
    const { has } = await auth()
    return has({ plan: planSlug })
  } catch (error) {
    console.error('Error checking plan access:', error)
    return false
  }
}

/**
 * Gets current subscription status from Clerk
 */
export async function getCurrentSubscriptionStatus(): Promise<SubscriptionStatus> {
  try {
    const { has, userId } = await auth()
    
    if (!userId) {
      return {
        isActive: false,
        talentHubTier: 'free',
        features: [],
        limits: { records: 50, apiCalls: 100, storage: 1 }
      }
    }
    
    // Check which plan user has
    let activePlanSlug: string | null = null
    let talentHubTier: AccountTier = 'free'
    
    for (const planSlug of Object.values(CLERK_PLAN_SLUGS)) {
      if (has({ plan: planSlug })) {
        activePlanSlug = planSlug
        talentHubTier = mapClerkPlanToTier(planSlug)
        break
      }
    }
    
    // Get user's features
    const userFeatures: string[] = []
    for (const featureSlug of Object.values(CLERK_FEATURE_SLUGS)) {
      if (has({ feature: featureSlug })) {
        userFeatures.push(featureSlug)
      }
    }
    
    const planConfig = activePlanSlug ? getPlanConfig(activePlanSlug) : null
    
    return {
      isActive: !!activePlanSlug,
      planSlug: activePlanSlug || undefined,
      talentHubTier,
      features: userFeatures,
      limits: planConfig?.limits || { records: 50, apiCalls: 100, storage: 1 }
    }
    
  } catch (error) {
    console.error('Error getting subscription status:', error)
    return {
      isActive: false,
      talentHubTier: 'free',
      features: [],
      limits: { records: 50, apiCalls: 100, storage: 1 }
    }
  }
}

/**
 * Feature usage limits for different tiers
 */
export const USAGE_LIMITS = {
  free: {
    records: 50,
    apiCalls: 100,
    storage: 1, // GB
    searches: 20,
    exports: 2
  },
  pro: {
    records: -1, // unlimited
    apiCalls: 10000,
    storage: 10,
    searches: -1,
    exports: -1
  },
  team: {
    records: -1,
    apiCalls: 50000,
    storage: 50,
    searches: -1,
    exports: -1,
    organizationMembers: 10
  },
  enterprise: {
    records: -1,
    apiCalls: -1,
    storage: -1,
    searches: -1,
    exports: -1,
    organizationMembers: -1
  }
} as const

/**
 * Checks if usage is within tier limits
 */
export function isWithinUsageLimit(
  tier: AccountTier, 
  usageType: keyof typeof USAGE_LIMITS.free,
  currentUsage: number
): boolean {
  const limits = USAGE_LIMITS[tier]
  const limit = limits[usageType as keyof typeof limits] as number
  
  // -1 means unlimited
  if (limit === -1) return true
  
  return currentUsage < limit
}

/**
 * Gets remaining usage for a tier
 */
export function getRemainingUsage(
  tier: AccountTier,
  usageType: keyof typeof USAGE_LIMITS.free,
  currentUsage: number
): number | null {
  const limits = USAGE_LIMITS[tier]
  const limit = limits[usageType as keyof typeof limits] as number
  
  // -1 means unlimited
  if (limit === -1) return null
  
  return Math.max(0, limit - currentUsage)
}
