import { createClient as createSupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Standard Supabase client (for public operations)
export const supabase = createSupabaseClient(supabaseUrl, supabaseAnonKey);

// Default createClient function for services
export function createClient() {
  return createSupabaseClient(supabaseUrl, supabaseAnonKey);
}

// Admin client with service role (for server-side operations only)
export const supabaseAdmin = typeof window === 'undefined' 
  ? createSupabaseClient(
      supabaseUrl,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
  : null; // Don't create admin client on the frontend

// Helper function to create Clerk-authenticated Supabase client
export function createClerkSupabaseClient(getToken: () => Promise<string | null>) {
  return createSupabaseClient(supabaseUrl, supabaseAnonKey, {
    global: {
      fetch: async (url, options = {}) => {
        const clerkToken = await getToken();
        
        // Insert the Clerk Supabase token into the headers
        const headers = new Headers(options?.headers);
        if (clerkToken) {
          headers.set('Authorization', `Bearer ${clerkToken}`);
        }

        return fetch(url, {
          ...options,
          headers,
        });
      },
    },
  });
}
