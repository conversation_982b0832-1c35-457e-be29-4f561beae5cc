// Enhanced User Profile Types for TalentHUB

export type AccountTier = 'free' | 'pro' | 'team' | 'enterprise';
export type ProcessType = 'recruitment' | 'bench_sales';
export type Theme = 'light' | 'dark' | 'system';

export interface EnhancedUserProfile {
  // Existing fields from user_preferences
  id: string;
  user_id: string;
  org_id?: string;
  is_org_member: boolean;
  first_name: string;
  last_name: string;
  username: string;
  display_name?: string;
  timezone: string;
  theme: Theme;
  process_context: ProcessType;
  account_tier: AccountTier;
  subscription_status: string;
  features_enabled: string[];
  
  // New enhanced profile fields
  profile_picture_url?: string;
  bio?: string;
  job_title?: string;
  phone?: string;
  linkedin_url?: string;
  website_url?: string;
  location?: string;
  notification_preferences: NotificationPreferences;
  privacy_settings: PrivacySettings;
  last_active: string;
  profile_completeness: number;
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface NotificationPreferences {
  email_notifications: boolean;
  browser_notifications: boolean;
  workflow_updates: boolean;
  organization_updates: boolean;
  security_alerts: boolean;
  
  // Recruitment-specific notifications
  job_updates?: boolean;
  candidate_updates?: boolean;
  client_messages?: boolean;
  
  // Bench sales-specific notifications
  resource_matches?: boolean;
  vendor_updates?: boolean;
  project_assignments?: boolean;
}

export interface PrivacySettings {
  profile_visibility: 'private' | 'organization' | 'public';
  show_email: boolean;
  show_phone: boolean;
  show_location: boolean;
  show_linkedin: boolean;
  show_website: boolean;
  
  // Advanced privacy settings
  allow_search_indexing: boolean;
  show_activity_status: boolean;
  allow_direct_messages: boolean;
}

export interface UserActivity {
  id: string;
  user_id: string;
  org_id?: string;
  activity_type: string;
  activity_data: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface UserDashboardStats {
  profile_completeness: number;
  last_login: string;
  total_activities: number;
  organization_role?: string;
  subscription_status: string;
  features_used: string[];
  
  // Process-specific stats
  recruitment_stats?: {
    jobs_posted: number;
    candidates_managed: number;
    placements_made: number;
  };
  
  bench_sales_stats?: {
    resources_managed: number;
    client_matches: number;
    projects_assigned: number;
  };
}

// Webhook event data types
export interface ClerkUserData {
  id: string;
  first_name?: string;
  last_name?: string;
  email_addresses: Array<{
    id: string;
    email_address: string;
  }>;
  primary_email_address_id?: string;
  username?: string;
  profile_image_url?: string;
  created_at: number;
  updated_at: number;
}

export interface ClerkOrganizationMembershipData {
  id: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  public_user_data: {
    user_id: string;
    first_name?: string;
    last_name?: string;
  };
  role: string;
  created_at: number;
  updated_at: number;
}

export interface ClerkSessionData {
  id: string;
  user_id: string;
  status: string;
  last_active_at: number;
  expire_at: number;
}

// Form data types for profile management
export interface ProfileUpdateData {
  first_name?: string;
  last_name?: string;
  display_name?: string;
  bio?: string;
  job_title?: string;
  phone?: string;
  linkedin_url?: string;
  website_url?: string;
  location?: string;
  profile_picture_url?: string;
}

export interface PreferencesUpdateData {
  timezone?: string;
  theme?: Theme;
  notification_preferences?: Partial<NotificationPreferences>;
  privacy_settings?: Partial<PrivacySettings>;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    has_more: boolean;
  };
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Default values for preferences and settings
export const DEFAULT_NOTIFICATION_PREFERENCES: NotificationPreferences = {
  email_notifications: true,
  browser_notifications: true,
  workflow_updates: true,
  organization_updates: true,
  security_alerts: true,
};

export const DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
  profile_visibility: 'organization',
  show_email: false,
  show_phone: false,
  show_location: true,
  show_linkedin: false,
  show_website: false,
  allow_search_indexing: true,
  show_activity_status: true,
  allow_direct_messages: true,
};

export const DEFAULT_FEATURES_ENABLED: string[] = ['basic'];

// Supabase database record type
export interface UserPreferencesRecord {
  id: string;
  user_id: string;
  org_id?: string;
  is_org_member: boolean;
  first_name: string;
  last_name: string;
  username: string;
  display_name?: string;
  timezone: string;
  theme: Theme;
  process_context: ProcessType;
  account_tier: AccountTier;
  subscription_status: string;
  features_enabled: string[];
  profile_picture_url?: string;
  bio?: string;
  job_title?: string;
  phone?: string;
  linkedin_url?: string;
  website_url?: string;
  location?: string;
  notification_preferences: NotificationPreferences;
  privacy_settings: PrivacySettings;
  last_active: string;
  profile_completeness: number;
  created_at: string;
  updated_at: string;
}

// Update interface for enhanced profile
export interface EnhancedUserProfileUpdate {
  firstName?: string;
  lastName?: string;
  username?: string;
  displayName?: string;
  timezone?: string;
  theme?: Theme;
  processContext?: ProcessType;
  profilePictureUrl?: string;
  bio?: string;
  jobTitle?: string;
  phone?: string;
  linkedinUrl?: string;
  websiteUrl?: string;
  location?: string;
  notificationPreferences?: Partial<NotificationPreferences>;
  privacySettings?: Partial<PrivacySettings>;
}