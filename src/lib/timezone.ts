/**
 * Core timezone utilities using native JavaScript
 * Provides timezone conversion, validation, and formatting functionality
 */

// Common business timezones for recruitment industry
export const COMMON_TIMEZONES = [
  'UTC',
  'America/New_York',
  'America/Chicago', 
  'America/Denver',
  'America/Los_Angeles',
  'America/Toronto',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Europe/Amsterdam',
  'Europe/Madrid',
  'Asia/Tokyo',
  'Asia/Seoul',
  'Asia/Shanghai',
  'Asia/Singapore',
  'Asia/Kolkata',
  'Australia/Sydney',
  'Australia/Melbourne',
  'Pacific/Auckland'
] as const

// Structured timezone data for the timezone selector
export const TIMEZONES_BY_REGION = [
  {
    region: 'UTC',
    timezones: [
      {
        value: 'UTC',
        label: 'UTC (Coordinated Universal Time)',
        city: 'UTC',
        offset: 'UTC+0'
      }
    ]
  },
  {
    region: 'North America',
    timezones: [
      {
        value: 'America/New_York',
        label: 'Eastern Time',
        city: 'New York',
        offset: 'UTC-5'
      },
      {
        value: 'America/Chicago',
        label: 'Central Time',
        city: 'Chicago',
        offset: 'UTC-6'
      },
      {
        value: 'America/Denver',
        label: 'Mountain Time',
        city: 'Denver',
        offset: 'UTC-7'
      },
      {
        value: 'America/Los_Angeles',
        label: 'Pacific Time',
        city: 'Los Angeles',
        offset: 'UTC-8'
      },
      {
        value: 'America/Toronto',
        label: 'Eastern Time',
        city: 'Toronto',
        offset: 'UTC-5'
      },
      {
        value: 'America/Vancouver',
        label: 'Pacific Time',
        city: 'Vancouver',
        offset: 'UTC-8'
      }
    ]
  },
  {
    region: 'Europe',
    timezones: [
      {
        value: 'Europe/London',
        label: 'Greenwich Mean Time',
        city: 'London',
        offset: 'UTC+0'
      },
      {
        value: 'Europe/Paris',
        label: 'Central European Time',
        city: 'Paris',
        offset: 'UTC+1'
      },
      {
        value: 'Europe/Berlin',
        label: 'Central European Time',
        city: 'Berlin',
        offset: 'UTC+1'
      },
      {
        value: 'Europe/Amsterdam',
        label: 'Central European Time',
        city: 'Amsterdam',
        offset: 'UTC+1'
      },
      {
        value: 'Europe/Madrid',
        label: 'Central European Time',
        city: 'Madrid',
        offset: 'UTC+1'
      },
      {
        value: 'Europe/Rome',
        label: 'Central European Time',
        city: 'Rome',
        offset: 'UTC+1'
      }
    ]
  },
  {
    region: 'Asia',
    timezones: [
      {
        value: 'Asia/Tokyo',
        label: 'Japan Standard Time',
        city: 'Tokyo',
        offset: 'UTC+9'
      },
      {
        value: 'Asia/Seoul',
        label: 'Korea Standard Time',
        city: 'Seoul',
        offset: 'UTC+9'
      },
      {
        value: 'Asia/Shanghai',
        label: 'China Standard Time',
        city: 'Shanghai',
        offset: 'UTC+8'
      },
      {
        value: 'Asia/Singapore',
        label: 'Singapore Standard Time',
        city: 'Singapore',
        offset: 'UTC+8'
      },
      {
        value: 'Asia/Kolkata',
        label: 'India Standard Time',
        city: 'Mumbai',
        offset: 'UTC+5:30'
      },
      {
        value: 'Asia/Dubai',
        label: 'Gulf Standard Time',
        city: 'Dubai',
        offset: 'UTC+4'
      }
    ]
  },
  {
    region: 'Australia & Pacific',
    timezones: [
      {
        value: 'Australia/Sydney',
        label: 'Australian Eastern Time',
        city: 'Sydney',
        offset: 'UTC+11'
      },
      {
        value: 'Australia/Melbourne',
        label: 'Australian Eastern Time',
        city: 'Melbourne',
        offset: 'UTC+11'
      },
      {
        value: 'Pacific/Auckland',
        label: 'New Zealand Standard Time',
        city: 'Auckland',
        offset: 'UTC+13'
      }
    ]
  }
] as const

// Timezone configuration interface
export interface TimezoneConfig {
  userTimezone?: string | null
  organizationTimezone: string
  fallbackTimezone: string
}

/**
 * Validates if a timezone string is valid
 */
export function isValidTimezone(timezone: string): boolean {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone })
    return true
  } catch {
    return false
  }
}

/**
 * Gets the effective timezone based on configuration priority:
 * 1. User timezone (if set and allowed)
 * 2. Organization timezone
 * 3. Fallback timezone
 */
export function getEffectiveTimezone(config: TimezoneConfig): string {
  // Priority 1: User timezone (if set and valid)
  if (config.userTimezone && isValidTimezone(config.userTimezone)) {
    return config.userTimezone
  }
  
  // Priority 2: Organization timezone (if valid)
  if (config.organizationTimezone && isValidTimezone(config.organizationTimezone)) {
    return config.organizationTimezone
  }
  
  // Priority 3: Fallback timezone
  return config.fallbackTimezone || 'UTC'
}

/**
 * Formats a date in a specific timezone
 */
export function formatInTimezone(
  date: Date | string,
  timezone: string,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: timezone,
    ...options
  }
  
  try {
    return new Intl.DateTimeFormat('en-US', defaultOptions).format(dateObj)
  } catch {
    // Fallback to UTC if timezone is invalid
    return new Intl.DateTimeFormat('en-US', {
      ...defaultOptions,
      timeZone: 'UTC'
    }).format(dateObj)
  }
}

/**
 * Gets timezone abbreviation (e.g., "EST", "PST")
 */
export function getTimezoneAbbreviation(timezone: string, date: Date = new Date()): string {
  try {
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short'
    })
    
    const parts = formatter.formatToParts(date)
    const abbreviation = parts.find(part => part.type === 'timeZoneName')?.value
    return abbreviation || timezone
  } catch {
    return timezone
  }
}

/**
 * Gets timezone offset in minutes
 */
export function getTimezoneOffset(timezone: string, date: Date = new Date()): number {
  try {
    // Create date in timezone
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }))
    // Create date in UTC
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }))
    
    return (utcDate.getTime() - tzDate.getTime()) / (1000 * 60)
  } catch {
    return 0
  }
}

/**
 * Converts a date from one timezone to another
 */
export function convertBetweenTimezones(
  date: Date | string,
  fromTimezone: string,
  toTimezone: string
): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  try {
    // Get the date in the source timezone
    const sourceTime = new Date(dateObj.toLocaleString('en-US', { timeZone: fromTimezone }))
    // Get the date in the target timezone
    const targetTime = new Date(dateObj.toLocaleString('en-US', { timeZone: toTimezone }))
    
    // Calculate the difference
    const diff = sourceTime.getTime() - targetTime.getTime()
    
    // Return adjusted date
    return new Date(dateObj.getTime() + diff)
  } catch {
    return dateObj
  }
}

/**
 * Gets a user-friendly display name for a timezone
 */
export function getTimezoneDisplayName(timezone: string): string {
  try {
    const cityName = timezone.split('/')[1]?.replace(/_/g, ' ') || timezone
    const abbreviation = getTimezoneAbbreviation(timezone)
    
    return `${cityName} (${abbreviation})`
  } catch {
    return timezone
  }
}

/**
 * Groups timezones by region
 */
export function groupTimezonesByRegion(): Record<string, string[]> {
  const groups: Record<string, string[]> = {}
  
  COMMON_TIMEZONES.forEach(tz => {
    const [region] = tz.split('/')
    if (!groups[region]) {
      groups[region] = []
    }
    groups[region].push(tz)
  })
  
  return groups
}

/**
 * Checks if it's business hours in a timezone
 */
export function isBusinessHours(
  timezone: string,
  date: Date = new Date(),
  startHour: number = 9,
  endHour: number = 17
): boolean {
  try {
    const timeString = date.toLocaleString('en-US', { 
      timeZone: timezone,
      hour12: false,
      hour: '2-digit'
    })
    
    const hour = parseInt(timeString.split(',')[1]?.trim().split(':')[0] || '0')
    return hour >= startHour && hour < endHour
  } catch {
    return false
  }
}

/**
 * Gets relative time string (e.g., "2 hours ago")
 */
export function getRelativeTime(date: Date | string, timezone?: string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  
  const diffMs = now.getTime() - dateObj.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffMinutes < 1) return 'just now'
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`
  
  // For older dates, show formatted date
  return timezone ? formatInTimezone(dateObj, timezone) : dateObj.toLocaleDateString()
}

/**
 * Formats business schedule across timezones
 */
export function formatBusinessSchedule(
  timezone: string,
  startHour: number = 9,
  endHour: number = 17
): string {
  const now = new Date()
  
  // Create start time
  const start = new Date(now)
  start.setHours(startHour, 0, 0, 0)
  
  // Create end time
  const end = new Date(now)
  end.setHours(endHour, 0, 0, 0)
  
  const startFormatted = formatInTimezone(start, timezone, {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
  
  const endFormatted = formatInTimezone(end, timezone, {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
  
  const abbreviation = getTimezoneAbbreviation(timezone)
  
  return `${startFormatted} - ${endFormatted} ${abbreviation}`
}
