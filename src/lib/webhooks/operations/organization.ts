/**
 * TalentHUB Webhook Operations - Organization Management
 * All organization-related webhook operations (create, update, delete, membership)
 */

import { ClerkOrganizationData, WebhookProcessingResult } from '../types';
import { BaseOperation, OperationContext, ActivityTracker } from './base';

// ===== ORGANIZATION CREATION OPERATION =====

export class CreateOrganizationOperation extends BaseOperation {
  private orgData: ClerkOrganizationData;

  constructor(context: OperationContext, orgData: ClerkOrganizationData) {
    super(context, 'create', 'organization', orgData.id);
    this.orgData = orgData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { data, error } = await this.safeDbOperation(
      () => this.createOrganizationRecord(),
      'createOrganizationRecord'
    );

    if (error) {
      return this.createErrorResult(error, { orgId: this.orgData.id });
    }

    // Track organization creation
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.orgData.created_by || 'system',
      'organization_created',
      {
        organization_id: this.orgData.id,
        organization_name: this.orgData.name,
        organization_slug: this.orgData.slug,
        clerk_created_at: this.orgData.created_at
      },
      this.context.request
    );

    return this.createSuccessResult({
      orgId: this.orgData.id,
      name: this.orgData.name,
      slug: this.orgData.slug,
      createdBy: this.orgData.created_by
    });
  }

  private async createOrganizationRecord() {
    const { data, error } = await this.context.supabase
      .from('organizations')
      .upsert({
        clerk_org_id: this.orgData.id,
        name: this.orgData.name,
        slug: this.orgData.slug,
        type: 'hybrid', // Default type for new orgs
        settings: {
          created_by: this.orgData.created_by,
          public_metadata: this.orgData.public_metadata || {},
          private_metadata: this.orgData.private_metadata || {}
        },
        is_active: true,
        created_at: new Date(this.orgData.created_at).toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'clerk_org_id'
      });

    if (error) {
      throw new Error(`Failed to create organization: ${error.message}`);
    }

    return data;
  }
}

// ===== ORGANIZATION UPDATE OPERATION =====

export class UpdateOrganizationOperation extends BaseOperation {
  private orgData: ClerkOrganizationData;

  constructor(context: OperationContext, orgData: ClerkOrganizationData) {
    super(context, 'update', 'organization', orgData.id);
    this.orgData = orgData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { error } = await this.safeDbOperation(
      () => this.updateOrganizationRecord(),
      'updateOrganizationRecord'
    );

    if (error) {
      return this.createErrorResult(error, { orgId: this.orgData.id });
    }

    // Track organization update
    await ActivityTracker.trackActivity(
      this.context.supabase,
      'system',
      'organization_updated',
      {
        organization_id: this.orgData.id,
        organization_name: this.orgData.name,
        clerk_updated_at: this.orgData.updated_at
      },
      this.context.request
    );

    return this.createSuccessResult({
      orgId: this.orgData.id,
      name: this.orgData.name,
      updated: ['name', 'slug', 'settings']
    });
  }

  private async updateOrganizationRecord() {
    const { error } = await this.context.supabase
      .from('organizations')
      .update({
        name: this.orgData.name,
        slug: this.orgData.slug,
        settings: {
          public_metadata: this.orgData.public_metadata || {},
          private_metadata: this.orgData.private_metadata || {}
        },
        updated_at: new Date().toISOString()
      })
      .eq('clerk_org_id', this.orgData.id);

    if (error) {
      throw new Error(`Failed to update organization: ${error.message}`);
    }
  }
}

// ===== ORGANIZATION DELETION OPERATION =====

export class DeleteOrganizationOperation extends BaseOperation {
  private orgData: ClerkOrganizationData;

  constructor(context: OperationContext, orgData: ClerkOrganizationData) {
    super(context, 'delete', 'organization', orgData.id);
    this.orgData = orgData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { error } = await this.safeDbOperation(
      () => this.softDeleteOrganization(),
      'softDeleteOrganization'
    );

    if (error) {
      return this.createErrorResult(error, { orgId: this.orgData.id });
    }

    // Track organization deletion
    await ActivityTracker.trackActivity(
      this.context.supabase,
      'system',
      'organization_deleted',
      {
        organization_id: this.orgData.id,
        deleted_at: this.orgData.deleted_at
      },
      this.context.request
    );

    return this.createSuccessResult({
      orgId: this.orgData.id,
      action: 'soft_delete',
      deletedAt: this.orgData.deleted_at
    });
  }

  private async softDeleteOrganization() {
    if (!this.orgData.deleted_at) {
      throw new Error('Organization deletion timestamp is required');
    }

    // Soft delete organization
    const { error: orgError } = await this.context.supabase
      .from('organizations')
      .update({
        is_active: false,
        deleted_at: new Date(this.orgData.deleted_at).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('clerk_org_id', this.orgData.id);

    if (orgError) {
      throw new Error(`Failed to soft delete organization: ${orgError.message}`);
    }

    // Remove user associations
    const { error: membershipError } = await this.context.supabase
      .from('user_preferences')
      .update({
        org_id: null,
        is_org_member: false,
        updated_at: new Date().toISOString()
      })
      .eq('org_id', this.orgData.id);

    if (membershipError) {
      console.warn('Failed to remove org memberships:', membershipError);
    }
  }
}

// ===== ORGANIZATION OPERATION FACTORY =====

export class OrganizationOperationFactory {
  
  static createOrganizationOperation(
    eventType: string,
    context: OperationContext,
    orgData: ClerkOrganizationData
  ): BaseOperation {
    switch (eventType) {
      case 'organization.created':
        return new CreateOrganizationOperation(context, orgData);
        
      case 'organization.updated':
        return new UpdateOrganizationOperation(context, orgData);
        
      case 'organization.deleted':
        return new DeleteOrganizationOperation(context, orgData);
        
      default:
        throw new Error(`Unsupported organization event type: ${eventType}`);
    }
  }
}
