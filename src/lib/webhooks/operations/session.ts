/**
 * TalentHUB Webhook Operations - Session & Security Management
 * All session-related webhook operations and security event tracking
 */

import { ClerkSessionData, WebhookProcessingResult } from '../types';
import { BaseOperation, OperationContext, ActivityTracker } from './base';

// ===== SESSION CREATION OPERATION =====

export class CreateSessionOperation extends BaseOperation {
  private sessionData: ClerkSessionData;

  constructor(context: OperationContext, sessionData: ClerkSessionData) {
    super(context, 'create', 'session', sessionData.id);
    this.sessionData = sessionData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { error } = await this.safeDbOperation(
      () => this.trackSessionCreation(),
      'trackSessionCreation'
    );

    if (error) {
      return this.createErrorResult(error, { 
        sessionId: this.sessionData.id, 
        userId: this.sessionData.user_id 
      });
    }

    // Track session creation activity
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.sessionData.user_id,
      'session_created',
      {
        session_id: this.sessionData.id,
        status: this.sessionData.status,
        created_at: this.sessionData.created_at,
        last_active_at: this.sessionData.last_active_at,
        expire_at: this.sessionData.expire_at,
        user_agent: this.context.request?.headers.get('user-agent'),
        ip_address: this.context.request?.ip
      },
      this.context.request
    );

    return this.createSuccessResult({
      userId: this.sessionData.user_id,
      sessionId: this.sessionData.id,
      status: this.sessionData.status
    });
  }

  private async trackSessionCreation() {
    // Update user's last active timestamp
    const { error: userError } = await this.context.supabase
      .from('user_preferences')
      .update({
        last_active: new Date(this.sessionData.last_active_at || this.sessionData.created_at).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', this.sessionData.user_id);

    if (userError) {
      console.warn('Failed to update last active timestamp:', userError);
    }

    // Track session in sessions table
    const { error: sessionError } = await this.context.supabase
      .from('user_sessions_tracking')
      .upsert({
        clerk_session_id: this.sessionData.id,
        user_id: this.sessionData.user_id,
        session_status: 'created',
        created_at_clerk: new Date(this.sessionData.created_at).toISOString(),
        last_active_at: new Date(this.sessionData.last_active_at || this.sessionData.created_at).toISOString(),
        expire_at: this.sessionData.expire_at ? new Date(this.sessionData.expire_at).toISOString() : null,
        ip_address: this.context.request?.ip || null,
        user_agent: this.context.request?.headers.get('user-agent') || null,
        session_metadata: {
          created_at: this.sessionData.created_at,
          status: this.sessionData.status
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'clerk_session_id'
      });

    if (sessionError) {
      throw new Error(`Failed to track session creation: ${sessionError.message}`);
    }
  }
}

// ===== SESSION END OPERATION =====

export class EndSessionOperation extends BaseOperation {
  private sessionData: ClerkSessionData;

  constructor(context: OperationContext, sessionData: ClerkSessionData) {
    super(context, 'end', 'session', sessionData.id);
    this.sessionData = sessionData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { error } = await this.safeDbOperation(
      () => this.trackSessionEnd(),
      'trackSessionEnd'
    );

    if (error) {
      return this.createErrorResult(error, { 
        sessionId: this.sessionData.id, 
        userId: this.sessionData.user_id 
      });
    }

    // Track session end activity
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.sessionData.user_id,
      'session_ended',
      {
        session_id: this.sessionData.id,
        status: this.sessionData.status,
        ended_at: this.sessionData.ended_at
      },
      this.context.request
    );

    return this.createSuccessResult({
      userId: this.sessionData.user_id,
      sessionId: this.sessionData.id,
      endedAt: this.sessionData.ended_at
    });
  }

  private async trackSessionEnd() {
    // Update user's last active timestamp
    const { error: userError } = await this.context.supabase
      .from('user_preferences')
      .update({
        last_active: new Date(this.sessionData.ended_at || this.sessionData.updated_at).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', this.sessionData.user_id);

    if (userError) {
      console.warn('Failed to update last active timestamp:', userError);
    }

    // Update session tracking
    const { error: sessionError } = await this.context.supabase
      .from('user_sessions_tracking')
      .update({
        session_status: 'ended',
        ended_at_clerk: this.sessionData.ended_at ? new Date(this.sessionData.ended_at).toISOString() : null,
        removal_reason: 'logout',
        updated_at: new Date().toISOString()
      })
      .eq('clerk_session_id', this.sessionData.id);

    if (sessionError) {
      throw new Error(`Failed to update session tracking: ${sessionError.message}`);
    }
  }
}

// ===== SESSION REVOCATION OPERATION (SECURITY EVENT) =====

export class RevokeSessionOperation extends BaseOperation {
  private sessionData: ClerkSessionData;

  constructor(context: OperationContext, sessionData: ClerkSessionData) {
    super(context, 'revoke', 'session', sessionData.id);
    this.sessionData = sessionData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { error } = await this.safeDbOperation(
      () => this.trackSessionRevocation(),
      'trackSessionRevocation'
    );

    if (error) {
      return this.createErrorResult(error, { 
        sessionId: this.sessionData.id, 
        userId: this.sessionData.user_id 
      });
    }

    // Track session revocation activity (SECURITY EVENT)
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.sessionData.user_id,
      'session_revoked',
      {
        session_id: this.sessionData.id,
        status: this.sessionData.status,
        revoked_at: this.sessionData.updated_at,
        security_event: true,
        removal_type: 'revoked'
      },
      this.context.request
    );

    return this.createSuccessResult({
      userId: this.sessionData.user_id,
      sessionId: this.sessionData.id,
      securityEvent: true,
      revokedAt: this.sessionData.updated_at
    });
  }

  private async trackSessionRevocation() {
    // Update session tracking with security flag
    const { error } = await this.context.supabase
      .from('user_sessions_tracking')
      .update({
        session_status: 'revoked',
        is_security_event: true,
        removal_reason: 'revoked',
        session_metadata: {
          security_event: true,
          revoked_at: this.sessionData.updated_at
        },
        updated_at: new Date().toISOString()
      })
      .eq('clerk_session_id', this.sessionData.id);

    if (error) {
      throw new Error(`Failed to track session revocation: ${error.message}`);
    }
  }
}

// ===== SESSION OPERATION FACTORY =====

export class SessionOperationFactory {
  
  static createSessionOperation(
    eventType: string,
    context: OperationContext,
    sessionData: ClerkSessionData
  ): BaseOperation {
    switch (eventType) {
      case 'session.created':
        return new CreateSessionOperation(context, sessionData);
        
      case 'session.ended':
        return new EndSessionOperation(context, sessionData);
        
      case 'session.revoked':
        return new RevokeSessionOperation(context, sessionData);
        
      default:
        throw new Error(`Unsupported session event type: ${eventType}`);
    }
  }
}
