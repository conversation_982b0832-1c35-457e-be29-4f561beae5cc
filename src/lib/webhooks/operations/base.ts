/**
 * TalentHUB Webhook Operations - Base Interface
 * Common interfaces and utilities for all webhook operations
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { NextRequest } from 'next/server';
import { WebhookProcessingResult } from '../types';

// ===== BASE OPERATION INTERFACE =====

export interface BaseWebhookOperation {
  /**
   * Execute the operation
   */
  execute(): Promise<WebhookProcessingResult>;
  
  /**
   * Get operation metadata
   */
  getMetadata(): {
    operationType: string;
    entityType: string;
    entityId: string;
  };
}

// ===== OPERATION CONTEXT =====

export interface OperationContext {
  supabase: SupabaseClient;
  request?: NextRequest;
  startTime: number;
  eventId: string;
  eventType: string;
}

// ===== BASE OPERATION CLASS =====

export abstract class BaseOperation implements BaseWebhookOperation {
  protected context: OperationContext;
  protected operationType: string;
  protected entityType: string;
  protected entityId: string;

  constructor(
    context: OperationContext,
    operationType: string,
    entityType: string,
    entityId: string
  ) {
    this.context = context;
    this.operationType = operationType;
    this.entityType = entityType;
    this.entityId = entityId;
  }

  abstract execute(): Promise<WebhookProcessingResult>;

  getMetadata() {
    return {
      operationType: this.operationType,
      entityType: this.entityType,
      entityId: this.entityId
    };
  }

  /**
   * Create standardized success result
   */
  protected createSuccessResult(details?: any): WebhookProcessingResult {
    return {
      success: true,
      duration: Date.now() - this.context.startTime,
      details: {
        operationType: this.operationType,
        entityType: this.entityType,
        entityId: this.entityId,
        ...details
      }
    };
  }

  /**
   * Create standardized error result
   */
  protected createErrorResult(error: string, details?: any): WebhookProcessingResult {
    return {
      success: false,
      duration: Date.now() - this.context.startTime,
      error: `${this.operationType} failed: ${error}`,
      details: {
        operationType: this.operationType,
        entityType: this.entityType,
        entityId: this.entityId,
        ...details
      }
    };
  }

  /**
   * Safe database operation wrapper
   */
  protected async safeDbOperation<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<{ data?: T; error?: string }> {
    try {
      const data = await operation();
      return { data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
      console.error(`Database operation failed [${operationName}]:`, error);
      return { error: errorMessage };
    }
  }
}

// ===== WEBHOOK LOGGING UTILITIES =====

export class WebhookLogger {
  
  /**
   * Log webhook event start
   */
  static async logEventStart(
    supabase: SupabaseClient,
    eventId: string,
    eventType: string,
    clerkUserId?: string,
    clerkOrgId?: string,
    eventData?: any
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('webhook_event_logs')
        .insert({
          event_id: eventId,
          event_type: eventType,
          clerk_user_id: clerkUserId,
          clerk_org_id: clerkOrgId,
          processing_status: 'processing',
          event_data: eventData,
          created_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log webhook event start:', error);
        return null;
      }

      return data.id;
    } catch (error) {
      console.error('Error logging webhook event start:', error);
      return null;
    }
  }

  /**
   * Update webhook event status
   */
  static async logEventCompletion(
    supabase: SupabaseClient,
    eventId: string,
    result: WebhookProcessingResult
  ): Promise<void> {
    try {
      const updateData: any = {
        processing_status: result.success ? 'success' : 'failed',
        processing_duration_ms: result.duration,
        processed_at: new Date().toISOString()
      };

      if (!result.success && result.error) {
        updateData.error_message = result.error;
        updateData.error_details = result.details || {};
      }

      const { error } = await supabase
        .from('webhook_event_logs')
        .update(updateData)
        .eq('event_id', eventId);

      if (error) {
        console.error('Failed to update webhook event log:', error);
      }
    } catch (error) {
      console.error('Error updating webhook event log:', error);
    }
  }
}

// ===== ACTIVITY TRACKING =====

export class ActivityTracker {
  
  /**
   * Track user activity with enhanced context
   */
  static async trackActivity(
    supabase: SupabaseClient,
    userId: string,
    activityType: string,
    activityData: any,
    request?: NextRequest
  ): Promise<void> {
    try {
      // Get organization context if user is part of one
      let orgId = null;
      if (userId !== 'system') {
        const { data: userPrefs } = await supabase
          .from('user_preferences')
          .select('org_id')
          .eq('user_id', userId)
          .single();
        
        orgId = userPrefs?.org_id;
      }

      const { error } = await supabase
        .from('user_activity_logs')
        .insert({
          user_id: userId,
          org_id: orgId,
          activity_type: activityType,
          activity_data: {
            ...activityData,
            processed_at: new Date().toISOString(),
            webhook_source: 'clerk'
          },
          ip_address: request?.ip || null,
          user_agent: request?.headers.get('user-agent') || null,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error tracking activity:', error);
      }
    } catch (error) {
      console.error('Error in activity tracking:', error);
      // Don't throw - activity tracking shouldn't break webhook processing
    }
  }
}

// ===== OPERATION FACTORY =====

export class OperationFactory {
  
  /**
   * Create operation context
   */
  static createContext(
    supabase: SupabaseClient,
    eventId: string,
    eventType: string,
    request?: NextRequest
  ): OperationContext {
    return {
      supabase,
      request,
      startTime: Date.now(),
      eventId,
      eventType
    };
  }
}
