/**
 * TalentHUB Webhook Operations - Main Export
 * Centralized export for all webhook operations
 */

// Base Operations
export * from './base';

// Domain-Specific Operations
export * from './user';
export * from './organization';
export * from './session';

// Operation Registry
import { ClerkWebhookEventType } from '../types';
import { BaseOperation, OperationContext } from './base';
import { UserOperationFactory } from './user';
import { OrganizationOperationFactory } from './organization';
import { SessionOperationFactory } from './session';

/**
 * Main Operation Factory - Creates appropriate operation based on event type
 */
export class WebhookOperationFactory {
  
  static createOperation(
    eventType: ClerkWebhookEventType,
    context: OperationContext,
    eventData: any
  ): BaseOperation {
    
    try {
      // User Events
      if (eventType.startsWith('user.')) {
        return UserOperationFactory.createUserOperation(eventType, context, eventData);
      }
      
      // Organization Events (not membership/invitation)
      if (eventType.startsWith('organization.') && 
          !eventType.includes('Membership') && 
          !eventType.includes('Invitation')) {
        return OrganizationOperationFactory.createOrganizationOperation(eventType, context, eventData);
      }
      
      // Session Events
      if (eventType.startsWith('session.')) {
        return SessionOperationFactory.createSessionOperation(eventType, context, eventData);
      }
      
      // TODO: Add other operation factories as we create them
      // - MembershipOperationFactory for organizationMembership.*
      // - InvitationOperationFactory for organizationInvitation.*
      
      throw new Error(`No operation factory found for event type: ${eventType}`);
      
    } catch (error) {
      throw new Error(`Failed to create operation for ${eventType}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get supported event types
   */
  static getSupportedEventTypes(): ClerkWebhookEventType[] {
    return [
      // User events
      'user.created',
      'user.updated', 
      'user.deleted',
      
      // Organization events
      'organization.created',
      'organization.updated',
      'organization.deleted',
      
      // Session events
      'session.created',
      'session.ended',
      'session.revoked'
      
      // TODO: Add more as we implement them
    ];
  }

  /**
   * Check if event type is supported
   */
  static isEventTypeSupported(eventType: ClerkWebhookEventType): boolean {
    return this.getSupportedEventTypes().includes(eventType);
  }
}
