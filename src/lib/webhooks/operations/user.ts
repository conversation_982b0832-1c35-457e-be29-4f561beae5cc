/**
 * TalentHUB Webhook Operations - User Management
 * All user-related webhook operations (create, update, delete)
 */

import { ClerkUserData, WebhookProcessingResult } from '../types';
import { BaseOperation, OperationContext, ActivityTracker } from './base';

// ===== USER CREATION OPERATION =====

export class CreateUserOperation extends BaseOperation {
  private userData: ClerkUserData;
  private primaryEmail?: string;

  constructor(context: OperationContext, userData: ClerkUserData, primaryEmail?: string) {
    super(context, 'create', 'user', userData.id);
    this.userData = userData;
    this.primaryEmail = primaryEmail;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { data, error } = await this.safeDbOperation(
      () => this.createUserPreferences(),
      'createUserPreferences'
    );

    if (error) {
      return this.createErrorResult(error, { 
        userData: { id: this.userData.id, email: this.primaryEmail } 
      });
    }

    // Track user creation activity
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.userData.id,
      'user_created',
      {
        email: this.primaryEmail,
        signup_method: 'clerk_auth',
        clerk_created_at: this.userData.created_at
      },
      this.context.request
    );

    return this.createSuccessResult({
      userId: this.userData.id,
      username: data?.username,
      email: this.primaryEmail
    });
  }

  private async createUserPreferences() {
    const username = `${this.userData.first_name || 'user'}_${this.userData.id.slice(-8)}`;
    const displayName = `${this.userData.first_name || ''} ${this.userData.last_name || ''}`.trim();

    let timezone = 'America/New_York';
    let orgId = null;

    if (this.userData.organization_memberships && this.userData.organization_memberships.length > 0) {
      const primaryOrgId = this.userData.organization_memberships[0].organization.id;
      const { data: org, error: orgError } = await this.context.supabase
        .from('organizations')
        .select('default_timezone')
        .eq('clerk_org_id', primaryOrgId)
        .single();

      if (orgError) {
        console.error('Error fetching organization timezone:', orgError);
      } else if (org?.default_timezone) {
        timezone = org.default_timezone;
        orgId = primaryOrgId;
      }
    }

    console.log("CreateUserOperation: Preparing profileData for upsert:", {
      user_id: this.userData.id,
      first_name: this.userData.first_name || '',
      last_name: this.userData.last_name || '',
      username: username,
      display_name: displayName,
      timezone: timezone,
      theme: 'system',
      account_tier: 'free',
      subscription_status: 'active',
      features_enabled: ['basic'],
      profile_completeness: 25,
      last_active: new Date().toISOString(),
      created_at: new Date(this.userData.created_at).toISOString(),
      updated_at: new Date().toISOString(),
      org_id: orgId,
      is_org_member: !!orgId,
    });

    const { data, error } = await this.context.supabase
      .from('user_preferences')
      .upsert({
        user_id: this.userData.id,
        first_name: this.userData.first_name || '',
        last_name: this.userData.last_name || '',
        username: username,
        display_name: displayName,
        timezone: timezone,
        theme: 'system',
        account_tier: 'free',
        subscription_status: 'active',
        features_enabled: ['basic'],
        profile_completeness: 25,
        last_active: new Date().toISOString(),
        created_at: new Date(this.userData.created_at).toISOString(),
        updated_at: new Date().toISOString(),
        org_id: orgId,
        is_org_member: !!orgId,
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error("CreateUserOperation: Supabase upsert failed:", error);
      throw new Error(`Failed to create user preferences: ${error.message}`);
    }

    console.log("CreateUserOperation: Supabase upsert successful. Data:", data);
    return { username, data };
  }
}

// ===== USER UPDATE OPERATION =====

export class UpdateUserOperation extends BaseOperation {
  private userData: ClerkUserData;

  constructor(context: OperationContext, userData: ClerkUserData) {
    super(context, 'update', 'user', userData.id);
    this.userData = userData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { data, error } = await this.safeDbOperation(
      () => this.updateUserPreferences(),
      'updateUserPreferences'
    );

    if (error) {
      return this.createErrorResult(error, { userId: this.userData.id });
    }

    // Track user update activity
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.userData.id,
      'profile_updated',
      {
        updated_fields: ['first_name', 'last_name'],
        clerk_updated_at: this.userData.updated_at
      },
      this.context.request
    );

    return this.createSuccessResult({
      userId: this.userData.id,
      updated: ['first_name', 'last_name', 'display_name']
    });
  }

  private async updateUserPreferences() {
    const displayName = `${this.userData.first_name || ''} ${this.userData.last_name || ''}`.trim();

    const { error } = await this.context.supabase
      .from('user_preferences')
      .update({
        first_name: this.userData.first_name || '',
        last_name: this.userData.last_name || '',
        display_name: displayName,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', this.userData.id);

    if (error) {
      throw new Error(`Failed to update user preferences: ${error.message}`);
    }

    return { displayName };
  }
}

// ===== USER DELETION OPERATION =====

export class DeleteUserOperation extends BaseOperation {
  private userData: ClerkUserData;

  constructor(context: OperationContext, userData: ClerkUserData) {
    super(context, 'delete', 'user', userData.id);
    this.userData = userData;
  }

  async execute(): Promise<WebhookProcessingResult> {
    const { error } = await this.safeDbOperation(
      () => this.anonymizeUserData(),
      'anonymizeUserData'
    );

    if (error) {
      return this.createErrorResult(error, { userId: this.userData.id });
    }

    // Track user deletion activity (for compliance)
    await ActivityTracker.trackActivity(
      this.context.supabase,
      this.userData.id,
      'user_deleted',
      {
        deleted_at: this.userData.deleted_at,
        compliance_action: 'soft_delete_and_anonymize'
      },
      this.context.request
    );

    return this.createSuccessResult({
      userId: this.userData.id,
      action: 'soft_delete_and_anonymize',
      deletedAt: this.userData.deleted_at,
      compliance: 'GDPR_compliant'
    });
  }

  private async anonymizeUserData() {
    if (!this.userData.deleted_at) {
      throw new Error('User deletion timestamp is required');
    }

    const { error } = await this.context.supabase
      .from('user_preferences')
      .update({
        is_deleted: true,
        deleted_at: new Date(this.userData.deleted_at).toISOString(),
        // Anonymize PII for GDPR compliance
        first_name: '[DELETED]',
        last_name: '[USER]',
        display_name: '[DELETED USER]',
        username: `deleted_${this.userData.id.slice(-8)}`,
        bio: null,
        phone: null,
        linkedin_url: null,
        website_url: null,
        location: null,
        profile_picture_url: null,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', this.userData.id);

    if (error) {
      throw new Error(`Failed to anonymize user data: ${error.message}`);
    }

    return { anonymized: true };
  }
}

// ===== USER OPERATION FACTORY =====

export class UserOperationFactory {
  
  static createUserOperation(
    eventType: string,
    context: OperationContext,
    userData: ClerkUserData,
    additionalData?: any
  ): BaseOperation {
    switch (eventType) {
      case 'user.created':
        const primaryEmail = userData.email_addresses?.find(
          (email: any) => email.id === userData.primary_email_address_id
        )?.email_address;
        return new CreateUserOperation(context, userData, primaryEmail);
        
      case 'user.updated':
        return new UpdateUserOperation(context, userData);
        
      case 'user.deleted':
        return new DeleteUserOperation(context, userData);
        
      default:
        throw new Error(`Unsupported user event type: ${eventType}`);
    }
  }
}
