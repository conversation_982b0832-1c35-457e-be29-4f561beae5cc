/**
 * TalentHUB Webhook Security & Validation
 * Comprehensive security validations and sanitization for webhook events
 */

import { NextRequest } from 'next/server';
import { Webhook } from 'svix';
import { 
  WebhookValidationResult, 
  WebhookSecurityError, 
  WebhookValidationError,
  ClerkWebhookEvent,
  ClerkWebhookEventType,
  SecurityEventContext
} from './types';

// ===== WEBHOOK SIGNATURE VERIFICATION =====

export class WebhookSecurity {
  private webhook: Webhook;

  constructor(secret: string) {
    if (!secret) {
      throw new WebhookSecurityError('Webhook secret is required');
    }
    this.webhook = new Webhook(secret);
  }

  /**
   * Verify webhook signature using Svix
   */
  async verifySignature(
    payload: string,
    headers: {
      'svix-id'?: string | null;
      'svix-timestamp'?: string | null;
      'svix-signature'?: string | null;
    }
  ): Promise<ClerkWebhookEvent> {
    const { 'svix-id': id, 'svix-timestamp': timestamp, 'svix-signature': signature } = headers;

    // Validate headers presence
    if (!id || !timestamp || !signature) {
      throw new WebhookSecurityError('Missing required Svix headers', {
        hasId: !!id,
        hasTimestamp: !!timestamp,
        hasSignature: !!signature
      });
    }

    try {
      // Verify the webhook signature
      const event = this.webhook.verify(payload, {
        'svix-id': id,
        'svix-timestamp': timestamp,
        'svix-signature': signature,
      }) as ClerkWebhookEvent;

      return event;
    } catch (error) {
      throw new WebhookSecurityError('Invalid webhook signature', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Validate request origin and rate limiting
   */
  validateRequest(request: NextRequest): WebhookValidationResult {
    try {
      // Check user agent (should be from Svix)
      const userAgent = request.headers.get('user-agent');
      if (!userAgent?.includes('Svix')) {
        return {
          isValid: false,
          error: 'Invalid user agent - not from Svix'
        };
      }

      // Check content type
      const contentType = request.headers.get('content-type');
      if (!contentType?.includes('application/json')) {
        return {
          isValid: false,
          error: 'Invalid content type - must be application/json'
        };
      }

      // Additional security headers validation
      const svixId = request.headers.get('svix-id');
      if (!svixId || svixId.length < 10) {
        return {
          isValid: false,
          error: 'Invalid or missing svix-id header'
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `Request validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

// ===== DATA VALIDATION & SANITIZATION =====

export class WebhookDataValidator {
  
  /**
   * Validate and sanitize user event data
   */
  validateUserData(data: any, eventType: ClerkWebhookEventType): WebhookValidationResult {
    try {
      // Basic structure validation
      if (!data || typeof data !== 'object') {
        return { isValid: false, error: 'Invalid user data structure' };
      }

      // Required fields validation
      if (!data.id || typeof data.id !== 'string') {
        return { isValid: false, error: 'Missing or invalid user ID' };
      }

      // Sanitize and validate based on event type
      const sanitizedData = this.sanitizeUserData(data, eventType);
      
      return {
        isValid: true,
        sanitizedData
      };
    } catch (error) {
      return {
        isValid: false,
        error: `User data validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate and sanitize organization event data
   */
  validateOrganizationData(data: any, eventType: ClerkWebhookEventType): WebhookValidationResult {
    try {
      if (!data || typeof data !== 'object') {
        return { isValid: false, error: 'Invalid organization data structure' };
      }

      if (!data.id || typeof data.id !== 'string') {
        return { isValid: false, error: 'Missing or invalid organization ID' };
      }

      if (eventType !== 'organization.deleted' && (!data.name || typeof data.name !== 'string')) {
        return { isValid: false, error: 'Missing or invalid organization name' };
      }

      const sanitizedData = this.sanitizeOrganizationData(data, eventType);
      
      return {
        isValid: true,
        sanitizedData
      };
    } catch (error) {
      return {
        isValid: false,
        error: `Organization data validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate session event data
   */
  validateSessionData(data: any, eventType: ClerkWebhookEventType): WebhookValidationResult {
    try {
      if (!data || typeof data !== 'object') {
        return { isValid: false, error: 'Invalid session data structure' };
      }

      if (!data.id || typeof data.id !== 'string') {
        return { isValid: false, error: 'Missing or invalid session ID' };
      }

      if (!data.user_id || typeof data.user_id !== 'string') {
        return { isValid: false, error: 'Missing or invalid user ID in session data' };
      }

      const sanitizedData = this.sanitizeSessionData(data, eventType);
      
      return {
        isValid: true,
        sanitizedData
      };
    } catch (error) {
      return {
        isValid: false,
        error: `Session data validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Sanitize user data to prevent XSS and injection attacks
   */
  private sanitizeUserData(data: any, eventType: ClerkWebhookEventType): any {
    const sanitized = { ...data };

    // Sanitize string fields
    if (sanitized.first_name) {
      sanitized.first_name = this.sanitizeString(sanitized.first_name, 50);
    }
    if (sanitized.last_name) {
      sanitized.last_name = this.sanitizeString(sanitized.last_name, 50);
    }
    if (sanitized.username) {
      sanitized.username = this.sanitizeUsername(sanitized.username);
    }

    // Validate email addresses
    if (sanitized.email_addresses && Array.isArray(sanitized.email_addresses)) {
      sanitized.email_addresses = sanitized.email_addresses.map((email: any) => ({
        ...email,
        email_address: this.sanitizeEmail(email.email_address)
      }));
    }

    // Sanitize metadata
    if (sanitized.public_metadata) {
      sanitized.public_metadata = this.sanitizeMetadata(sanitized.public_metadata);
    }

    return sanitized;
  }

  /**
   * Sanitize organization data
   */
  private sanitizeOrganizationData(data: any, eventType: ClerkWebhookEventType): any {
    const sanitized = { ...data };

    if (sanitized.name) {
      sanitized.name = this.sanitizeString(sanitized.name, 100);
    }
    if (sanitized.slug) {
      sanitized.slug = this.sanitizeSlug(sanitized.slug);
    }

    // Sanitize metadata
    if (sanitized.public_metadata) {
      sanitized.public_metadata = this.sanitizeMetadata(sanitized.public_metadata);
    }
    if (sanitized.private_metadata) {
      sanitized.private_metadata = this.sanitizeMetadata(sanitized.private_metadata);
    }

    return sanitized;
  }

  /**
   * Sanitize session data
   */
  private sanitizeSessionData(data: any, eventType: ClerkWebhookEventType): any {
    const sanitized = { ...data };

    // Validate status values
    const validStatuses = ['active', 'ended', 'removed', 'revoked'];
    if (sanitized.status && !validStatuses.includes(sanitized.status)) {
      sanitized.status = 'unknown';
    }

    return sanitized;
  }

  /**
   * Sanitize string input to prevent XSS and injection
   */
  private sanitizeString(input: string, maxLength: number = 255): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[<>'"&]/g, '') // Remove potentially dangerous characters
      .replace(/\s+/g, ' '); // Normalize whitespace
  }

  /**
   * Sanitize username with specific rules
   */
  private sanitizeUsername(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .toLowerCase()
      .slice(0, 30)
      .replace(/[^a-zA-Z0-9._-]/g, ''); // Only allow alphanumeric, dots, underscores, hyphens
  }

  /**
   * Sanitize email address
   */
  private sanitizeEmail(input: string): string {
    if (typeof input !== 'string') return '';
    
    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(input)) {
      throw new WebhookValidationError('Invalid email format');
    }
    
    return input.trim().toLowerCase();
  }

  /**
   * Sanitize organization slug
   */
  private sanitizeSlug(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .toLowerCase()
      .slice(0, 50)
      .replace(/[^a-zA-Z0-9-]/g, ''); // Only allow alphanumeric and hyphens
  }

  /**
   * Sanitize metadata objects
   */
  private sanitizeMetadata(metadata: any): any {
    if (!metadata || typeof metadata !== 'object') return {};
    
    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(metadata)) {
      // Sanitize key
      const sanitizedKey = this.sanitizeString(key, 50);
      
      // Sanitize value based on type
      if (typeof value === 'string') {
        sanitized[sanitizedKey] = this.sanitizeString(value, 500);
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        sanitized[sanitizedKey] = value;
      } else if (Array.isArray(value)) {
        sanitized[sanitizedKey] = value.slice(0, 10); // Limit array size
      } else if (typeof value === 'object' && value !== null) {
        // Recursively sanitize nested objects (limit depth)
        sanitized[sanitizedKey] = this.sanitizeMetadata(value);
      }
    }
    
    return sanitized;
  }
}

// ===== SECURITY CONTEXT ANALYSIS =====

export class SecurityAnalyzer {
  
  /**
   * Analyze if an event represents a security concern
   */
  analyzeSecurityContext(
    eventType: ClerkWebhookEventType, 
    data: any, 
    request: NextRequest
  ): SecurityEventContext {
    
    const context: SecurityEventContext = {
      isSecurityEvent: false,
      eventSeverity: 'low',
      requiresAlert: false
    };

    // Session-based security events
    if (eventType === 'session.revoked') {
      context.isSecurityEvent = true;
      context.eventSeverity = 'high';
      context.requiresAlert = true;
      context.additionalContext = {
        reason: 'Session forcibly revoked - potential security incident'
      };
    }

    // User deletion events
    if (eventType === 'user.deleted') {
      context.isSecurityEvent = true;
      context.eventSeverity = 'medium';
      context.requiresAlert = true;
      context.additionalContext = {
        reason: 'User account deleted - compliance and security monitoring required'
      };
    }

    // Organization changes
    if (eventType === 'organization.deleted') {
      context.isSecurityEvent = true;
      context.eventSeverity = 'high';
      context.requiresAlert = true;
      context.additionalContext = {
        reason: 'Organization deleted - major structural change'
      };
    }

    // Role changes
    if (eventType === 'organizationMembership.updated') {
      context.isSecurityEvent = true;
      context.eventSeverity = 'medium';
      context.requiresAlert = false; // Normal business operation
      context.additionalContext = {
        reason: 'User role changed - access control modification'
      };
    }

    return context;
  }

  /**
   * Check for suspicious patterns in webhook events
   */
  detectSuspiciousActivity(
    eventType: ClerkWebhookEventType,
    data: any,
    request: NextRequest
  ): { isSuspicious: boolean; reasons: string[] } {
    
    const reasons: string[] = [];
    
    // Check for unusual IP patterns
    const ip = request.ip;
    if (ip && this.isUnusualIP(ip)) {
      reasons.push('Request from unusual IP address');
    }

    // Check for malformed user agent
    const userAgent = request.headers.get('user-agent');
    if (!userAgent || !userAgent.includes('Svix')) {
      reasons.push('Invalid or missing user agent');
    }

    return {
      isSuspicious: reasons.length > 0,
      reasons
    };
  }

  /**
   * Basic IP validation (extend with actual geo-blocking logic)
   */
  private isUnusualIP(ip: string): boolean {
    // Implement actual IP validation logic here
    return false; // Placeholder
  }
}

// ===== SECURITY UTILITIES =====

export function createSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  };
}

export function logSecurityEvent(
  event: string, 
  severity: 'low' | 'medium' | 'high' | 'critical',
  context: Record<string, any>
): void {
  console.warn(`🚨 SECURITY EVENT [${severity.toUpperCase()}]: ${event}`, {
    timestamp: new Date().toISOString(),
    ...context
  });
}

/**
 * Rate limiting for webhook endpoints
 */
export class WebhookRateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 100, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  /**
   * Check if request is within rate limits
   */
  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      // Reset or create new record
      this.attempts.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs
      });
      return true;
    }

    if (record.count >= this.maxAttempts) {
      return false;
    }

    record.count++;
    return true;
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.attempts.entries()) {
      if (now > record.resetTime) {
        this.attempts.delete(key);
      }
    }
  }
}

// ===== WEBHOOK SECURITY MANAGER =====

export class WebhookSecurityManager {
  private security: WebhookSecurity;
  private validator: WebhookDataValidator;
  private analyzer: SecurityAnalyzer;
  private rateLimiter: WebhookRateLimiter;

  constructor(webhookSecret: string) {
    this.security = new WebhookSecurity(webhookSecret);
    this.validator = new WebhookDataValidator();
    this.analyzer = new SecurityAnalyzer();
    this.rateLimiter = new WebhookRateLimiter();
  }

  /**
   * Comprehensive security validation for webhook requests
   */
  async validateWebhookRequest(
    payload: string,
    request: NextRequest
  ): Promise<{
    isValid: boolean;
    event?: ClerkWebhookEvent;
    error?: string;
    securityContext?: SecurityEventContext;
  }> {
    try {
      // Rate limiting check
      const clientIdentifier = request.ip || 'unknown';
      if (!this.rateLimiter.isAllowed(clientIdentifier)) {
        logSecurityEvent('Rate limit exceeded', 'medium', { ip: clientIdentifier });
        return {
          isValid: false,
          error: 'Rate limit exceeded'
        };
      }

      // Basic request validation
      const requestValidation = this.security.validateRequest(request);
      if (!requestValidation.isValid) {
        logSecurityEvent('Invalid request', 'low', { error: requestValidation.error });
        return {
          isValid: false,
          error: requestValidation.error
        };
      }

      // Signature verification
      const event = await this.security.verifySignature(payload, {
        'svix-id': request.headers.get('svix-id'),
        'svix-timestamp': request.headers.get('svix-timestamp'),
        'svix-signature': request.headers.get('svix-signature')
      });

      // Data validation based on event type
      let dataValidation: WebhookValidationResult;
      
      if (event.type.startsWith('user.')) {
        dataValidation = this.validator.validateUserData(event.data, event.type);
      } else if (event.type.startsWith('organization.') && !event.type.includes('Membership') && !event.type.includes('Invitation')) {
        dataValidation = this.validator.validateOrganizationData(event.data, event.type);
      } else if (event.type.startsWith('session.')) {
        dataValidation = this.validator.validateSessionData(event.data, event.type);
      } else {
        // For other events, do basic validation
        dataValidation = { isValid: true, sanitizedData: event.data };
      }

      if (!dataValidation.isValid) {
        logSecurityEvent('Data validation failed', 'medium', { 
          eventType: event.type,
          error: dataValidation.error 
        });
        return {
          isValid: false,
          error: dataValidation.error
        };
      }

      // Security context analysis
      const securityContext = this.analyzer.analyzeSecurityContext(event.type, event.data, request);
      
      // Suspicious activity detection
      const suspiciousActivity = this.analyzer.detectSuspiciousActivity(event.type, event.data, request);
      if (suspiciousActivity.isSuspicious) {
        logSecurityEvent('Suspicious activity detected', 'high', {
          eventType: event.type,
          reasons: suspiciousActivity.reasons
        });
      }

      // Update event data with sanitized version
      if (dataValidation.sanitizedData) {
        event.data = dataValidation.sanitizedData;
      }

      return {
        isValid: true,
        event,
        securityContext
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown security error';
      logSecurityEvent('Security validation failed', 'high', { error: errorMessage });
      
      return {
        isValid: false,
        error: errorMessage
      };
    }
  }

  /**
   * Periodic cleanup of rate limiting data
   */
  performMaintenance(): void {
    this.rateLimiter.cleanup();
  }
}
