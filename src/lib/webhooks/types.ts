/**
 * TalentHUB Webhook Types & Interfaces
 * Comprehensive type definitions for Clerk webhook events
 */

import { NextRequest } from 'next/server';

// ===== CLERK WEBHOOK EVENT TYPES =====

export type ClerkWebhookEventType = 
  // User Events
  | 'user.created'
  | 'user.updated' 
  | 'user.deleted'
  // Organization Events
  | 'organization.created'
  | 'organization.updated'
  | 'organization.deleted'
  // Organization Membership Events
  | 'organizationMembership.created'
  | 'organizationMembership.updated'
  | 'organizationMembership.deleted'
  // Organization Invitation Events
  | 'organizationInvitation.created'
  | 'organizationInvitation.accepted'
  | 'organizationInvitation.revoked'
  // Session Events
  | 'session.created'
  | 'session.ended'
  | 'session.removed'
  | 'session.revoked';

// ===== WEBHOOK PRIORITY CLASSIFICATION =====

export const WEBHOOK_EVENT_PRIORITIES = {
  PRIORITY_1: [
    'user.deleted',
    'organization.created', 
    'organizationMembership.updated',
    'session.created'
  ] as const,
  PRIORITY_2: [
    'organizationInvitation.created',
    'organizationInvitation.accepted',
    'organization.updated',
    'session.revoked'
  ] as const,
  PRIORITY_3: [
    'organization.deleted',
    'organizationInvitation.revoked',
    'session.removed'
  ] as const,
  EXISTING: [
    'user.created',
    'user.updated',
    'organizationMembership.created',
    'organizationMembership.deleted',
    'session.ended'
  ] as const
} as const;

// ===== WEBHOOK DATA INTERFACES =====

export interface ClerkWebhookEvent {
  data: any;
  object: 'event';
  type: ClerkWebhookEventType;
  timestamp: number;
  instance_id: string;
}

export interface WebhookProcessingContext {
  eventId: string;
  eventType: ClerkWebhookEventType;
  startTime: number;
  supabase: any;
  request: NextRequest;
}

export interface WebhookProcessingResult {
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

// ===== USER EVENT DATA INTERFACES =====

export interface ClerkUserData {
  id: string;
  first_name?: string;
  last_name?: string;
  email_addresses?: ClerkEmailAddress[];
  primary_email_address_id?: string;
  username?: string;
  image_url?: string;
  profile_image_url?: string;
  created_at: number;
  updated_at: number;
  deleted_at?: number;
  public_metadata?: Record<string, any>;
  private_metadata?: Record<string, any>;
  unsafe_metadata?: Record<string, any>;
}

export interface ClerkEmailAddress {
  id: string;
  email_address: string;
  verification?: {
    status: 'verified' | 'unverified';
    strategy?: string;
  };
}

// ===== ORGANIZATION EVENT DATA INTERFACES =====

export interface ClerkOrganizationData {
  id: string;
  name: string;
  slug: string;
  image_url?: string;
  created_at: number;
  updated_at: number;
  deleted_at?: number;
  created_by?: string;
  public_metadata?: Record<string, any>;
  private_metadata?: Record<string, any>;
}

export interface ClerkOrganizationMembershipData {
  id: string;
  public_user_data: {
    user_id: string;
    first_name?: string;
    last_name?: string;
    image_url?: string;
  };
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  role: string;
  created_at: number;
  updated_at: number;
}

export interface ClerkOrganizationInvitationData {
  id: string;
  email_address: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  inviter_user_id?: string;
  role: string;
  status: 'pending' | 'accepted' | 'revoked' | 'expired';
  created_at: number;
  updated_at: number;
}

// ===== SESSION EVENT DATA INTERFACES =====

export interface ClerkSessionData {
  id: string;
  user_id: string;
  status: 'active' | 'ended' | 'removed' | 'revoked';
  created_at: number;
  updated_at: number;
  ended_at?: number;
  last_active_at?: number;
  expire_at?: number;
}

// ===== ACTIVITY TRACKING INTERFACES =====

export interface ActivityData {
  [key: string]: any;
  webhook_source?: 'clerk';
  processed_at?: string;
  security_event?: boolean;
}

export interface UserActivityLog {
  id: string;
  user_id: string;
  org_id?: string;
  activity_type: string;
  activity_data: ActivityData;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

// ===== WEBHOOK LOGGING INTERFACES =====

export interface WebhookEventLog {
  id: string;
  event_id?: string;
  event_type: ClerkWebhookEventType;
  clerk_user_id?: string;
  clerk_org_id?: string;
  processing_status: 'processing' | 'success' | 'failed' | 'retry';
  processing_duration_ms?: number;
  error_message?: string;
  error_details?: Record<string, any>;
  retry_count: number;
  event_data?: Record<string, any>;
  processed_at: string;
  created_at: string;
}

// ===== VALIDATION SCHEMAS =====

export interface WebhookValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedData?: any;
}

// ===== SECURITY INTERFACES =====

export interface SecurityEventContext {
  isSecurityEvent: boolean;
  eventSeverity: 'low' | 'medium' | 'high' | 'critical';
  requiresAlert: boolean;
  additionalContext?: Record<string, any>;
}

// ===== HANDLER FUNCTION TYPES =====

export type WebhookHandler = (
  data: any,
  context: WebhookProcessingContext
) => Promise<WebhookProcessingResult>;

export interface WebhookHandlerRegistry {
  [K in ClerkWebhookEventType]?: WebhookHandler;
}

// ===== ERROR HANDLING =====

export class WebhookError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'WebhookError';
  }
}

export class WebhookValidationError extends WebhookError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', context);
    this.name = 'WebhookValidationError';
  }
}

export class WebhookSecurityError extends WebhookError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'SECURITY_ERROR', context);
    this.name = 'WebhookSecurityError';
  }
}

// ===== CONFIGURATION =====

export interface WebhookConfig {
  maxRetries: number;
  timeoutMs: number;
  enableDetailedLogging: boolean;
  enableSecurityMonitoring: boolean;
  rateLimitThreshold: number;
}

export const DEFAULT_WEBHOOK_CONFIG: WebhookConfig = {
  maxRetries: 3,
  timeoutMs: 30000, // 30 seconds
  enableDetailedLogging: true,
  enableSecurityMonitoring: true,
  rateLimitThreshold: 100 // requests per minute
};
