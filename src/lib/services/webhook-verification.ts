import crypto from 'crypto'
import { createClient } from '@/lib/supabase'
import { trackUserActivity, ACTIVITY_TYPES } from './activity-tracking'
import { 
  createEnhancedUserProfile, 
  updateEnhancedUserProfile,
  associateUserWithOrganization
} from './user-preferences'
import { updateLastActive } from './activity-tracking'

/**
 * Webhook Verification Service
 * 
 * Provides secure webhook signature verification and event processing
 * for Clerk webhooks. Handles user lifecycle events and maintains
 * sync between Clerk and Supabase.
 */

export interface ClerkWebhookEvent {
  type: string
  object: 'event'
  data: any
}

export interface ClerkUser {
  id: string
  email_addresses: Array<{
    email_address: string
    verification?: {
      status: string
    }
  }>
  first_name: string
  last_name: string
  username?: string
  profile_image_url?: string
  created_at: number
  updated_at: number
}

export interface ClerkOrganizationMembership {
  id: string
  object: 'organization_membership'
  organization: {
    id: string
    name: string
    slug: string
    created_at: number
  }
  public_user_data: {
    user_id: string
    first_name: string
    last_name: string
    profile_image_url?: string
  }
  role: string
  created_at: number
}

/**
 * Verifies <PERSON> webhook signature
 */
export function verifyClerkWebhook(
  signature: string,
  body: string,
  secret: string
): boolean {
  try {
    // Parse the signature header
    const signatureParts = signature.split(',')
    const timestamp = signatureParts.find(part => part.startsWith('t='))?.split('=')[1]
    const signatures = signatureParts.filter(part => part.startsWith('v1='))
    
    if (!timestamp || signatures.length === 0) {
      console.error('Invalid signature format')
      return false
    }
    
    // Check timestamp freshness (within 5 minutes)
    const webhookTimestamp = parseInt(timestamp, 10)
    const currentTimestamp = Math.floor(Date.now() / 1000)
    const timeDifference = Math.abs(currentTimestamp - webhookTimestamp)
    
    if (timeDifference > 300) { // 5 minutes
      console.error('Webhook timestamp too old')
      return false
    }
    
    // Create expected signature
    const payload = `${timestamp}.${body}`
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')
    
    // Compare signatures using timing-safe comparison
    for (const sig of signatures) {
      const providedSignature = sig.split('=')[1]
      if (crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(providedSignature, 'hex')
      )) {
        return true
      }
    }
    
    return false
    
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return false
  }
}

/**
 * Processes user-related webhook events
 */
export async function processUserEvent(
  eventType: string,
  eventData: any
): Promise<{ success: boolean; error?: string }> {
  try {
    switch (eventType) {
      case 'user.created':
        return await handleUserCreated(eventData)
        
      case 'user.updated':
        return await handleUserUpdated(eventData)
        
      case 'session.ended':
        return await handleSessionEnded(eventData)
        
      default:
        console.log('Unhandled user event type:', eventType)
        return { success: true }
    }
    
  } catch (error) {
    console.error('Process user event failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Processes organization-related webhook events
 */
export async function processOrganizationEvent(
  eventType: string,
  eventData: any
): Promise<{ success: boolean; error?: string }> {
  try {
    switch (eventType) {
      case 'organizationMembership.created':
        return await handleOrganizationMembershipCreated(eventData)
        
      case 'organizationMembership.deleted':
        return await handleOrganizationMembershipDeleted(eventData)
        
      case 'organizationMembership.updated':
        return await handleOrganizationMembershipUpdated(eventData)
        
      default:
        console.log('Unhandled organization event type:', eventType)
        return { success: true }
    }
    
  } catch (error) {
    console.error('Process organization event failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Handles user creation event
 */
async function handleUserCreated(userData: ClerkUser): Promise<{ success: boolean; error?: string }> {
  try {
    const primaryEmail = userData.email_addresses.find(
      email => email.verification?.status === 'verified'
    )?.email_address || userData.email_addresses[0]?.email_address
    
    if (!primaryEmail) {
      return { success: false, error: 'No email address found' }
    }
    
    // Generate username from email if not provided
    const username = userData.username || primaryEmail.split('@')[0]
    
    const result = await createEnhancedUserProfile({
      userId: userData.id,
      firstName: userData.first_name || 'User',
      lastName: userData.last_name || '',
      username: username,
      email: primaryEmail
    })
    
    if (!result.success) {
      return { success: false, error: result.error }
    }
    
    // Update profile picture if available
    if (userData.profile_image_url) {
      await updateEnhancedUserProfile(userData.id, {
        profilePictureUrl: userData.profile_image_url
      })
    }
    
    // Track user creation activity
    await trackUserActivity(
      userData.id,
      ACTIVITY_TYPES.LOGIN,
      { source: 'user_created', clerk_created_at: userData.created_at }
    )
    
    return { success: true }
    
  } catch (error) {
    console.error('Handle user created failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Handles user update event
 */
async function handleUserUpdated(userData: ClerkUser): Promise<{ success: boolean; error?: string }> {
  try {
    const updates: any = {}
    
    // Update basic profile information
    if (userData.first_name) updates.firstName = userData.first_name
    if (userData.last_name) updates.lastName = userData.last_name
    if (userData.username) updates.username = userData.username
    if (userData.profile_image_url) updates.profilePictureUrl = userData.profile_image_url
    
    if (Object.keys(updates).length > 0) {
      const result = await updateEnhancedUserProfile(userData.id, updates)
      
      if (!result.success) {
        return { success: false, error: result.error }
      }
      
      // Track profile update activity
      await trackUserActivity(
        userData.id,
        ACTIVITY_TYPES.PROFILE_UPDATED,
        { 
          source: 'clerk_sync', 
          updated_fields: Object.keys(updates),
          clerk_updated_at: userData.updated_at
        }
      )
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Handle user updated failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Handles session ended event
 */
async function handleSessionEnded(sessionData: any): Promise<{ success: boolean; error?: string }> {
  try {
    if (sessionData.user_id) {
      await updateLastActive(sessionData.user_id)
      
      await trackUserActivity(
        sessionData.user_id,
        ACTIVITY_TYPES.LOGOUT,
        { session_id: sessionData.id }
      )
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Handle session ended failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Handles organization membership creation
 */
async function handleOrganizationMembershipCreated(
  membershipData: ClerkOrganizationMembership
): Promise<{ success: boolean; error?: string }> {
  try {
    const userId = membershipData.public_user_data.user_id
    const orgId = membershipData.organization.id
    
    // Find the organization in our database
    const supabase = createClient()
    const { data: organization } = await supabase
      .from('organizations')
      .select('id')
      .eq('clerk_org_id', orgId)
      .single()
    
    if (organization) {
      // Associate user with organization
      await associateUserWithOrganization(userId, organization.id, true)
      
      // Track organization join activity
      await trackUserActivity(
        userId,
        ACTIVITY_TYPES.ORGANIZATION_JOINED,
        { 
          organization_id: organization.id,
          clerk_org_id: orgId,
          role: membershipData.role
        }
      )
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Handle organization membership created failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Handles organization membership deletion
 */
async function handleOrganizationMembershipDeleted(
  membershipData: ClerkOrganizationMembership
): Promise<{ success: boolean; error?: string }> {
  try {
    const userId = membershipData.public_user_data.user_id
    
    // Remove organization association
    await associateUserWithOrganization(userId, '', false)
    
    // Track organization leave activity
    await trackUserActivity(
      userId,
      ACTIVITY_TYPES.ORGANIZATION_LEFT,
      { 
        clerk_org_id: membershipData.organization.id,
        role: membershipData.role
      }
    )
    
    return { success: true }
    
  } catch (error) {
    console.error('Handle organization membership deleted failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Handles organization membership updates (role changes)
 */
async function handleOrganizationMembershipUpdated(
  membershipData: ClerkOrganizationMembership
): Promise<{ success: boolean; error?: string }> {
  try {
    const userId = membershipData.public_user_data.user_id
    
    // Track role change activity
    await trackUserActivity(
      userId,
      ACTIVITY_TYPES.ORGANIZATION_JOINED, // Using this for role updates too
      { 
        clerk_org_id: membershipData.organization.id,
        new_role: membershipData.role,
        action: 'role_updated'
      }
    )
    
    return { success: true }
    
  } catch (error) {
    console.error('Handle organization membership updated failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}