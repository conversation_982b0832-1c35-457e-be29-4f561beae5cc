import { createClient } from '@/lib/supabase'
import { 
  UserActivity, 
  UserDashboardStats, 
  ActivityType, 
  ACTIVITY_TYPES 
} from '@/lib/types/profile'

// Re-export ACTIVITY_TYPES for other services
export { ACTIVITY_TYPES } from '@/lib/types/profile'

/**
 * Activity Tracking Service
 * 
 * Manages user activity logging for audit and analytics.
 * Provides enterprise-grade activity tracking capabilities.
 */

/**
 * Tracks a user activity with optional context
 */
export async function trackUserActivity(
  userId: string,
  activityType: ActivityType,
  activityData?: Record<string, any>,
  request?: Request
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    // Extract IP and user agent from request if provided
    let ipAddress: string | undefined
    let userAgent: string | undefined
    
    if (request) {
      // Get IP address (handle various headers for different deployments)
      ipAddress = 
        request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
        request.headers.get('x-real-ip') ||
        request.headers.get('cf-connecting-ip') ||
        undefined
      
      userAgent = request.headers.get('user-agent') || undefined
    }
    
    const { error } = await supabase
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        activity_type: activityType,
        activity_data: activityData || {},
        ip_address: ipAddress,
        user_agent: userAgent
      })
    
    if (error) {
      console.error('Error tracking user activity:', error)
      return { success: false, error: error.message }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Track user activity failed:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Gets user activity log with pagination
 */
export async function getUserActivityLog(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<UserActivity[]> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_activity_logs')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (error) {
      console.error('Error fetching user activity log:', error)
      return []
    }
    
    return (data || []).map(transformDatabaseToActivity)
    
  } catch (error) {
    console.error('Get user activity log failed:', error)
    return []
  }
}

/**
 * Gets user dashboard statistics
 */
export async function getUserDashboardStats(
  userId: string,
  orgId?: string
): Promise<UserDashboardStats | null> {
  try {
    const supabase = createClient()
    
    // Get total activities count
    const { count: totalActivities } = await supabase
      .from('user_activity_logs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
    
    // Get last login
    const { data: lastLogin } = await supabase
      .from('user_activity_logs')
      .select('created_at')
      .eq('user_id', userId)
      .eq('activity_type', ACTIVITY_TYPES.LOGIN)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
    
    // Get user preferences for other stats
    const { data: preferences } = await supabase
      .from('user_preferences')
      .select(`
        profile_completeness,
        account_tier,
        subscription_status,
        org_id,
        is_org_member
      `)
      .eq('user_id', userId)
      .single()
    
    if (!preferences) {
      return null
    }
    
    // Get organization role if applicable
    let organizationRole: string | undefined
    if (preferences.org_id && preferences.is_org_member) {
      const { data: roleData } = await supabase
        .from('user_process_roles')
        .select('role')
        .eq('user_id', userId)
        .eq('org_id', preferences.org_id)
        .limit(1)
        .single()
      
      organizationRole = roleData?.role
    }
    
    return {
      totalActivities: totalActivities || 0,
      lastLoginDate: lastLogin?.created_at || new Date().toISOString(),
      profileCompleteness: preferences.profile_completeness || 0,
      organizationRole,
      accountTier: preferences.account_tier,
      subscriptionStatus: preferences.subscription_status
    }
    
  } catch (error) {
    console.error('Get user dashboard stats failed:', error)
    return null
  }
}

/**
 * Gets recent activity for dashboard
 */
export async function getRecentActivity(
  userId: string,
  limit: number = 10
): Promise<UserActivity[]> {
  return getUserActivityLog(userId, limit, 0)
}

/**
 * Gets activity by type
 */
export async function getActivityByType(
  userId: string,
  activityType: ActivityType,
  limit: number = 20
): Promise<UserActivity[]> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_activity_logs')
      .select('*')
      .eq('user_id', userId)
      .eq('activity_type', activityType)
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) {
      console.error('Error fetching activity by type:', error)
      return []
    }
    
    return (data || []).map(transformDatabaseToActivity)
    
  } catch (error) {
    console.error('Get activity by type failed:', error)
    return []
  }
}

/**
 * Updates user's last active timestamp
 */
export async function updateLastActive(userId: string): Promise<{ success: boolean }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('user_preferences')
      .update({ last_active: new Date().toISOString() })
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error updating last active:', error)
      return { success: false }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Update last active failed:', error)
    return { success: false }
  }
}

/**
 * Transform database record to UserActivity interface
 */
function transformDatabaseToActivity(record: any): UserActivity {
  return {
    id: record.id,
    userId: record.user_id,
    orgId: record.org_id,
    activityType: record.activity_type,
    activityData: record.activity_data || {},
    ipAddress: record.ip_address,
    userAgent: record.user_agent,
    createdAt: record.created_at
  }
}