/**
 * Tier Gating Service
 * 
 * Centralized service for managing tier-based access control throughout TalentHUB.
 * Handles validation, upgrade requirements, and feature availability based on account tiers.
 * 
 * Features:
 * - Tier-based organization access validation
 * - Upgrade requirement calculation
 * - Feature availability checking
 * - Admin capability management
 * - Revenue protection for billing
 */

export type AccountTier = 'free' | 'pro' | 'team' | 'enterprise'

export interface TierAccess {
  canDetectOrganizations: boolean    // All tiers (for upgrade prompts)
  canJoinOrganizations: boolean      // Team+ only
  canCreateOrganizations: boolean    // Enterprise only
  canBecomeAdmin: boolean           // Team+ only
  maxOrgMembers: number             // Team: 10, Enterprise: unlimited
  adminCapabilities: string[]       // Different admin features per tier
}

export interface TierValidationResult {
  allowed: boolean
  requiresUpgrade: boolean
  currentTier: AccountTier
  requiredTier?: AccountTier
  message: string
  upgradePrompt?: string
}

export interface UpgradeRequirement {
  fromTier: AccountTier
  toTier: AccountTier
  action: string
  benefits: string[]
  pricing?: {
    monthly: number
    annually: number
  }
  urgency: 'low' | 'medium' | 'high'
}

// Tier access matrix - defines what each tier can do
export const TIER_ACCESS: Record<AccountTier, TierAccess> = {
  free: {
    canDetectOrganizations: true,     // For upgrade prompts
    canJoinOrganizations: false,
    canCreateOrganizations: false,
    canBecomeAdmin: false,
    maxOrgMembers: 0,
    adminCapabilities: []
  },
  pro: {
    canDetectOrganizations: true,     // For upgrade prompts
    canJoinOrganizations: false,
    canCreateOrganizations: false,
    canBecomeAdmin: false,
    maxOrgMembers: 0,
    adminCapabilities: []
  },
  team: {
    canDetectOrganizations: true,
    canJoinOrganizations: true,       // ✅ Can join existing orgs
    canCreateOrganizations: false,     // Must upgrade to Enterprise
    canBecomeAdmin: true,             // ✅ Can become admin
    maxOrgMembers: 10,
    adminCapabilities: ['invite_members', 'manage_domains', 'basic_settings']
  },
  enterprise: {
    canDetectOrganizations: true,
    canJoinOrganizations: true,
    canCreateOrganizations: true,     // ✅ Can create new orgs
    canBecomeAdmin: true,
    maxOrgMembers: -1, // Unlimited
    adminCapabilities: ['all'] // Full admin access
  }
}

// Upgrade paths and requirements
export const UPGRADE_PATHS: Record<AccountTier, UpgradeRequirement[]> = {
  free: [
    {
      fromTier: 'free',
      toTier: 'team',
      action: 'join_organization',
      benefits: [
        'Join your company team on TalentHUB',
        'Collaborate with up to 10 team members',
        'Access team-specific features',
        'Become a team administrator'
      ],
      pricing: { monthly: 19, annually: 15 },
      urgency: 'high'
    },
    {
      fromTier: 'free',
      toTier: 'pro',
      action: 'individual_upgrade',
      benefits: [
        'Unlimited personal records',
        'Advanced search capabilities',
        'API access and integrations',
        'Analytics dashboard'
      ],
      pricing: { monthly: 9, annually: 7 },
      urgency: 'low'
    }
  ],
  pro: [
    {
      fromTier: 'pro',
      toTier: 'team',
      action: 'join_organization',
      benefits: [
        'Join your company team on TalentHUB',
        'Keep all Pro features plus team collaboration',
        'Become a team administrator',
        'Manage team workflows'
      ],
      pricing: { monthly: 19, annually: 15 },
      urgency: 'high'
    }
  ],
  team: [
    {
      fromTier: 'team',
      toTier: 'enterprise',
      action: 'create_organization',
      benefits: [
        'Create new organizations',
        'Unlimited team members',
        'Advanced admin capabilities',
        'Enterprise-grade features'
      ],
      pricing: { monthly: 49, annually: 39 },
      urgency: 'medium'
    }
  ],
  enterprise: [] // No upgrades needed
}

/**
 * Validates if a user can perform an organization-related action
 */
export function validateOrganizationAccess(
  action: 'detect' | 'join' | 'create' | 'admin' | 'invite',
  accountTier: AccountTier
): TierValidationResult {
  const tierAccess = TIER_ACCESS[accountTier]
  
  switch (action) {
    case 'detect':
      return {
        allowed: tierAccess.canDetectOrganizations,
        requiresUpgrade: false,
        currentTier: accountTier,
        message: 'Organization detection is available for all users'
      }
      
    case 'join':
      if (tierAccess.canJoinOrganizations) {
        return {
          allowed: true,
          requiresUpgrade: false,
          currentTier: accountTier,
          message: 'You can join organizations with your current plan'
        }
      }
      
      return {
        allowed: false,
        requiresUpgrade: true,
        currentTier: accountTier,
        requiredTier: 'team',
        message: 'Upgrade to Team plan to join your organization',
        upgradePrompt: 'Join your team and start collaborating today!'
      }
      
    case 'create':
      if (tierAccess.canCreateOrganizations) {
        return {
          allowed: true,
          requiresUpgrade: false,
          currentTier: accountTier,
          message: 'You can create organizations with your current plan'
        }
      }
      
      return {
        allowed: false,
        requiresUpgrade: true,
        currentTier: accountTier,
        requiredTier: 'enterprise',
        message: 'Upgrade to Enterprise to create organizations',
        upgradePrompt: 'Create and manage multiple organizations'
      }
      
    case 'admin':
      if (tierAccess.canBecomeAdmin) {
        return {
          allowed: true,
          requiresUpgrade: false,
          currentTier: accountTier,
          message: 'You can become an organization admin'
        }
      }
      
      return {
        allowed: false,
        requiresUpgrade: true,
        currentTier: accountTier,
        requiredTier: 'team',
        message: 'Upgrade to Team plan to become an admin',
        upgradePrompt: 'Manage your team and organization settings'
      }
      
    case 'invite':
      if (tierAccess.adminCapabilities.includes('invite_members') || 
          tierAccess.adminCapabilities.includes('all')) {
        return {
          allowed: true,
          requiresUpgrade: false,
          currentTier: accountTier,
          message: 'You can invite team members'
        }
      }
      
      return {
        allowed: false,
        requiresUpgrade: true,
        currentTier: accountTier,
        requiredTier: 'team',
        message: 'Upgrade to Team plan to invite members',
        upgradePrompt: 'Build your team and collaborate effectively'
      }
      
    default:
      return {
        allowed: false,
        requiresUpgrade: true,
        currentTier: accountTier,
        message: 'Unknown action requested'
      }
  }
}

/**
 * Gets upgrade requirement for a specific action
 */
export function getUpgradeRequirement(
  currentTier: AccountTier,
  action: string
): UpgradeRequirement | null {
  const availableUpgrades = UPGRADE_PATHS[currentTier]
  
  // Find the most relevant upgrade for the action
  if (action.includes('organization') || action.includes('join') || action.includes('team')) {
    return availableUpgrades.find(upgrade => 
      upgrade.action === 'join_organization' || 
      upgrade.toTier === 'team'
    ) || null
  }
  
  if (action.includes('create') || action.includes('enterprise')) {
    return availableUpgrades.find(upgrade => 
      upgrade.action === 'create_organization' || 
      upgrade.toTier === 'enterprise'
    ) || null
  }
  
  // Default to first available upgrade
  return availableUpgrades[0] || null
}

/**
 * Checks if user has specific admin capability
 */
export function hasAdminCapability(
  accountTier: AccountTier,
  capability: string
): boolean {
  const tierAccess = TIER_ACCESS[accountTier]
  
  return tierAccess.adminCapabilities.includes('all') ||
         tierAccess.adminCapabilities.includes(capability)
}

/**
 * Gets the maximum number of organization members for a tier
 */
export function getMaxOrgMembers(accountTier: AccountTier): number {
  return TIER_ACCESS[accountTier].maxOrgMembers
}

/**
 * Determines if organization member limit is reached
 */
export function isOrgMemberLimitReached(
  accountTier: AccountTier,
  currentMembers: number
): boolean {
  const maxMembers = getMaxOrgMembers(accountTier)
  
  // -1 means unlimited
  if (maxMembers === -1) return false
  
  return currentMembers >= maxMembers
}

/**
 * Gets contextual upgrade message based on user journey
 */
export function getContextualUpgradeMessage(
  context: 'domain_detection' | 'join_attempt' | 'admin_attempt' | 'create_attempt',
  organizationName?: string
): string {
  switch (context) {
    case 'domain_detection':
      return organizationName 
        ? `Your company team (${organizationName}) is on TalentHUB! Upgrade to Team plan to join them.`
        : 'Your company team is on TalentHUB! Upgrade to Team plan to join them.'
        
    case 'join_attempt':
      return 'Team collaboration requires a Team plan. Upgrade now to join your organization.'
      
    case 'admin_attempt':
      return 'Organization administration requires a Team plan or higher. Upgrade to manage your team.'
      
    case 'create_attempt':
      return 'Creating organizations requires an Enterprise plan. Upgrade for full organization management.'
      
    default:
      return 'Upgrade your plan to unlock team collaboration features.'
  }
}

/**
 * Calculates annual savings for upgrade pricing
 */
export function calculateAnnualSavings(monthlyPrice: number, annualPrice: number): {
  monthlySavings: number
  yearlyTotal: number
  percentSaved: number
} {
  const monthlyTotal = monthlyPrice * 12
  const yearlyTotal = annualPrice * 12
  const monthlySavings = monthlyPrice - annualPrice
  const percentSaved = Math.round(((monthlyTotal - yearlyTotal) / monthlyTotal) * 100)
  
  return {
    monthlySavings,
    yearlyTotal,
    percentSaved
  }
}
