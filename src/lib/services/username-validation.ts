import { createClient } from '@/lib/supabase'

/**
 * Username Validation Service
 * 
 * Provides comprehensive username validation, availability checking,
 * and smart username suggestions for the TalentHUB platform.
 * 
 * Features:
 * - Global username uniqueness validation
 * - Real-time availability checking  
 * - Smart first.last suggestions
 * - Format validation with clear error messages
 * - Optimized for enterprise use cases
 */

// Username validation constants
export const USERNAME_CONSTRAINTS = {
  MIN_LENGTH: 3,
  MAX_LENGTH: 30,
  PATTERN: /^[a-zA-Z0-9._-]+$/,
  RESERVED_USERNAMES: [
    'admin', 'administrator', 'root', 'support', 'help',
    'api', 'www', 'mail', 'ftp', 'blog', 'news',
    'test', 'demo', 'system', 'null', 'undefined'
  ]
} as const

// Validation result types
export interface UsernameValidationResult {
  isValid: boolean
  isAvailable: boolean
  errors: string[]
  suggestions?: string[]
}

export interface UsernameFormatValidation {
  isValid: boolean
  errors: string[]
}

/**
 * Validates username format without checking availability
 */
export function validateUsernameFormat(username: string): UsernameFormatValidation {
  const errors: string[] = []
  
  // Trim and convert to lowercase for validation
  const trimmedUsername = username.trim().toLowerCase()
  
  // Check length requirements
  if (trimmedUsername.length < USERNAME_CONSTRAINTS.MIN_LENGTH) {
    errors.push(`Username must be at least ${USERNAME_CONSTRAINTS.MIN_LENGTH} characters long`)
  }
  
  if (trimmedUsername.length > USERNAME_CONSTRAINTS.MAX_LENGTH) {
    errors.push(`Username cannot exceed ${USERNAME_CONSTRAINTS.MAX_LENGTH} characters`)
  }
  
  // Check pattern requirements
  if (!USERNAME_CONSTRAINTS.PATTERN.test(trimmedUsername)) {
    errors.push('Username can only contain letters, numbers, dots, hyphens, and underscores')
  }
  
  // Check for reserved usernames
  if (USERNAME_CONSTRAINTS.RESERVED_USERNAMES.includes(trimmedUsername)) {
    errors.push('This username is reserved and cannot be used')
  }
  
  // Check for invalid patterns
  if (trimmedUsername.startsWith('.') || trimmedUsername.endsWith('.')) {
    errors.push('Username cannot start or end with a dot')
  }
  
  if (trimmedUsername.includes('..')) {
    errors.push('Username cannot contain consecutive dots')
  }
  
  if (trimmedUsername.startsWith('-') || trimmedUsername.endsWith('-')) {
    errors.push('Username cannot start or end with a hyphen')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Checks if username is available in the database
 */
export async function checkUsernameAvailability(username: string): Promise<boolean> {
  try {
    const supabase = createClient()
    
    // Use the database function for consistency
    const { data, error } = await supabase
      .rpc('is_username_available', { check_username: username.trim() })
    
    if (error) {
      console.error('Error checking username availability:', error)
      return false
    }
    
    return data === true
  } catch (error) {
    console.error('Username availability check failed:', error)
    return false
  }
}

/**
 * Comprehensive username validation including format and availability
 */
export async function validateUsername(username: string): Promise<UsernameValidationResult> {
  // First validate format
  const formatValidation = validateUsernameFormat(username)
  
  if (!formatValidation.isValid) {
    return {
      isValid: false,
      isAvailable: false,
      errors: formatValidation.errors
    }
  }
  
  // Check availability if format is valid
  const isAvailable = await checkUsernameAvailability(username)
  
  const result: UsernameValidationResult = {
    isValid: formatValidation.isValid && isAvailable,
    isAvailable,
    errors: []
  }
  
  if (!isAvailable) {
    result.errors.push('This username is already taken')
  }
  
  return result
}

/**
 * Generates smart username suggestions based on first and last name
 */
export async function suggestUsername(
  firstName: string, 
  lastName: string
): Promise<string[]> {
  try {
    const supabase = createClient()
    
    // Use the database function for the primary suggestion
    const { data: primarySuggestion, error } = await supabase
      .rpc('suggest_username', { 
        first_name_param: firstName.trim(), 
        last_name_param: lastName.trim() 
      })
    
    if (error) {
      console.error('Error getting username suggestion:', error)
      return generateFallbackSuggestions(firstName, lastName)
    }
    
    // Generate additional suggestions
    const suggestions = [primarySuggestion]
    const additionalSuggestions = await generateAdditionalSuggestions(firstName, lastName)
    
    // Filter out duplicates and ensure all suggestions are available
    const uniqueSuggestions = Array.from(new Set([...suggestions, ...additionalSuggestions]))
    
    // Verify availability for all suggestions
    const availableSuggestions: string[] = []
    for (const suggestion of uniqueSuggestions) {
      if (suggestion && await checkUsernameAvailability(suggestion)) {
        availableSuggestions.push(suggestion)
      }
      
      // Limit to 5 suggestions for better UX
      if (availableSuggestions.length >= 5) break
    }
    
    return availableSuggestions
    
  } catch (error) {
    console.error('Username suggestion failed:', error)
    return generateFallbackSuggestions(firstName, lastName)
  }
}

/**
 * Generates additional username variations
 */
async function generateAdditionalSuggestions(
  firstName: string, 
  lastName: string
): Promise<string[]> {
  const cleanFirstName = firstName.trim().toLowerCase().replace(/[^a-zA-Z0-9]/g, '')
  const cleanLastName = lastName.trim().toLowerCase().replace(/[^a-zA-Z0-9]/g, '')
  
  const suggestions = [
    // First initial + last name
    `${cleanFirstName.charAt(0)}${cleanLastName}`,
    
    // First name + last initial
    `${cleanFirstName}${cleanLastName.charAt(0)}`,
    
    // First name + underscore + last name
    `${cleanFirstName}_${cleanLastName}`,
    
    // First name + hyphen + last name  
    `${cleanFirstName}-${cleanLastName}`,
    
    // Last name + first name
    `${cleanLastName}${cleanFirstName}`,
    
    // First name only (if long enough)
    cleanFirstName.length >= 3 ? cleanFirstName : null,
    
    // Last name only (if long enough)
    cleanLastName.length >= 3 ? cleanLastName : null
  ].filter(Boolean) as string[]
  
  return suggestions
}

/**
 * Fallback suggestions when database is unavailable
 */
function generateFallbackSuggestions(firstName: string, lastName: string): string[] {
  const cleanFirstName = firstName.trim().toLowerCase().replace(/[^a-zA-Z0-9]/g, '')
  const cleanLastName = lastName.trim().toLowerCase().replace(/[^a-zA-Z0-9]/g, '')
  
  return [
    `${cleanFirstName}.${cleanLastName}`,
    `${cleanFirstName}_${cleanLastName}`,
    `${cleanFirstName}${cleanLastName}`,
    `${cleanFirstName}-${cleanLastName}`,
    `${cleanFirstName.charAt(0)}${cleanLastName}`
  ].filter(suggestion => suggestion.length >= USERNAME_CONSTRAINTS.MIN_LENGTH)
}

/**
 * Validates and suggests username in a single call (optimized for forms)
 */
export async function validateAndSuggestUsername(
  username: string,
  firstName?: string,
  lastName?: string
): Promise<UsernameValidationResult> {
  const validation = await validateUsername(username)
  
  // If username is not available and we have name info, provide suggestions
  if (!validation.isAvailable && firstName && lastName) {
    validation.suggestions = await suggestUsername(firstName, lastName)
  }
  
  return validation
}

/**
 * Debounced username validation for real-time feedback
 */
export function createDebouncedUsernameValidator(delay: number = 300) {
  let timeoutId: NodeJS.Timeout | null = null
  
  return async (
    username: string,
    callback: (result: UsernameValidationResult) => void
  ) => {
    // Clear previous timeout
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    // Set new timeout
    timeoutId = setTimeout(async () => {
      if (username.trim()) {
        const result = await validateUsername(username)
        callback(result)
      }
    }, delay)
  }
}
