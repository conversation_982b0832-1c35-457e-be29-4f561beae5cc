import { createClient } from '@/lib/supabase'
import { 
  EnhancedUserProfile,
  EnhancedUserProfileUpdate,
  UserPreferencesRecord,
  NotificationPreferences,
  PrivacySettings,
  ProfileServiceResponse,
  DEFAULT_NOTIFICATION_PREFERENCES,
  DEFAULT_PRIVACY_SETTINGS,
  DEFAULT_FEATURES_ENABLED
} from '@/lib/types/profile'
import { detectOrganizationByEmail } from './domain-detection'
import { 
  validateOrganizationAccess, 
  getUpgradeRequirement,
  TIER_ACCESS,
  type AccountTier 
} from './tier-gating'

/**
 * Enhanced User Profile Service
 * 
 * Manages complete user profile information including basic profile data
 * and enhanced features. Integrates with Clerk authentication and Supabase database.
 * 
 * Features:
 * - Enhanced profile CRUD operations
 * - Profile completeness calculation
 * - Organization association logic
 * - Account tier handling
 * - Activity tracking integration
 */

/**
 * Gets enhanced user profile by user ID
 */
export async function getEnhancedUserProfile(userId: string): Promise<EnhancedUserProfile | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      console.error('Error fetching enhanced user profile:', error)
      return null
    }
    
    return transformDatabaseToEnhancedProfile(data)
    
  } catch (error) {
    console.error('Get enhanced user profile failed:', error)
    return null
  }
}

/**
 * Creates new enhanced user profile with optional organization detection
 */
export async function createEnhancedUserProfile(
  input: {
    userId: string
    firstName: string
    lastName: string
    username: string
    displayName?: string
    timezone?: string
    theme?: 'light' | 'dark' | 'system'
    processContext?: 'recruitment' | 'bench_sales' | 'both'
    email?: string // For organization detection
  }
): Promise<ProfileServiceResponse> {
  try {
    const supabase = createClient()
    
    // Detect organization if email is provided
    let orgId: string | undefined
    let isOrgMember = false
    
    if (input.email) {
      const orgDetection = await detectOrganizationByEmail(input.email)
      if (orgDetection.found && orgDetection.organization?.autoJoinEnabled) {
        orgId = orgDetection.organization.id
        isOrgMember = false // Domain-based association, not formal membership
      }
    }
    
    const profileData = {
      user_id: input.userId,
      org_id: orgId,
      is_org_member: isOrgMember,
      first_name: input.firstName.trim(),
      last_name: input.lastName.trim(),
      username: input.username.trim(),
      display_name: input.displayName?.trim() || null,
      timezone: input.timezone || 'America/Chicago',
      theme: input.theme || 'system',
      process_context: input.processContext || 'both',
      account_tier: 'free',
      subscription_status: 'active',
      features_enabled: DEFAULT_FEATURES_ENABLED,
      
      // Enhanced profile fields with defaults
      profile_picture_url: null,
      bio: null,
      job_title: null,
      phone: null,
      linkedin_url: null,
      website_url: null,
      location: null,
      notification_preferences: DEFAULT_NOTIFICATION_PREFERENCES,
      privacy_settings: DEFAULT_PRIVACY_SETTINGS
      // last_active and profile_completeness will be set by database triggers
    }
    
    const { data, error } = await supabase
      .from('user_preferences')
      .insert(profileData)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating enhanced user profile:', error)
      
      // Provide specific error messages for common issues
      if (error.code === '23505' && error.message.includes('username')) {
        return {
          success: false,
          error: 'Username is already taken'
        }
      }
      
      return {
        success: false,
        error: 'Failed to create user profile'
      }
    }
    
    return {
      success: true,
      data: transformDatabaseToEnhancedProfile(data),
      message: 'Profile created successfully'
    }
    
  } catch (error) {
    console.error('Create enhanced user profile failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Updates enhanced user profile
 */
export async function updateEnhancedUserProfile(
  userId: string,
  updates: EnhancedUserProfileUpdate
): Promise<ProfileServiceResponse> {
  try {
    console.log("updateEnhancedUserProfile: Received updates for userId:", userId, "updates:", updates)
    const supabase = createClient()

    // Check if profile exists before attempting update
    const existingProfile = await getEnhancedUserProfile(userId)
    if (!existingProfile) {
      console.error("updateEnhancedUserProfile: Profile not found for userId:", userId)
      return {
        success: false,
        error: "User profile not found. Please ensure the user has signed up and their profile is initialized."
      }
    }
    
    // Build update object with proper column mapping
    const updateData: Partial<UserPreferencesRecord> = {}
    
    // Basic profile fields
    if (updates.firstName !== undefined) updateData.first_name = updates.firstName.trim()
    if (updates.lastName !== undefined) updateData.last_name = updates.lastName.trim()
    if (updates.username !== undefined) updateData.username = updates.username.trim()
    if (updates.displayName !== undefined) updateData.display_name = updates.displayName?.trim() || null
    
    // Preferences
    if (updates.timezone !== undefined) updateData.timezone = updates.timezone
    if (updates.theme !== undefined) updateData.theme = updates.theme
    if (updates.processContext !== undefined) updateData.process_context = updates.processContext
    
    // Enhanced profile fields
    if (updates.profilePictureUrl !== undefined) updateData.profile_picture_url = updates.profilePictureUrl
    if (updates.bio !== undefined) updateData.bio = updates.bio?.trim() || null
    if (updates.jobTitle !== undefined) updateData.job_title = updates.jobTitle?.trim() || null
    if (updates.phone !== undefined) updateData.phone = updates.phone?.trim() || null
    if (updates.linkedinUrl !== undefined) updateData.linkedin_url = updates.linkedinUrl?.trim() || null
    if (updates.websiteUrl !== undefined) updateData.website_url = updates.websiteUrl?.trim() || null
    if (updates.location !== undefined) updateData.location = updates.location?.trim() || null
    
    // Handle nested objects carefully
    if (updates.notificationPreferences !== undefined) {
      // Get current preferences and merge with updates
      const currentNotifications = existingProfile.notificationPreferences || DEFAULT_NOTIFICATION_PREFERENCES
      updateData.notification_preferences = {
        ...currentNotifications,
        ...updates.notificationPreferences
      }
    }
    
    if (updates.privacySettings !== undefined) {
      // Get current settings and merge with updates
      const currentPrivacy = existingProfile.privacySettings || DEFAULT_PRIVACY_SETTINGS
      updateData.privacy_settings = {
        ...currentPrivacy,
        ...updates.privacySettings
      }
    }
    
    console.log("updateEnhancedUserProfile: Constructed updateData for Supabase:", updateData)
    const { data, error } = await supabase
      .from('user_preferences')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating enhanced user profile:', error)
      
      if (error.code === '23505' && error.message.includes('username')) {
        return {
          success: false,
          error: 'Username is already taken'
        }
      }
      
      return {
        success: false,
        error: `Failed to update profile: ${error.message}`
      }
    }
    
    return {
      success: true,
      data: transformDatabaseToEnhancedProfile(data),
      message: 'Profile updated successfully'
    }
    
  } catch (error) {
    console.error('Update enhanced user profile failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Gets enhanced user profile with organization information
 */
export async function getEnhancedUserProfileWithOrg(userId: string): Promise<(EnhancedUserProfile & { organization?: any }) | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_preferences')
      .select(`
        *,
        organizations (
          id,
          name,
          slug,
          type
        )
      `)
      .eq('user_id', userId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      console.error('Error fetching enhanced user profile with organization:', error)
      return null
    }
    
    const profile = transformDatabaseToEnhancedProfile(data)
    
    return {
      ...profile,
      organization: data.organizations ? {
        id: data.organizations.id,
        name: data.organizations.name,
        slug: data.organizations.slug,
        type: data.organizations.type
      } : undefined
    }
    
  } catch (error) {
    console.error('Get enhanced user profile with organization failed:', error)
    return null
  }
}

/**
 * Calculates profile completeness based on filled fields
 */
export async function calculateProfileCompleteness(userId: string): Promise<number> {
  try {
    const profile = await getEnhancedUserProfile(userId)
    if (!profile) return 0
    
    const requiredFields = ['firstName', 'lastName', 'username'] as const
    const optionalFields = ['bio', 'jobTitle', 'phone', 'linkedinUrl', 'websiteUrl', 'location', 'profilePictureUrl'] as const
    
    let filledFields = 0
    const totalFields = requiredFields.length + optionalFields.length
    
    // Count required fields
    for (const field of requiredFields) {
      if (profile[field] && profile[field].trim().length > 0) {
        filledFields++
      }
    }
    
    // Count optional fields
    for (const field of optionalFields) {
      if (profile[field] && profile[field]!.trim().length > 0) {
        filledFields++
      }
    }
    
    return Math.round((filledFields / totalFields) * 100)
    
  } catch (error) {
    console.error('Calculate profile completeness failed:', error)
    return 0
  }
}

/**
 * Checks if user has completed their basic profile setup
 */
export async function hasUserCompletedProfile(userId: string): Promise<boolean> {
  try {
    const profile = await getEnhancedUserProfile(userId)
    
    if (!profile) {
      return false
    }
    
    // Check if required fields are completed
    return !!(
      profile.firstName &&
      profile.lastName &&
      profile.username &&
      profile.timezone &&
      profile.theme &&
      profile.processContext
    )
    
  } catch (error) {
    console.error('Error checking profile completion:', error)
    return false
  }
}

/**
 * Transforms database record to enhanced profile interface
 */
function transformDatabaseToEnhancedProfile(data: any): EnhancedUserProfile {
  return {
    id: data.id,
    userId: data.user_id,
    orgId: data.org_id,
    isOrgMember: data.is_org_member || false,
    
    // Basic profile
    firstName: data.first_name || '',
    lastName: data.last_name || '',
    username: data.username || '',
    displayName: data.display_name,
    
    // Preferences
    timezone: data.timezone || 'America/Chicago',
    theme: data.theme || 'system',
    processContext: data.process_context || 'both',
    
    // Account
    accountTier: data.account_tier || 'free',
    subscriptionStatus: data.subscription_status || 'active',
    featuresEnabled: data.features_enabled || {},
    
    // Enhanced profile
    profilePictureUrl: data.profile_picture_url,
    bio: data.bio,
    jobTitle: data.job_title,
    phone: data.phone,
    linkedinUrl: data.linkedin_url,
    websiteUrl: data.website_url,
    location: data.location,
    notificationPreferences: data.notification_preferences || DEFAULT_NOTIFICATION_PREFERENCES,
    privacySettings: data.privacy_settings || DEFAULT_PRIVACY_SETTINGS,
    lastActive: data.last_active,
    profileCompleteness: data.profile_completeness || 0,
    
    // Audit
    createdAt: data.created_at,
    updatedAt: data.updated_at
  }
}

/**
 * Associates user with organization (updates org_id and membership status)
 */
export async function associateUserWithOrganization(
  userId: string, 
  orgId: string | null, 
  isOrgMember: boolean
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('user_preferences')
      .update({
        org_id: orgId,
        is_org_member: isOrgMember,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error associating user with organization:', error)
      return { success: false, error: error.message }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Associate user with organization failed:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

// All functions now use clear Enhanced Profile naming
// No more confusing aliases - function names match their actual purpose
