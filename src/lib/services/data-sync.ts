/**
 * Data Sync Service
 * 
 * Centralized data synchronization management for TalentHUB.
 * Implements hybrid sync strategy with immediate critical data sync
 * and batched preference updates every 30 seconds.
 * 
 * Features:
 * - Hybrid sync strategy (immediate vs batched)
 * - Retry logic with exponential backoff
 * - Conflict resolution algorithms
 * - Sync queue management
 * - Real-time status updates
 * - Cross-browser compatibility
 */

import { createClient } from '@/lib/supabase'
import type { UserPreferences } from './user-preferences'

// Types for sync operations
export type SyncPriority = 'immediate' | 'batched' | 'background'
export type SyncStatus = 'idle' | 'syncing' | 'success' | 'error' | 'retrying'
export type ConflictResolution = 'local_wins' | 'remote_wins' | 'merge' | 'user_choice'

export interface SyncOperation {
  id: string
  type: 'create' | 'update' | 'delete'
  table: string
  data: any
  priority: SyncPriority
  userId: string
  orgId?: string
  timestamp: number
  retryCount: number
  maxRetries: number
  conflictResolution: ConflictResolution
}

export interface SyncQueueItem extends SyncOperation {
  status: SyncStatus
  error?: string
  lastAttempt?: number
  nextRetry?: number
}

export interface SyncProgress {
  total: number
  completed: number
  failed: number
  pending: number
  percentage: number
}

export interface SyncStatusUpdate {
  queueSize: number
  activeOperations: number
  lastSyncTime: number
  progress: SyncProgress
  errors: Array<{
    operation: SyncOperation
    error: string
    timestamp: number
  }>
}

// Data categories for sync prioritization
export const SYNC_CATEGORIES = {
  CRITICAL: {
    priority: 'immediate' as SyncPriority,
    tables: ['organizations', 'user_process_roles', 'subscription_events'],
    maxRetries: 5,
    retryDelay: 1000 // 1 second
  },
  IMPORTANT: {
    priority: 'batched' as SyncPriority,
    tables: ['user_preferences', 'org_settings', 'usage_tracking'],
    maxRetries: 3,
    retryDelay: 5000 // 5 seconds
  },
  BACKGROUND: {
    priority: 'background' as SyncPriority,
    tables: ['audit_logs', 'analytics_events', 'search_history'],
    maxRetries: 2,
    retryDelay: 30000 // 30 seconds
  }
} as const

// Sync configuration
const BATCH_INTERVAL = 30000 // 30 seconds
const MAX_BATCH_SIZE = 10
const SYNC_TIMEOUT = 10000 // 10 seconds
const MAX_QUEUE_SIZE = 100

/**
 * DataSyncService - Core synchronization engine
 */
class DataSyncService {
  private syncQueue: Map<string, SyncQueueItem> = new Map()
  private batchQueue: SyncQueueItem[] = []
  private activeOperations: Set<string> = new Set()
  private subscribers: Set<(status: SyncStatusUpdate) => void> = new Set()
  private batchTimeout: NodeJS.Timeout | null = null
  private lastSyncTime = 0
  private isOnline = true

  constructor() {
    this.initializeService()
  }

  /**
   * Initialize the sync service
   */
  private initializeService() {
    // Monitor online status
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.isOnline = true
        this.processPendingOperations()
      })
      
      window.addEventListener('offline', () => {
        this.isOnline = false
      })
      
      this.isOnline = navigator.onLine
    }

    // Start batch processing interval
    this.startBatchProcessing()
  }

  /**
   * Add operation to sync queue
   */
  public async queueOperation(operation: Partial<SyncOperation>): Promise<string> {
    const operationId = this.generateOperationId()
    
    const category = this.getSyncCategory(operation.table || '')
    
    const queueItem: SyncQueueItem = {
      id: operationId,
      type: operation.type || 'update',
      table: operation.table || '',
      data: operation.data || {},
      priority: operation.priority || category.priority,
      userId: operation.userId || '',
      orgId: operation.orgId,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: category.maxRetries,
      conflictResolution: operation.conflictResolution || 'remote_wins',
      status: 'idle'
    }

    // Check queue size limit
    if (this.syncQueue.size >= MAX_QUEUE_SIZE) {
      throw new Error('Sync queue is full. Please wait for pending operations to complete.')
    }

    this.syncQueue.set(operationId, queueItem)

    // Process immediately if critical, otherwise add to batch
    if (queueItem.priority === 'immediate') {
      await this.processOperation(queueItem)
    } else {
      this.addToBatch(queueItem)
    }

    this.notifySubscribers()
    return operationId
  }

  /**
   * Process individual operation
   */
  private async processOperation(operation: SyncQueueItem): Promise<boolean> {
    if (!this.isOnline) {
      operation.status = 'error'
      operation.error = 'Offline - operation queued for retry'
      return false
    }

    this.activeOperations.add(operation.id)
    operation.status = 'syncing'
    operation.lastAttempt = Date.now()

    try {
      const supabase = createClient()
      let result

      switch (operation.type) {
        case 'create':
          result = await supabase
            .from(operation.table)
            .insert(operation.data)
            .select()
          break
          
        case 'update':
          result = await supabase
            .from(operation.table)
            .update(operation.data)
            .eq('id', operation.data.id)
            .select()
          break
          
        case 'delete':
          result = await supabase
            .from(operation.table)
            .delete()
            .eq('id', operation.data.id)
          break
      }

      if (result?.error) {
        throw new Error(result.error.message)
      }

      // Handle potential conflicts
      if (result?.data && result.data.length === 0 && operation.type === 'update') {
        await this.handleConflict(operation, 'record_not_found')
      }

      operation.status = 'success'
      this.syncQueue.delete(operation.id)
      this.lastSyncTime = Date.now()
      
      return true

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown sync error'
      operation.error = errorMessage
      operation.retryCount++

      if (operation.retryCount >= operation.maxRetries) {
        operation.status = 'error'
        console.error(`Sync operation ${operation.id} failed permanently:`, errorMessage)
      } else {
        operation.status = 'retrying'
        const category = this.getSyncCategory(operation.table)
        operation.nextRetry = Date.now() + (category.retryDelay * Math.pow(2, operation.retryCount))
        
        // Schedule retry
        setTimeout(() => {
          if (this.syncQueue.has(operation.id)) {
            this.processOperation(operation)
          }
        }, category.retryDelay * Math.pow(2, operation.retryCount))
      }

      return false

    } finally {
      this.activeOperations.delete(operation.id)
      this.notifySubscribers()
    }
  }

  /**
   * Handle data conflicts
   */
  private async handleConflict(operation: SyncQueueItem, conflictType: string): Promise<void> {
    switch (operation.conflictResolution) {
      case 'local_wins':
        // Force update with local data
        await this.forceUpdate(operation)
        break
        
      case 'remote_wins':
        // Skip local changes, fetch remote data
        operation.status = 'success'
        break
        
      case 'merge':
        // Attempt to merge changes (implementation specific)
        await this.mergeChanges(operation)
        break
        
      case 'user_choice':
        // Emit conflict event for user resolution
        this.emitConflictEvent(operation, conflictType)
        break
    }
  }

  /**
   * Add operation to batch queue
   */
  private addToBatch(operation: SyncQueueItem): void {
    this.batchQueue.push(operation)
    
    if (this.batchQueue.length >= MAX_BATCH_SIZE) {
      this.processBatch()
    }
  }

  /**
   * Start batch processing interval
   */
  private startBatchProcessing(): void {
    if (this.batchTimeout) {
      clearInterval(this.batchTimeout)
    }

    this.batchTimeout = setInterval(() => {
      if (this.batchQueue.length > 0) {
        this.processBatch()
      }
    }, BATCH_INTERVAL)
  }

  /**
   * Process batch of operations
   */
  private async processBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return

    const batch = this.batchQueue.splice(0, MAX_BATCH_SIZE)
    
    // Process batch operations in parallel
    const promises = batch.map(operation => this.processOperation(operation))
    
    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.error('Batch processing error:', error)
    }
  }

  /**
   * Process pending operations (called when coming back online)
   */
  private async processPendingOperations(): Promise<void> {
    const pendingOps = Array.from(this.syncQueue.values())
      .filter(op => op.status === 'idle' || op.status === 'error')
      .sort((a, b) => {
        // Process critical operations first
        const priorityOrder = { immediate: 0, batched: 1, background: 2 }
        return priorityOrder[a.priority] - priorityOrder[b.priority]
      })

    for (const operation of pendingOps) {
      if (operation.priority === 'immediate') {
        await this.processOperation(operation)
      } else {
        this.addToBatch(operation)
      }
    }
  }

  /**
   * Subscribe to sync status updates
   */
  public subscribe(callback: (status: SyncStatusUpdate) => void): () => void {
    this.subscribers.add(callback)
    
    // Send initial status
    callback(this.getCurrentStatus())
    
    return () => {
      this.subscribers.delete(callback)
    }
  }

  /**
   * Get current sync status
   */
  public getCurrentStatus(): SyncStatusUpdate {
    const operations = Array.from(this.syncQueue.values())
    const total = operations.length
    const completed = operations.filter(op => op.status === 'success').length
    const failed = operations.filter(op => op.status === 'error').length
    const pending = operations.filter(op => op.status === 'idle' || op.status === 'retrying').length

    return {
      queueSize: this.syncQueue.size,
      activeOperations: this.activeOperations.size,
      lastSyncTime: this.lastSyncTime,
      progress: {
        total,
        completed,
        failed,
        pending,
        percentage: total > 0 ? Math.round((completed / total) * 100) : 100
      },
      errors: operations
        .filter(op => op.status === 'error')
        .map(op => ({
          operation: op,
          error: op.error || 'Unknown error',
          timestamp: op.lastAttempt || op.timestamp
        }))
    }
  }

  /**
   * Retry failed operations
   */
  public async retryFailedOperations(): Promise<void> {
    const failedOps = Array.from(this.syncQueue.values())
      .filter(op => op.status === 'error')
    
    for (const operation of failedOps) {
      operation.retryCount = 0
      operation.status = 'idle'
      operation.error = undefined
      
      if (operation.priority === 'immediate') {
        await this.processOperation(operation)
      } else {
        this.addToBatch(operation)
      }
    }
  }

  /**
   * Clear completed operations
   */
  public clearCompleted(): void {
    const completedOps = Array.from(this.syncQueue.entries())
      .filter(([, op]) => op.status === 'success')
    
    completedOps.forEach(([id]) => {
      this.syncQueue.delete(id)
    })
    
    this.notifySubscribers()
  }

  /**
   * Get sync category for table
   */
  private getSyncCategory(table: string) {
    for (const [, category] of Object.entries(SYNC_CATEGORIES)) {
      if (category.tables.includes(table)) {
        return category
      }
    }
    return SYNC_CATEGORIES.BACKGROUND
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Notify subscribers of status changes
   */
  private notifySubscribers(): void {
    const status = this.getCurrentStatus()
    this.subscribers.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('Error notifying sync subscriber:', error)
      }
    })
  }

  /**
   * Force update for conflict resolution
   */
  private async forceUpdate(operation: SyncQueueItem): Promise<void> {
    // Implementation for force update
    const supabase = createClient()
    await supabase
      .from(operation.table)
      .upsert(operation.data, { onConflict: 'id' })
  }

  /**
   * Merge changes for conflict resolution
   */
  private async mergeChanges(operation: SyncQueueItem): Promise<void> {
    // Implementation for merge strategy
    // This is table-specific and would need custom logic
    console.log('Merge conflict resolution not implemented for:', operation.table)
  }

  /**
   * Emit conflict event for user resolution
   */
  private emitConflictEvent(operation: SyncQueueItem, conflictType: string): void {
    // Emit custom event for UI to handle
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('sync-conflict', {
        detail: { operation, conflictType }
      }))
    }
  }

  /**
   * Destroy the service
   */
  public destroy(): void {
    if (this.batchTimeout) {
      clearInterval(this.batchTimeout)
    }
    this.syncQueue.clear()
    this.batchQueue = []
    this.subscribers.clear()
    this.activeOperations.clear()
  }
}

// Singleton instance
let syncServiceInstance: DataSyncService | null = null

/**
 * Get the singleton sync service instance
 */
export function getSyncService(): DataSyncService {
  if (!syncServiceInstance) {
    syncServiceInstance = new DataSyncService()
  }
  return syncServiceInstance
}

/**
 * High-level sync functions for common operations
 */

/**
 * Sync user preferences with hybrid strategy
 */
export async function syncUserPreferences(
  userId: string,
  preferences: Partial<UserPreferences>,
  priority: SyncPriority = 'batched'
): Promise<string> {
  const syncService = getSyncService()
  
  return await syncService.queueOperation({
    type: 'update',
    table: 'user_preferences',
    data: { ...preferences, updated_at: new Date().toISOString() },
    priority,
    userId,
    conflictResolution: 'merge'
  })
}

/**
 * Sync critical organization data
 */
export async function syncOrganizationData(
  userId: string,
  orgId: string,
  data: any
): Promise<string> {
  const syncService = getSyncService()
  
  return await syncService.queueOperation({
    type: 'update',
    table: 'organizations',
    data: { ...data, updated_at: new Date().toISOString() },
    priority: 'immediate',
    userId,
    orgId,
    conflictResolution: 'remote_wins'
  })
}

/**
 * Track usage events (background sync)
 */
export async function trackUsageEvent(
  userId: string,
  eventType: string,
  metadata: any
): Promise<string> {
  const syncService = getSyncService()
  
  return await syncService.queueOperation({
    type: 'create',
    table: 'usage_tracking',
    data: {
      user_id: userId,
      event_type: eventType,
      metadata,
      timestamp: new Date().toISOString()
    },
    priority: 'background',
    userId,
    conflictResolution: 'local_wins'
  })
}

export default getSyncService