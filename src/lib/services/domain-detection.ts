import { createClient } from '@/lib/supabase'

/**
 * Domain Detection Service
 * 
 * Handles organization detection based on email domains and provides
 * utilities for managing domain-based organization associations.
 * 
 * Features:
 * - Email domain extraction and validation
 * - Organization detection by domain
 * - Domain verification workflows
 * - Auto-join organization logic
 * - Batch domain operations for admins
 */

// Types for domain detection
export interface OrganizationDetectionResult {
  found: boolean
  organization?: {
    id: string
    name: string
    autoJoinEnabled: boolean
  }
  domain: string
}

export interface DomainValidation {
  isValid: boolean
  domain?: string
  errors: string[]
}

// Common domain patterns and validation
const DOMAIN_PATTERN = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/
const EMAIL_PATTERN = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// Common public email domains that should not auto-associate with organizations
const PUBLIC_EMAIL_DOMAINS = new Set([
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
  'icloud.com', 'protonmail.com', 'zoho.com', 'yandex.com', 'mail.com',
  'gmx.com', 'fastmail.com', 'tutanota.com', 'cock.li', 'guerrillamail.com'
])

/**
 * Extracts domain from email address with validation
 */
export function extractDomainFromEmail(email: string): DomainValidation {
  const errors: string[] = []
  
  if (!email || typeof email !== 'string') {
    errors.push('Email address is required')
    return { isValid: false, errors }
  }
  
  const trimmedEmail = email.trim().toLowerCase()
  
  if (!EMAIL_PATTERN.test(trimmedEmail)) {
    errors.push('Invalid email address format')
    return { isValid: false, errors }
  }
  
  const domain = trimmedEmail.split('@')[1]
  
  if (!domain || !DOMAIN_PATTERN.test(domain)) {
    errors.push('Invalid email domain')
    return { isValid: false, errors }
  }
  
  if (domain.length < 4 || domain.length > 255) {
    errors.push('Email domain length is invalid')
    return { isValid: false, errors }
  }
  
  return {
    isValid: true,
    domain,
    errors: []
  }
}

/**
 * Checks if domain is a public email provider
 */
export function isPublicEmailDomain(domain: string): boolean {
  return PUBLIC_EMAIL_DOMAINS.has(domain.toLowerCase())
}

/**
 * Detects organization by email domain
 */
export async function detectOrganizationByEmail(
  email: string
): Promise<OrganizationDetectionResult> {
  // First validate and extract domain
  const domainValidation = extractDomainFromEmail(email)
  
  if (!domainValidation.isValid || !domainValidation.domain) {
    return {
      found: false,
      domain: email.split('@')[1] || ''
    }
  }
  
  const domain = domainValidation.domain
  
  // Don't attempt detection for public email domains
  if (isPublicEmailDomain(domain)) {
    return {
      found: false,
      domain
    }
  }
  
  try {
    const supabase = createClient()
    
    // Use the database function to detect organization
    const { data, error } = await supabase
      .rpc('detect_org_by_domain', { email_domain: domain })
    
    if (error) {
      console.error('Error detecting organization by domain:', error)
      return { found: false, domain }
    }
    
    if (data && data.length > 0) {
      const orgData = data[0]
      return {
        found: true,
        organization: {
          id: orgData.org_id,
          name: orgData.org_name,
          autoJoinEnabled: orgData.auto_join_enabled
        },
        domain
      }
    }
    
    return { found: false, domain }
    
  } catch (error) {
    console.error('Organization detection failed:', error)
    return { found: false, domain }
  }
}

/**
 * Validates if domain can be added to organization
 */
export async function validateDomainForOrganization(
  domain: string,
  organizationId: string
): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = []
  
  // Validate domain format
  if (!DOMAIN_PATTERN.test(domain)) {
    errors.push('Invalid domain format')
  }
  
  // Check if it's a public domain
  if (isPublicEmailDomain(domain)) {
    errors.push('Public email domains cannot be added to organizations')
  }
  
  // Check if domain is already claimed
  try {
    const supabase = createClient()
    
    const { data: existingDomain, error } = await supabase
      .from('org_domains')
      .select('org_id, domain')
      .eq('domain', domain.toLowerCase())
      .single()
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking domain availability:', error)
      errors.push('Unable to verify domain availability')
    } else if (existingDomain && existingDomain.org_id !== organizationId) {
      errors.push('This domain is already claimed by another organization')
    }
    
  } catch (error) {
    console.error('Domain validation failed:', error)
    errors.push('Unable to validate domain')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Adds a domain to an organization (admin function)
 */
export async function addDomainToOrganization(
  domain: string,
  organizationId: string,
  autoJoinEnabled: boolean = true
): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate domain first
    const validation = await validateDomainForOrganization(domain, organizationId)
    
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join(', ')
      }
    }
    
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('org_domains')
      .insert({
        org_id: organizationId,
        domain: domain.toLowerCase(),
        auto_join_enabled: autoJoinEnabled,
        is_verified: false // Requires manual verification
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error adding domain to organization:', error)
      return {
        success: false,
        error: 'Failed to add domain to organization'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Add domain operation failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Gets all domains for an organization
 */
export async function getOrganizationDomains(organizationId: string) {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('org_domains')
      .select('*')
      .eq('org_id', organizationId)
      .order('created_at', { ascending: true })
    
    if (error) {
      console.error('Error fetching organization domains:', error)
      return []
    }
    
    return data || []
    
  } catch (error) {
    console.error('Get organization domains failed:', error)
    return []
  }
}

/**
 * Updates domain settings for an organization
 */
export async function updateDomainSettings(
  domainId: string,
  settings: {
    autoJoinEnabled?: boolean
    isVerified?: boolean
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const updateData: any = {}
    if (settings.autoJoinEnabled !== undefined) {
      updateData.auto_join_enabled = settings.autoJoinEnabled
    }
    if (settings.isVerified !== undefined) {
      updateData.is_verified = settings.isVerified
    }
    
    const { error } = await supabase
      .from('org_domains')
      .update(updateData)
      .eq('id', domainId)
    
    if (error) {
      console.error('Error updating domain settings:', error)
      return {
        success: false,
        error: 'Failed to update domain settings'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Update domain settings failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Removes a domain from an organization
 */
export async function removeDomainFromOrganization(
  domainId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('org_domains')
      .delete()
      .eq('id', domainId)
    
    if (error) {
      console.error('Error removing domain from organization:', error)
      return {
        success: false,
        error: 'Failed to remove domain from organization'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Remove domain operation failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Suggests domain verification methods for organization admins
 */
export function getDomainVerificationMethods(domain: string) {
  return {
    methods: [
      {
        type: 'dns',
        name: 'DNS TXT Record',
        description: 'Add a TXT record to your domain\'s DNS settings',
        difficulty: 'medium',
        verificationCode: `talenthub-verify=${generateVerificationCode()}`
      },
      {
        type: 'email',
        name: 'Admin Email Verification',
        description: 'Send verification email to admin@' + domain,
        difficulty: 'easy',
        emailAddress: `admin@${domain}`
      },
      {
        type: 'file',
        name: 'File Upload Verification',
        description: 'Upload a verification file to your website root',
        difficulty: 'hard',
        fileName: 'talenthub-verification.txt',
        content: generateVerificationCode()
      }
    ]
  }
}

/**
 * Generates a verification code for domain ownership
 */
function generateVerificationCode(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${timestamp}${random}`
}

/**
 * Bulk domain operations for enterprise clients
 */
export async function bulkAddDomains(
  domains: string[],
  organizationId: string,
  autoJoinEnabled: boolean = true
): Promise<{
  success: boolean
  results: Array<{ domain: string; success: boolean; error?: string }>
}> {
  const results = []
  
  for (const domain of domains) {
    const result = await addDomainToOrganization(domain, organizationId, autoJoinEnabled)
    results.push({
      domain,
      success: result.success,
      error: result.error
    })
  }
  
  const successCount = results.filter(r => r.success).length
  
  return {
    success: successCount > 0,
    results
  }
}
