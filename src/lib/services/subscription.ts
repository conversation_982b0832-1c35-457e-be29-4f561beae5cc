/**
 * Subscription Service
 * 
 * Enhanced subscription management service that bridges TalentHUB's existing
 * tier system with Clerk Billing. Provides unified access to subscription
 * status, usage tracking, and billing features.
 * 
 * Features:
 * - Unified subscription status management
 * - Usage tracking and limits enforcement
 * - Billing portal integration
 * - Subscription upgrade workflows
 * - Real-time subscription sync
 */

import { createClient } from '@/lib/supabase'
import { 
  getCurrentSubscriptionStatus,
  hasFeatureAccess,
  hasPlanAccess,
  mapClerkPlanToTier,
  getPlanConfigByTier,
  isWithinUsageLimit,
  getRemainingUsage,
  CLERK_FEATURE_SLUGS,
  CLERK_PLAN_SLUGS,
  type SubscriptionStatus,
  type PlanConfig
} from '@/lib/billing/clerk-integration'
import { 
  validateOrganizationAccess,
  getUpgradeRequirement,
  type AccountTier,
  type TierValidationResult
} from '@/lib/services/tier-gating'

// Enhanced subscription types
export interface EnhancedSubscriptionStatus extends SubscriptionStatus {
  usageStats: UsageStats
  upgradeRequirement?: UpgradeRequirement
  billingPortalUrl?: string
}

export interface UsageStats {
  records: { current: number; limit: number | null; percentage: number }
  apiCalls: { current: number; limit: number | null; percentage: number }
  storage: { current: number; limit: number | null; percentage: number }
  organizationMembers?: { current: number; limit: number | null; percentage: number }
}

export interface UpgradeRequirement {
  fromTier: AccountTier
  toTier: AccountTier
  reason: string
  benefits: string[]
  pricing: {
    monthly: number
    annually: number
    savings: number
  }
}

export interface SubscriptionAction {
  type: 'upgrade' | 'downgrade' | 'cancel' | 'reactivate'
  fromPlan?: string
  toPlan: string
  effectiveDate: Date
  proration?: number
}

/**
 * Gets comprehensive subscription status including usage and upgrade recommendations
 */
export async function getSubscriptionStatus(userId: string): Promise<EnhancedSubscriptionStatus> {
  try {
    // Get current Clerk subscription status
    const clerkStatus = await getCurrentSubscriptionStatus()
    
    // Get usage statistics from Supabase
    const usageStats = await getUserUsageStats(userId)
    
    // Determine upgrade requirement if needed
    const upgradeRequirement = getUpgradeRecommendation(clerkStatus.talentHubTier, usageStats)
    
    return {
      ...clerkStatus,
      usageStats,
      upgradeRequirement
    }
    
  } catch (error) {
    console.error('Error getting subscription status:', error)
    
    // Return safe defaults
    return {
      isActive: false,
      talentHubTier: 'free',
      features: [],
      limits: { records: 50, apiCalls: 100, storage: 1 },
      usageStats: {
        records: { current: 0, limit: 50, percentage: 0 },
        apiCalls: { current: 0, limit: 100, percentage: 0 },
        storage: { current: 0, limit: 1, percentage: 0 }
      }
    }
  }
}

/**
 * Gets user usage statistics from Supabase
 */
export async function getUserUsageStats(userId: string): Promise<UsageStats> {
  try {
    const supabase = createClient()
    
    // Get current usage from various tables
    // This would be implemented based on your actual usage tracking tables
    
    // For now, return mock data - replace with actual queries
    const mockUsage = {
      records: { current: 25, limit: 50, percentage: 50 },
      apiCalls: { current: 150, limit: 100, percentage: 150 }, // Over limit
      storage: { current: 0.5, limit: 1, percentage: 50 }
    }
    
    return mockUsage
    
  } catch (error) {
    console.error('Error getting usage stats:', error)
    return {
      records: { current: 0, limit: 50, percentage: 0 },
      apiCalls: { current: 0, limit: 100, percentage: 0 },
      storage: { current: 0, limit: 1, percentage: 0 }
    }
  }
}

/**
 * Determines if user should upgrade based on usage patterns
 */
export function getUpgradeRecommendation(
  currentTier: AccountTier,
  usageStats: UsageStats
): UpgradeRequirement | undefined {
  // Check if user is hitting limits
  const isOverUsage = Object.values(usageStats).some(stat => 
    stat.limit !== null && stat.percentage >= 80
  )
  
  if (!isOverUsage && currentTier !== 'free') {
    return undefined
  }
  
  // Determine recommended tier
  let recommendedTier: AccountTier
  let reason: string
  
  if (currentTier === 'free') {
    recommendedTier = 'pro'
    reason = 'Unlock unlimited records and advanced features'
  } else if (currentTier === 'pro') {
    recommendedTier = 'team'
    reason = 'Access team collaboration and organization features'
  } else {
    return undefined // Already on highest relevant tier
  }
  
  const planConfig = getPlanConfigByTier(recommendedTier)
  if (!planConfig) return undefined
  
  return {
    fromTier: currentTier,
    toTier: recommendedTier,
    reason,
    benefits: getBenefitsForTier(recommendedTier),
    pricing: {
      monthly: planConfig.monthlyPrice,
      annually: planConfig.annualPrice,
      savings: planConfig.monthlyPrice - planConfig.annualPrice
    }
  }
}

/**
 * Gets benefits list for a specific tier
 */
function getBenefitsForTier(tier: AccountTier): string[] {
  switch (tier) {
    case 'pro':
      return [
        'Unlimited records and candidates',
        'Advanced search and filtering',
        'API access for integrations',
        'Analytics dashboard',
        'Data export capabilities'
      ]
    case 'team':
      return [
        'All Pro features included',
        'Team collaboration tools',
        'Organization management',
        'Up to 10 team members',
        'Domain-based auto-join'
      ]
    case 'enterprise':
      return [
        'All Team features included',
        'Unlimited team members',
        'Multiple organizations',
        'Custom integrations',
        'Priority support'
      ]
    default:
      return []
  }
}

/**
 * Checks if user can perform an action based on subscription
 */
export async function canPerformAction(
  action: 'create_record' | 'api_call' | 'export_data' | 'invite_member',
  currentUsage?: number
): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: boolean }> {
  try {
    const status = await getCurrentSubscriptionStatus()
    
    switch (action) {
      case 'create_record':
        if (await hasFeatureAccess(CLERK_FEATURE_SLUGS.UNLIMITED_RECORDS)) {
          return { allowed: true }
        }
        
        const recordLimit = status.limits.records || 50
        if (currentUsage && currentUsage >= recordLimit) {
          return {
            allowed: false,
            reason: `You've reached your limit of ${recordLimit} records`,
            upgradeRequired: true
          }
        }
        return { allowed: true }
        
      case 'api_call':
        if (!(await hasFeatureAccess(CLERK_FEATURE_SLUGS.API_ACCESS))) {
          return {
            allowed: false,
            reason: 'API access requires Pro plan or higher',
            upgradeRequired: true
          }
        }
        return { allowed: true }
        
      case 'export_data':
        if (!(await hasFeatureAccess(CLERK_FEATURE_SLUGS.EXPORT_DATA))) {
          return {
            allowed: false,
            reason: 'Data export requires Pro plan or higher',
            upgradeRequired: true
          }
        }
        return { allowed: true }
        
      case 'invite_member':
        if (!(await hasFeatureAccess(CLERK_FEATURE_SLUGS.MEMBER_MANAGEMENT))) {
          return {
            allowed: false,
            reason: 'Member invitations require Team plan or higher',
            upgradeRequired: true
          }
        }
        return { allowed: true }
        
      default:
        return { allowed: false, reason: 'Unknown action' }
    }
    
  } catch (error) {
    console.error('Error checking action permission:', error)
    return { allowed: false, reason: 'Unable to verify permissions' }
  }
}

/**
 * Records usage event for tracking and billing
 */
export async function recordUsage(
  userId: string,
  usageType: 'record_creation' | 'api_call' | 'storage_usage' | 'export',
  amount: number = 1,
  metadata?: Record<string, any>
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    // Record usage in tracking table
    const { error } = await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        usage_type: usageType,
        amount,
        metadata,
        recorded_at: new Date().toISOString()
      })
    
    if (error) {
      console.error('Error recording usage:', error)
      return { success: false, error: 'Failed to record usage' }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Record usage failed:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

/**
 * Syncs Clerk subscription status with Supabase user preferences
 */
export async function syncSubscriptionStatus(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const clerkStatus = await getCurrentSubscriptionStatus()
    const supabase = createClient()
    
    // Update user preferences with current subscription tier
    const { error } = await supabase
      .from('user_preferences')
      .update({
        account_tier: clerkStatus.talentHubTier,
        subscription_status: clerkStatus.isActive ? 'active' : 'inactive',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error syncing subscription status:', error)
      return { success: false, error: 'Failed to sync subscription status' }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Sync subscription status failed:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

/**
 * Gets billing portal URL for subscription management
 */
export async function getBillingPortalUrl(): Promise<string | null> {
  try {
    // This would integrate with Clerk's billing portal
    // For now, return a placeholder URL
    return '/billing/manage'
    
  } catch (error) {
    console.error('Error getting billing portal URL:', error)
    return null
  }
}

/**
 * Validates subscription access for organization features
 */
export async function validateSubscriptionAccess(
  feature: 'join_org' | 'create_org' | 'admin_access',
  currentTier: AccountTier
): Promise<TierValidationResult & { hasSubscription: boolean }> {
  const tierValidation = validateOrganizationAccess(
    feature === 'join_org' ? 'join' : 
    feature === 'create_org' ? 'create' : 'admin',
    currentTier
  )
  
  // Check Clerk subscription status
  let hasSubscription = false
  try {
    if (feature === 'join_org' || feature === 'admin_access') {
      hasSubscription = await hasFeatureAccess(CLERK_FEATURE_SLUGS.ORGANIZATION_ACCESS)
    } else if (feature === 'create_org') {
      hasSubscription = await hasFeatureAccess(CLERK_FEATURE_SLUGS.UNLIMITED_ORGS)
    }
  } catch (error) {
    console.error('Error checking subscription access:', error)
  }
  
  return {
    ...tierValidation,
    hasSubscription
  }
}

/**
 * Gets usage statistics summary for dashboard display
 */
export async function getUsageSummary(userId: string): Promise<{
  records: { used: number; limit: number | null; percentage: number }
  apiCalls: { used: number; limit: number | null; percentage: number }
  storage: { used: number; limit: number | null; percentage: number }
  nearingLimits: boolean
}> {
  try {
    const usageStats = await getUserUsageStats(userId)
    
    const nearingLimits = Object.values(usageStats).some(stat => 
      stat.limit !== null && stat.percentage >= 80
    )
    
    return {
      records: {
        used: usageStats.records.current,
        limit: usageStats.records.limit,
        percentage: usageStats.records.percentage
      },
      apiCalls: {
        used: usageStats.apiCalls.current,
        limit: usageStats.apiCalls.limit,
        percentage: usageStats.apiCalls.percentage
      },
      storage: {
        used: usageStats.storage.current,
        limit: usageStats.storage.limit,
        percentage: usageStats.storage.percentage
      },
      nearingLimits
    }
    
  } catch (error) {
    console.error('Error getting usage summary:', error)
    return {
      records: { used: 0, limit: 50, percentage: 0 },
      apiCalls: { used: 0, limit: 100, percentage: 0 },
      storage: { used: 0, limit: 1, percentage: 0 },
      nearingLimits: false
    }
  }
}
