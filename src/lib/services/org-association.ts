import { createClient } from '@/lib/supabase'
import { detectOrganizationByEmail, type OrganizationDetectionResult } from './domain-detection'
import { getEnhancedUserProfile, associateUserWithOrganization } from './user-preferences'
import { 
  validateOrganizationAccess, 
  getUpgradeRequirement,
  type AccountTier,
  type TierValidationResult 
} from './tier-gating'

/**
 * Organization Association Service
 * 
 * Manages organization membership workflows including join requests,
 * individual to organization transitions, and admin approval processes.
 * 
 * Features:
 * - Join organization request management
 * - Individual to organization data migration
 * - Admin approval workflows
 * - Membership audit trails
 * - Auto-join based on domain verification
 */

// Types for organization association
export interface JoinRequest {
  id: string
  userId: string
  orgId: string
  email: string
  domain: string
  status: 'pending' | 'approved' | 'rejected' | 'expired'
  requestType: 'domain_detection' | 'manual_request' | 'invitation'
  message?: string
  reviewedBy?: string
  reviewedAt?: string
  adminNotes?: string
  expiresAt: string
  createdAt: string
  updatedAt: string
}

export interface JoinRequestWithOrg extends JoinRequest {
  organization: {
    id: string
    name: string
    slug: string
    type: string
  }
}

export interface OrganizationDetectionWithRequests extends OrganizationDetectionResult {
  hasPendingRequest?: boolean
  existingRequest?: JoinRequest
  tierValidation?: TierValidationResult
  upgradeRequired?: boolean
}

export interface JoinRequestResult {
  success: boolean
  requestId?: string
  error?: string
  tierValidation?: TierValidationResult
  upgradeRequired?: boolean
}

export interface MembershipTransition {
  userId: string
  fromOrgId?: string
  toOrgId: string
  preserveData: boolean
  transferData: boolean
}

/**
 * Detects organization and checks for existing join requests with tier validation
 */
export async function detectOrganizationWithRequests(
  email: string,
  userId: string,
  accountTier: AccountTier
): Promise<OrganizationDetectionWithRequests> {
  try {
    // Get basic organization detection
    const detection = await detectOrganizationByEmail(email)
    
    if (!detection.found || !detection.organization) {
      return detection
    }
    
    // Validate tier access for joining organizations
    const tierValidation = validateOrganizationAccess('join', accountTier)
    
    // Check for existing join requests
    const supabase = createClient()
    
    const { data: existingRequest, error } = await supabase
      .from('org_join_requests')
      .select('*')
      .eq('user_id', userId)
      .eq('org_id', detection.organization.id)
      .eq('status', 'pending')
      .single()
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error checking existing join requests:', error)
      return detection
    }
    
    return {
      ...detection,
      hasPendingRequest: !!existingRequest,
      existingRequest: existingRequest ? transformDatabaseToJoinRequest(existingRequest) : undefined,
      tierValidation,
      upgradeRequired: tierValidation.requiresUpgrade
    }
    
  } catch (error) {
    console.error('Organization detection with requests failed:', error)
    return { found: false, domain: email.split('@')[1] || '' }
  }
}

/**
 * Creates a join organization request with tier validation
 */
export async function createJoinRequest(
  userId: string,
  organizationId: string,
  email: string,
  accountTier: AccountTier,
  requestType: 'domain_detection' | 'manual_request' = 'domain_detection',
  message?: string
): Promise<JoinRequestResult> {
  try {
    // First validate tier access
    const tierValidation = validateOrganizationAccess('join', accountTier)
    
    if (!tierValidation.allowed) {
      return {
        success: false,
        error: tierValidation.message,
        tierValidation,
        upgradeRequired: tierValidation.requiresUpgrade
      }
    }
    
    const domain = email.split('@')[1]
    
    if (!domain) {
      return {
        success: false,
        error: 'Invalid email address'
      }
    }
    
    const supabase = createClient()
    
    // Use the database function to create join request
    const { data: requestId, error } = await supabase
      .rpc('create_join_request', {
        p_user_id: userId,
        p_org_id: organizationId,
        p_email: email,
        p_domain: domain,
        p_request_type: requestType,
        p_message: message
      })
    
    if (error) {
      console.error('Error creating join request:', error)
      return {
        success: false,
        error: 'Failed to create join request'
      }
    }
    
    return {
      success: true,
      requestId: requestId,
      tierValidation
    }
    
  } catch (error) {
    console.error('Create join request failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Gets all pending join requests for an organization (admin function)
 */
export async function getOrganizationJoinRequests(
  organizationId: string,
  status?: 'pending' | 'approved' | 'rejected' | 'expired'
): Promise<JoinRequestWithOrg[]> {
  try {
    const supabase = createClient()
    
    let query = supabase
      .from('org_join_requests')
      .select(`
        *,
        organizations (
          id,
          name,
          slug,
          type
        )
      `)
      .eq('org_id', organizationId)
      .order('created_at', { ascending: false })
    
    if (status) {
      query = query.eq('status', status)
    }
    
    const { data, error } = await query
    
    if (error) {
      console.error('Error fetching organization join requests:', error)
      return []
    }
    
    return (data || []).map(item => ({
      ...transformDatabaseToJoinRequest(item),
      organization: {
        id: item.organizations.id,
        name: item.organizations.name,
        slug: item.organizations.slug,
        type: item.organizations.type
      }
    }))
    
  } catch (error) {
    console.error('Get organization join requests failed:', error)
    return []
  }
}

/**
 * Gets all join requests for a user
 */
export async function getUserJoinRequests(userId: string): Promise<JoinRequestWithOrg[]> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('org_join_requests')
      .select(`
        *,
        organizations (
          id,
          name,
          slug,
          type
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching user join requests:', error)
      return []
    }
    
    return (data || []).map(item => ({
      ...transformDatabaseToJoinRequest(item),
      organization: {
        id: item.organizations.id,
        name: item.organizations.name,
        slug: item.organizations.slug,
        type: item.organizations.type
      }
    }))
    
  } catch (error) {
    console.error('Get user join requests failed:', error)
    return []
  }
}

/**
 * Approves or rejects a join request (admin function)
 */
export async function reviewJoinRequest(
  requestId: string,
  action: 'approve' | 'reject',
  reviewedBy: string,
  adminNotes?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    // Get the join request details
    const { data: request, error: fetchError } = await supabase
      .from('org_join_requests')
      .select('*')
      .eq('id', requestId)
      .single()
    
    if (fetchError || !request) {
      return {
        success: false,
        error: 'Join request not found'
      }
    }
    
    // Update the request status
    const { error: updateError } = await supabase
      .from('org_join_requests')
      .update({
        status: action === 'approve' ? 'approved' : 'rejected',
        reviewed_by: reviewedBy,
        reviewed_at: new Date().toISOString(),
        admin_notes: adminNotes
      })
      .eq('id', requestId)
    
    if (updateError) {
      console.error('Error updating join request:', updateError)
      return {
        success: false,
        error: 'Failed to update join request'
      }
    }
    
    // If approved, associate user with organization
    if (action === 'approve') {
      const associationResult = await associateUserWithOrganization(
        request.user_id,
        request.org_id,
        true
      )
      
      if (!associationResult.success) {
        // Rollback the request approval
        await supabase
          .from('org_join_requests')
          .update({ status: 'pending', reviewed_by: null, reviewed_at: null })
          .eq('id', requestId)
        
        return {
          success: false,
          error: 'Failed to associate user with organization'
        }
      }
      
      // Create audit trail
      await createMembershipAudit(
        request.user_id,
        request.org_id,
        'joined',
        null,
        'member',
        reviewedBy,
        'admin_action',
        { requestId, adminNotes }
      )
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Review join request failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Transitions user from individual to organization context
 */
export async function transitionToOrganization(
  transition: MembershipTransition
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    // Start transaction-like operations
    const associationResult = await associateUserWithOrganization(
      transition.userId,
      transition.toOrgId,
      true
    )
    
    if (!associationResult.success) {
      return {
        success: false,
        error: 'Failed to associate user with organization'
      }
    }
    
    // Create audit trail for the transition
    await createMembershipAudit(
      transition.userId,
      transition.toOrgId,
      'joined',
      transition.fromOrgId ? 'individual' : null,
      'member',
      transition.userId,
      'user_request',
      {
        fromOrgId: transition.fromOrgId,
        preserveData: transition.preserveData,
        transferData: transition.transferData
      }
    )
    
    return { success: true }
    
  } catch (error) {
    console.error('Transition to organization failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Creates membership audit trail entry
 */
export async function createMembershipAudit(
  userId: string,
  orgId: string,
  action: 'joined' | 'left' | 'promoted' | 'demoted' | 'removed',
  previousStatus: string | null,
  newStatus: string,
  triggeredBy: string,
  triggerType: 'user_request' | 'admin_action' | 'auto_join' | 'domain_verification',
  details: Record<string, any> = {}
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('org_membership_audit')
      .insert({
        user_id: userId,
        org_id: orgId,
        action,
        previous_status: previousStatus,
        new_status: newStatus,
        triggered_by: triggeredBy,
        trigger_type: triggerType,
        details
      })
    
    if (error) {
      console.error('Error creating membership audit:', error)
      return {
        success: false,
        error: 'Failed to create audit trail'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Create membership audit failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Cancels a pending join request
 */
export async function cancelJoinRequest(
  requestId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('org_join_requests')
      .update({ status: 'rejected' })
      .eq('id', requestId)
      .eq('user_id', userId)
      .eq('status', 'pending')
    
    if (error) {
      console.error('Error canceling join request:', error)
      return {
        success: false,
        error: 'Failed to cancel join request'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Cancel join request failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Checks if user can auto-join organization based on domain
 */
export async function checkAutoJoinEligibility(
  email: string,
  userId: string
): Promise<{
  eligible: boolean
  organizationId?: string
  requiresApproval: boolean
}> {
  try {
    const detection = await detectOrganizationWithRequests(email, userId)
    
    if (!detection.found || !detection.organization) {
      return {
        eligible: false,
        requiresApproval: false
      }
    }
    
    // If has pending request, not eligible for auto-join
    if (detection.hasPendingRequest) {
      return {
        eligible: false,
        requiresApproval: true
      }
    }
    
    return {
      eligible: detection.organization.autoJoinEnabled,
      organizationId: detection.organization.id,
      requiresApproval: !detection.organization.autoJoinEnabled
    }
    
  } catch (error) {
    console.error('Auto-join eligibility check failed:', error)
    return {
      eligible: false,
      requiresApproval: false
    }
  }
}

/**
 * Auto-joins user to organization if eligible
 */
export async function autoJoinOrganization(
  userId: string,
  email: string
): Promise<{ success: boolean; joined: boolean; error?: string }> {
  try {
    const eligibility = await checkAutoJoinEligibility(email, userId)
    
    if (!eligibility.eligible || !eligibility.organizationId) {
      return {
        success: true,
        joined: false
      }
    }
    
    // Associate user with organization
    const associationResult = await associateUserWithOrganization(
      userId,
      eligibility.organizationId,
      true
    )
    
    if (!associationResult.success) {
      return {
        success: false,
        joined: false,
        error: 'Failed to auto-join organization'
      }
    }
    
    // Create audit trail
    await createMembershipAudit(
      userId,
      eligibility.organizationId,
      'joined',
      null,
      'member',
      userId,
      'auto_join',
      { email, autoJoin: true }
    )
    
    return {
      success: true,
      joined: true
    }
    
  } catch (error) {
    console.error('Auto-join organization failed:', error)
    return {
      success: false,
      joined: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Gets membership audit trail for a user
 */
export async function getUserMembershipAudit(userId: string) {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('org_membership_audit')
      .select(`
        *,
        organizations (
          id,
          name,
          slug
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching user membership audit:', error)
      return []
    }
    
    return data || []
    
  } catch (error) {
    console.error('Get user membership audit failed:', error)
    return []
  }
}

/**
 * Transforms database row to JoinRequest object
 */
function transformDatabaseToJoinRequest(data: any): JoinRequest {
  return {
    id: data.id,
    userId: data.user_id,
    orgId: data.org_id,
    email: data.email,
    domain: data.domain,
    status: data.status,
    requestType: data.request_type,
    message: data.message,
    reviewedBy: data.reviewed_by,
    reviewedAt: data.reviewed_at,
    adminNotes: data.admin_notes,
    expiresAt: data.expires_at,
    createdAt: data.created_at,
    updatedAt: data.updated_at
  }
}

/**
 * Cleans up expired join requests (utility function for background tasks)
 */
export async function cleanupExpiredJoinRequests(): Promise<{ cleaned: number; error?: string }> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('org_join_requests')
      .update({ status: 'expired' })
      .lt('expires_at', new Date().toISOString())
      .eq('status', 'pending')
      .select('id')
    
    if (error) {
      console.error('Error cleaning up expired join requests:', error)
      return {
        cleaned: 0,
        error: 'Failed to cleanup expired requests'
      }
    }
    
    return {
      cleaned: (data || []).length
    }
    
  } catch (error) {
    console.error('Cleanup expired join requests failed:', error)
    return {
      cleaned: 0,
      error: 'An unexpected error occurred'
    }
  }
}
