/**
 * Error Handling System
 * 
 * Comprehensive error management for TalentHUB with React Error Boundaries,
 * API error classification, user-friendly messaging, and automatic retry.
 * 
 * Features:
 * - React Error Boundaries for component crashes
 * - API error classification and handling
 * - User-friendly error messages
 * - Automatic retry mechanisms
 * - Error reporting and analytics
 * - Context-aware error recovery
 */

'use client'

import React, { Component, ReactNode, ErrorInfo } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

// Error types and classifications
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'
export type ErrorCategory = 'network' | 'authentication' | 'authorization' | 'validation' | 'sync' | 'ui' | 'unknown'

export interface AppError {
  id: string
  message: string
  userMessage: string
  category: ErrorCategory
  severity: ErrorSeverity
  context?: Record<string, any>
  stack?: string
  timestamp: number
  userId?: string
  retryable: boolean
  component?: string
}

export interface ErrorBoundaryState {
  hasError: boolean
  error: AppError | null
  retryCount: number
  isRetrying: boolean
}

// Error classification patterns
const ERROR_PATTERNS = {
  NETWORK: {
    patterns: [/network/i, /fetch/i, /timeout/i, /connection/i],
    category: 'network' as ErrorCategory,
    severity: 'medium' as ErrorSeverity,
    retryable: true,
    userMessage: 'Connection issue. Please check your internet and try again.'
  },
  AUTHENTICATION: {
    patterns: [/unauthorized/i, /authentication/i, /401/],
    category: 'authentication' as ErrorCategory,
    severity: 'high' as ErrorSeverity,
    retryable: false,
    userMessage: 'Please sign in to continue.'
  },
  AUTHORIZATION: {
    patterns: [/forbidden/i, /permission/i, /403/],
    category: 'authorization' as ErrorCategory,
    severity: 'high' as ErrorSeverity,
    retryable: false,
    userMessage: 'You don\'t have permission to access this feature.'
  },
  VALIDATION: {
    patterns: [/validation/i, /invalid/i, /400/],
    category: 'validation' as ErrorCategory,
    severity: 'low' as ErrorSeverity,
    retryable: false,
    userMessage: 'Please check your input and try again.'
  },
  SYNC: {
    patterns: [/sync/i, /conflict/i, /stale/i],
    category: 'sync' as ErrorCategory,
    severity: 'medium' as ErrorSeverity,
    retryable: true,
    userMessage: 'Data sync issue. Your changes will be saved automatically.'
  },
  QUOTA: {
    patterns: [/quota/i, /limit/i, /429/],
    category: 'authorization' as ErrorCategory,
    severity: 'medium' as ErrorSeverity,
    retryable: true,
    userMessage: 'You\'ve reached your usage limit. Please upgrade or try again later.'
  }
}

// Maximum retry attempts
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY = 2000 // 2 seconds

/**
 * Classify error based on message and context
 */
export function classifyError(error: Error | string, context?: Record<string, any>): AppError {
  const errorMessage = error instanceof Error ? error.message : error
  const stack = error instanceof Error ? error.stack : undefined
  
  // Find matching pattern
  let classification = ERROR_PATTERNS.NETWORK // Default
  
  for (const [, pattern] of Object.entries(ERROR_PATTERNS)) {
    if (pattern.patterns.some(regex => regex.test(errorMessage))) {
      classification = pattern
      break
    }
  }

  return {
    id: generateErrorId(),
    message: errorMessage,
    userMessage: classification.userMessage,
    category: classification.category,
    severity: classification.severity,
    retryable: classification.retryable,
    context,
    stack,
    timestamp: Date.now(),
    component: context?.component
  }
}

/**
 * Generate unique error ID
 */
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Main Error Boundary Component
 */
export class AppErrorBoundary extends Component<
  { children: ReactNode; fallback?: ReactNode; onError?: (error: AppError) => void },
  ErrorBoundaryState
> {
  private retryTimeout: NodeJS.Timeout | null = null

  constructor(props: any) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
      isRetrying: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const appError = classifyError(error, { component: 'ErrorBoundary' })
    
    return {
      hasError: true,
      error: appError
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const appError = classifyError(error, { 
      component: 'ErrorBoundary',
      componentStack: errorInfo.componentStack 
    })

    // Report error
    this.reportError(appError)
    
    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(appError)
    }

    console.error('Error Boundary caught error:', error, errorInfo)
  }

  private reportError = (error: AppError) => {
    // Send error to analytics/monitoring service
    try {
      // In production, send to error tracking service
      if (process.env.NODE_ENV === 'production') {
        // Example: Sentry, LogRocket, etc.
        console.error('Error reported:', error)
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  private handleRetry = () => {
    if (this.state.retryCount >= MAX_RETRY_ATTEMPTS || !this.state.error?.retryable) {
      return
    }

    this.setState({ isRetrying: true })

    this.retryTimeout = setTimeout(() => {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        retryCount: prevState.retryCount + 1,
        isRetrying: false
      }))
    }, RETRY_DELAY)
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/dashboard'
  }

  private getSeverityTitle = (severity: ErrorSeverity): string => {
    switch (severity) {
      case 'critical': return 'Critical Error'
      case 'high': return 'Error'
      case 'medium': return 'Something Went Wrong'
      case 'low': return 'Minor Issue'
      default: return 'Unexpected Error'
    }
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout)
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
          <Card className="w-full max-w-lg mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle className="text-xl font-semibold text-gray-900">
                {this.getSeverityTitle(this.state.error.severity)}
              </CardTitle>
              <CardDescription className="text-gray-600 mt-2">
                {this.state.error.userMessage}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="text-center space-y-4">
              {/* Error details for development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-left bg-gray-50 p-3 rounded-md border">
                  <p className="font-medium text-sm text-gray-800 mb-1">Debug Info:</p>
                  <p className="text-xs text-gray-600 font-mono">
                    {this.state.error.message}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Category: {this.state.error.category} | Severity: {this.state.error.severity}
                  </p>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex flex-col gap-2">
                {this.state.error.retryable && this.state.retryCount < MAX_RETRY_ATTEMPTS && (
                  <Button
                    onClick={this.handleRetry}
                    disabled={this.state.isRetrying}
                    className="w-full"
                  >
                    {this.state.isRetrying ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Retrying...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Try Again ({MAX_RETRY_ATTEMPTS - this.state.retryCount} left)
                      </>
                    )}
                  </Button>
                )}

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={this.handleGoHome}
                    className="flex-1"
                  >
                    <Home className="w-4 h-4 mr-2" />
                    Go Home
                  </Button>

                  <Button
                    variant="outline"
                    onClick={this.handleReload}
                    className="flex-1"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reload
                  </Button>
                </div>

                {process.env.NODE_ENV === 'development' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => console.log('Full error:', this.state.error)}
                    className="text-xs"
                  >
                    <Bug className="w-3 h-3 mr-1" />
                    Log Details
                  </Button>
                )}
              </div>

              <p className="text-xs text-gray-500 mt-4">
                Error ID: {this.state.error.id}
              </p>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * HOC for wrapping components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: AppError) => void
) {
  return function WrappedComponent(props: P) {
    return (
      <AppErrorBoundary fallback={fallback} onError={onError}>
        <Component {...props} />
      </AppErrorBoundary>
    )
  }
}

/**
 * Hook for handling async errors
 */
export function useErrorHandler() {
  const handleError = React.useCallback((error: Error | string, context?: Record<string, any>) => {
    const appError = classifyError(error, context)
    
    // Report error
    console.error('Async error handled:', appError)
    
    // In production, send to error tracking
    if (process.env.NODE_ENV === 'production') {
      // Send to monitoring service
    }

    return appError
  }, [])

  const handleAsyncError = React.useCallback(async (asyncFn: () => Promise<any>) => {
    try {
      return await asyncFn()
    } catch (error) {
      return handleError(error instanceof Error ? error : new Error(String(error)))
    }
  }, [handleError])

  return { handleError, handleAsyncError }
}

/**
 * Specific error boundary for sync operations
 */
export function SyncErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <AppErrorBoundary
      onError={(error) => {
        // Specific handling for sync errors
        if (error.category === 'sync') {
          // Maybe trigger a sync retry or show sync status
          console.log('Sync error detected, triggering recovery...')
        }
      }}
      fallback={
        <div className="p-4 bg-orange-50 border border-orange-200 rounded-md">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-orange-600 mr-2" />
            <p className="text-orange-800 font-medium">Sync Issue</p>
          </div>
          <p className="text-orange-600 text-sm mt-1">
            Having trouble syncing your data. Your changes are saved locally and will sync automatically.
          </p>
        </div>
      }
    >
      {children}
    </AppErrorBoundary>
  )
}

/**
 * Network error boundary for API operations
 */
export function NetworkErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <AppErrorBoundary
      onError={(error) => {
        if (error.category === 'network') {
          // Maybe show offline indicator or retry suggestions
          console.log('Network error detected, showing offline state...')
        }
      }}
      fallback={
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-blue-600 mr-2" />
            <p className="text-blue-800 font-medium">Connection Issue</p>
          </div>
          <p className="text-blue-600 text-sm mt-1">
            Check your internet connection and try again.
          </p>
        </div>
      }
    >
      {children}
    </AppErrorBoundary>
  )
}

/**
 * Utility functions for error handling
 */

/**
 * Safely execute async function with error handling
 */
export async function safeAsync<T>(
  fn: () => Promise<T>,
  fallback?: T,
  onError?: (error: AppError) => void
): Promise<T | undefined> {
  try {
    return await fn()
  } catch (error) {
    const appError = classifyError(error instanceof Error ? error : new Error(String(error)))
    
    if (onError) {
      onError(appError)
    }
    
    console.error('Safe async error:', appError)
    return fallback
  }
}

/**
 * Create error reporter function
 */
export function createErrorReporter(context: Record<string, any> = {}) {
  return (error: Error | string, additionalContext?: Record<string, any>) => {
    const appError = classifyError(error, { ...context, ...additionalContext })
    
    // Log error
    console.error('Error reported:', appError)
    
    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Send to error tracking service
    }
    
    return appError
  }
}

export default AppErrorBoundary