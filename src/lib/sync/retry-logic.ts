/**
 * Retry Logic Service
 * 
 * Robust retry mechanisms for failed operations in TalentHUB.
 * Implements exponential backoff, circuit breaker patterns,
 * and operation priority queuing for reliable data operations.
 * 
 * Features:
 * - Exponential backoff algorithms
 * - Circuit breaker pattern for failing services
 * - Operation priority queuing
 * - Maximum retry limits per operation type
 * - Jitter for distributed retry timing
 * - Health monitoring and metrics
 */

export type RetryableOperation = {
  id: string
  name: string
  operation: () => Promise<any>
  priority: 'high' | 'medium' | 'low'
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
  jitter?: boolean
  context?: Record<string, any>
}

export type RetryResult<T = any> = {
  success: boolean
  data?: T
  error?: Error
  attempts: number
  totalTime: number
  cancelled: boolean
}

export type CircuitState = 'closed' | 'open' | 'half-open'

export interface CircuitBreakerConfig {
  failureThreshold: number
  recoveryTimeout: number
  monitoringPeriod: number
}

export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  multiplier: number
  jitter: boolean
  timeoutMs?: number
}

// Default retry configurations for different operation types
export const RETRY_CONFIGS: Record<string, RetryConfig> = {
  'critical': {
    maxRetries: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    multiplier: 2,
    jitter: true,
    timeoutMs: 10000
  },
  'important': {
    maxRetries: 3,
    baseDelay: 2000,
    maxDelay: 15000,
    multiplier: 1.5,
    jitter: true,
    timeoutMs: 8000
  },
  'background': {
    maxRetries: 2,
    baseDelay: 5000,
    maxDelay: 60000,
    multiplier: 2,
    jitter: true,
    timeoutMs: 30000
  },
  'network': {
    maxRetries: 4,
    baseDelay: 500,
    maxDelay: 8000,
    multiplier: 2,
    jitter: true,
    timeoutMs: 5000
  }
}

// Circuit breaker configurations
export const CIRCUIT_BREAKER_CONFIGS: Record<string, CircuitBreakerConfig> = {
  'database': {
    failureThreshold: 5,
    recoveryTimeout: 60000,
    monitoringPeriod: 30000
  },
  'api': {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    monitoringPeriod: 15000
  },
  'sync': {
    failureThreshold: 10,
    recoveryTimeout: 120000,
    monitoringPeriod: 60000
  }
}

/**
 * Circuit Breaker implementation
 */
class CircuitBreaker {
  private state: CircuitState = 'closed'
  private failures = 0
  private lastFailureTime = 0
  private nextAttemptTime = 0

  constructor(
    private config: CircuitBreakerConfig,
    private name: string
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() < this.nextAttemptTime) {
        throw new Error(`Circuit breaker is open for ${this.name}`)
      }
      this.state = 'half-open'
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess(): void {
    this.failures = 0
    this.state = 'closed'
  }

  private onFailure(): void {
    this.failures++
    this.lastFailureTime = Date.now()

    if (this.failures >= this.config.failureThreshold) {
      this.state = 'open'
      this.nextAttemptTime = Date.now() + this.config.recoveryTimeout
    }
  }

  getState(): CircuitState {
    return this.state
  }

  getMetrics() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
      nextAttemptTime: this.nextAttemptTime
    }
  }
}

/**
 * Priority Queue for retry operations
 */
class RetryQueue {
  private queue: Array<{
    operation: RetryableOperation
    priority: number
    addedAt: number
  }> = []

  enqueue(operation: RetryableOperation): void {
    const priority = this.getPriorityValue(operation.priority)
    this.queue.push({
      operation,
      priority,
      addedAt: Date.now()
    })
    this.queue.sort((a, b) => b.priority - a.priority)
  }

  dequeue(): RetryableOperation | undefined {
    const item = this.queue.shift()
    return item?.operation
  }

  size(): number {
    return this.queue.length
  }

  clear(): void {
    this.queue = []
  }

  private getPriorityValue(priority: 'high' | 'medium' | 'low'): number {
    switch (priority) {
      case 'high': return 3
      case 'medium': return 2
      case 'low': return 1
      default: return 1
    }
  }
}

/**
 * Main Retry Service
 */
class RetryService {
  private circuitBreakers = new Map<string, CircuitBreaker>()
  private activeOperations = new Map<string, AbortController>()
  private retryQueue = new RetryQueue()
  private metrics = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    retriedOperations: 0
  }

  /**
   * Execute operation with retry logic
   */
  async executeWithRetry<T>(
    operation: RetryableOperation,
    configType: string = 'important'
  ): Promise<RetryResult<T>> {
    const config = RETRY_CONFIGS[configType] || RETRY_CONFIGS.important
    const startTime = Date.now()
    let attempts = 0
    let lastError: Error | undefined

    // Create abort controller for cancellation
    const abortController = new AbortController()
    this.activeOperations.set(operation.id, abortController)

    // Get or create circuit breaker for operation context
    const circuitBreakerKey = operation.context?.service || 'default'
    const circuitBreaker = this.getCircuitBreaker(circuitBreakerKey)

    this.metrics.totalOperations++

    try {
      while (attempts <= (operation.maxRetries || config.maxRetries)) {
        if (abortController.signal.aborted) {
          return {
            success: false,
            error: new Error('Operation cancelled'),
            attempts,
            totalTime: Date.now() - startTime,
            cancelled: true
          }
        }

        attempts++

        try {
          // Execute with circuit breaker protection
          const result = await circuitBreaker.execute(async () => {
            // Add timeout if configured
            if (config.timeoutMs) {
              return await this.withTimeout(operation.operation(), config.timeoutMs)
            }
            return await operation.operation()
          })

          this.metrics.successfulOperations++
          if (attempts > 1) {
            this.metrics.retriedOperations++
          }

          return {
            success: true,
            data: result,
            attempts,
            totalTime: Date.now() - startTime,
            cancelled: false
          }

        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error))
          
          // Check if this is a retryable error
          if (!this.isRetryableError(lastError) || attempts > (operation.maxRetries || config.maxRetries)) {
            break
          }

          // Calculate delay with exponential backoff and jitter
          const delay = this.calculateDelay(
            attempts,
            operation.baseDelay || config.baseDelay,
            operation.maxDelay || config.maxDelay,
            config.multiplier,
            operation.jitter !== undefined ? operation.jitter : config.jitter
          )

          // Wait before retry
          await this.delay(delay)
        }
      }

      this.metrics.failedOperations++
      return {
        success: false,
        error: lastError || new Error('Maximum retry attempts exceeded'),
        attempts,
        totalTime: Date.now() - startTime,
        cancelled: false
      }

    } finally {
      this.activeOperations.delete(operation.id)
    }
  }

  /**
   * Execute multiple operations with retry logic
   */
  async executeMultipleWithRetry<T>(
    operations: RetryableOperation[],
    configType: string = 'important',
    concurrency: number = 3
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = []
    const executing: Promise<void>[] = []

    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i]
      
      const promise = this.executeWithRetry<T>(operation, configType)
        .then(result => {
          results[i] = result
        })

      executing.push(promise)

      // Control concurrency
      if (executing.length >= concurrency) {
        await Promise.race(executing)
        // Remove completed promises
        for (let j = executing.length - 1; j >= 0; j--) {
          if (results[i] !== undefined) {
            executing.splice(j, 1)
          }
        }
      }
    }

    // Wait for remaining operations
    await Promise.allSettled(executing)
    return results
  }

  /**
   * Cancel operation by ID
   */
  cancelOperation(operationId: string): boolean {
    const controller = this.activeOperations.get(operationId)
    if (controller) {
      controller.abort()
      return true
    }
    return false
  }

  /**
   * Cancel all active operations
   */
  cancelAllOperations(): void {
    this.activeOperations.forEach(controller => controller.abort())
    this.activeOperations.clear()
  }

  /**
   * Get circuit breaker for service
   */
  private getCircuitBreaker(serviceName: string): CircuitBreaker {
    if (!this.circuitBreakers.has(serviceName)) {
      const config = CIRCUIT_BREAKER_CONFIGS[serviceName] || CIRCUIT_BREAKER_CONFIGS.api
      this.circuitBreakers.set(serviceName, new CircuitBreaker(config, serviceName))
    }
    return this.circuitBreakers.get(serviceName)!
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(
    attempt: number,
    baseDelay: number,
    maxDelay: number,
    multiplier: number,
    jitter: boolean
  ): number {
    const exponentialDelay = Math.min(baseDelay * Math.pow(multiplier, attempt - 1), maxDelay)
    
    if (!jitter) {
      return exponentialDelay
    }

    // Add jitter (±25% of the delay)
    const jitterRange = exponentialDelay * 0.25
    const jitterValue = (Math.random() - 0.5) * 2 * jitterRange
    return Math.max(0, exponentialDelay + jitterValue)
  }

  /**
   * Determine if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /connection/i,
      /502/,
      /503/,
      /504/,
      /520/,
      /521/,
      /522/,
      /523/,
      /524/,
      /temporary/i,
      /rate.*limit/i
    ]

    const nonRetryablePatterns = [
      /401/,
      /403/,
      /404/,
      /400/,
      /validation/i,
      /unauthorized/i,
      /forbidden/i,
      /not.*found/i
    ]

    // Check non-retryable first
    if (nonRetryablePatterns.some(pattern => pattern.test(error.message))) {
      return false
    }

    // Check retryable patterns
    return retryablePatterns.some(pattern => pattern.test(error.message))
  }

  /**
   * Add timeout to operation
   */
  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeoutMs}ms`))
      }, timeoutMs)

      promise
        .then(resolve)
        .catch(reject)
        .finally(() => clearTimeout(timeout))
    })
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get service metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      activeOperations: this.activeOperations.size,
      queueSize: this.retryQueue.size(),
      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([name, breaker]) => ({
        name,
        ...breaker.getMetrics()
      }))
    }
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      retriedOperations: 0
    }
  }

  /**
   * Get health status
   */
  getHealthStatus() {
    const metrics = this.getMetrics()
    const successRate = metrics.totalOperations > 0 
      ? (metrics.successfulOperations / metrics.totalOperations) * 100 
      : 100

    const openCircuits = metrics.circuitBreakers.filter(cb => cb.state === 'open').length

    return {
      healthy: successRate >= 90 && openCircuits === 0,
      successRate,
      openCircuits,
      activeOperations: metrics.activeOperations,
      queueSize: metrics.queueSize
    }
  }
}

// Singleton instance
let retryServiceInstance: RetryService | null = null

/**
 * Get singleton retry service instance
 */
export function getRetryService(): RetryService {
  if (!retryServiceInstance) {
    retryServiceInstance = new RetryService()
  }
  return retryServiceInstance
}

/**
 * High-level retry utility functions
 */

/**
 * Retry a function with default configuration
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number
    baseDelay?: number
    maxDelay?: number
    priority?: 'high' | 'medium' | 'low'
    context?: Record<string, any>
  } = {}
): Promise<T> {
  const service = getRetryService()
  const operationId = `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const retryableOp: RetryableOperation = {
    id: operationId,
    name: 'anonymous',
    operation,
    priority: options.priority || 'medium',
    maxRetries: options.maxRetries,
    baseDelay: options.baseDelay,
    maxDelay: options.maxDelay,
    context: options.context
  }

  const result = await service.executeWithRetry<T>(retryableOp, 'important')
  
  if (!result.success) {
    throw result.error || new Error('Operation failed after retries')
  }

  return result.data!
}

/**
 * Retry database operations
 */
export async function retryDatabaseOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  return retryOperation(operation, {
    maxRetries,
    priority: 'high',
    context: { service: 'database', type: 'database' }
  })
}

/**
 * Retry API operations
 */
export async function retryApiOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 4
): Promise<T> {
  return retryOperation(operation, {
    maxRetries,
    priority: 'medium',
    context: { service: 'api', type: 'network' }
  })
}

/**
 * Retry sync operations
 */
export async function retrySyncOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 5
): Promise<T> {
  return retryOperation(operation, {
    maxRetries,
    priority: 'high',
    context: { service: 'sync', type: 'sync' }
  })
}

/**
 * Create a retryable version of any async function
 */
export function withRetry<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: {
    maxRetries?: number
    baseDelay?: number
    priority?: 'high' | 'medium' | 'low'
    context?: Record<string, any>
  } = {}
): T {
  return ((...args: Parameters<T>) => {
    return retryOperation(() => fn(...args), options)
  }) as T
}

/**
 * Batch retry multiple operations
 */
export async function retryMultipleOperations<T>(
  operations: Array<{
    operation: () => Promise<T>
    name?: string
    priority?: 'high' | 'medium' | 'low'
  }>,
  concurrency: number = 3
): Promise<Array<RetryResult<T>>> {
  const service = getRetryService()
  
  const retryableOps: RetryableOperation[] = operations.map((op, index) => ({
    id: `batch_${Date.now()}_${index}`,
    name: op.name || `operation_${index}`,
    operation: op.operation,
    priority: op.priority || 'medium'
  }))

  return service.executeMultipleWithRetry(retryableOps, 'important', concurrency)
}

export default getRetryService