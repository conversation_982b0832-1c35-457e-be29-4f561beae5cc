{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "curl -X POST http://localhost:3000/api/db-test"}, "dependencies": {"@clerk/nextjs": "^6.23.0", "@formkit/tempo": "^0.1.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.469.0", "next": "^15.3.4", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "sonner": "^2.0.5", "svix": "^1.68.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/node": "^22.9.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "15.1.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3"}}