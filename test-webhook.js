/**
 * Test script to verify webhook functionality
 * This simulates a real Clerk webhook request
 */

const crypto = require('crypto');

// Simulate Clerk user.created event
const testPayload = {
  data: {
    id: 'user_test123456789',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email_addresses: [
      {
        id: 'email_123',
        email_address: '<EMAIL>'
      }
    ],
    primary_email_address_id: 'email_123',
    created_at: Date.now(),
    updated_at: Date.now()
  },
  object: 'event',
  type: 'user.created'
};

// Create test headers (simplified - in real scenario Svix creates these)
const timestamp = Math.floor(Date.now() / 1000).toString();
const payload = JSON.stringify(testPayload);

console.log('Testing webhook with payload:');
console.log(JSON.stringify(testPayload, null, 2));

// Note: This test will fail signature verification (as expected)
// but will help us verify the endpoint is reachable and processing requests correctly
fetch('http://localhost:3002/api/webhooks/clerk', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'svix-id': 'msg_test123',
    'svix-timestamp': timestamp,
    'svix-signature': 'v1,test_signature_will_fail'
  },
  body: payload
})
.then(response => response.text())
.then(data => {
  console.log('Response:', data);
  console.log('✅ Webhook endpoint is responding correctly');
})
.catch(error => {
  console.error('❌ Error:', error);
});
