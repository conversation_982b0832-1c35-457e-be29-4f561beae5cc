#!/bin/bash

# TalentHUB Webhook Secrets Setup Script
# This script helps configure the required environment variables for Clerk webhooks

echo "🚀 TalentHUB Webhook Secrets Setup"
echo "=================================="

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if logged in to Supabase
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please login first:"
    echo "   supabase login"
    exit 1
fi

echo "✅ Supabase CLI is ready"

# Get current project info
PROJECT_REF="hfzhvrknjgwtrkgyinjf"
echo "📋 Project Reference: $PROJECT_REF"

# Check current secrets
echo ""
echo "🔍 Current secrets:"
supabase secrets list --project-ref $PROJECT_REF

echo ""
echo "📝 Setting up required secrets..."

# Prompt for <PERSON> webhook secret
echo ""
echo "🔐 Clerk Webhook Secret Setup"
echo "1. Go to your Clerk Dashboard: https://dashboard.clerk.com"
echo "2. Navigate to Webhooks section"
echo "3. Find or create a webhook endpoint"
echo "4. Copy the 'Signing Secret' (starts with 'whsec_')"
echo ""
read -p "Enter your Clerk Webhook Secret (whsec_...): " CLERK_WEBHOOK_SECRET

# Validate the webhook secret format
if [[ ! $CLERK_WEBHOOK_SECRET =~ ^whsec_ ]]; then
    echo "❌ Invalid webhook secret format. It should start with 'whsec_'"
    echo "   Example: whsec_abc123def456..."
    exit 1
fi

echo "✅ Webhook secret format is valid"

# Set the secret in Supabase
echo ""
echo "🔧 Setting CLERK_WEBHOOK_SECRET in Supabase..."
supabase secrets set --project-ref $PROJECT_REF CLERK_WEBHOOK_SECRET="$CLERK_WEBHOOK_SECRET"

if [ $? -eq 0 ]; then
    echo "✅ CLERK_WEBHOOK_SECRET set successfully"
else
    echo "❌ Failed to set CLERK_WEBHOOK_SECRET"
    exit 1
fi

# Verify the secret was set
echo ""
echo "🔍 Verifying secrets are set:"
supabase secrets list --project-ref $PROJECT_REF

echo ""
echo "🎉 Webhook secrets setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Deploy your Edge Function: supabase functions deploy clerk-webhook"
echo "2. Update your Clerk webhook endpoint URL to:"
echo "   https://hfzhvrknjgwtrkgyinjf.supabase.co/functions/v1/clerk-webhook"
echo "3. Test the webhook using the debug script:"
echo "   npx ts-node scripts/debug-webhook-verification.ts"
echo ""
echo "🔗 Useful commands:"
echo "   supabase functions logs clerk-webhook --follow"
echo "   supabase functions deploy clerk-webhook"
echo ""
echo "📚 For troubleshooting, see: docs/WEBHOOK_TROUBLESHOOTING_GUIDE.md"
