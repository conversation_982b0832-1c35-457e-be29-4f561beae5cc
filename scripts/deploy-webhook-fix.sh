#!/bin/bash

# TalentHUB Webhook Fix Deployment Script
# This script deploys the improved webhook with proper error handling

echo "🚀 TalentHUB Webhook Fix Deployment"
echo "==================================="

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if logged in to Supabase
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please login first:"
    echo "   supabase login"
    exit 1
fi

echo "✅ Supabase CLI is ready"

PROJECT_REF="hfzhvrknjgwtrkgyinjf"
FUNCTION_NAME="clerk-webhook"

# Check current secrets
echo ""
echo "🔍 Checking environment variables..."
SECRETS=$(supabase secrets list --project-ref $PROJECT_REF)
echo "$SECRETS"

if [[ $SECRETS == *"CLERK_WEBHOOK_SECRET"* ]]; then
    echo "✅ CLERK_WEBHOOK_SECRET is configured"
else
    echo "❌ CLERK_WEBHOOK_SECRET is not configured"
    echo "   Please run: ./scripts/setup-webhook-secrets.sh"
    exit 1
fi

# Backup current function
echo ""
echo "💾 Creating backup of current function..."
BACKUP_DIR="backup-functions/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r "supabase/functions/$FUNCTION_NAME" "$BACKUP_DIR/"
echo "✅ Backup created at: $BACKUP_DIR"

# Ask user which version to deploy
echo ""
echo "📋 Choose deployment option:"
echo "1. Deploy improved version (recommended)"
echo "2. Deploy current version with fixes"
echo "3. Cancel deployment"
echo ""
read -p "Enter your choice (1-3): " CHOICE

case $CHOICE in
    1)
        echo "🔄 Deploying improved version..."
        # Replace current index.ts with improved version
        cp "supabase/functions/$FUNCTION_NAME/index-improved.ts" "supabase/functions/$FUNCTION_NAME/index.ts"
        echo "✅ Switched to improved version"
        ;;
    2)
        echo "🔄 Using current version with existing fixes..."
        ;;
    3)
        echo "❌ Deployment cancelled"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Deployment cancelled"
        exit 1
        ;;
esac

# Deploy the function
echo ""
echo "🚀 Deploying Edge Function..."
supabase functions deploy $FUNCTION_NAME --project-ref $PROJECT_REF

if [ $? -eq 0 ]; then
    echo "✅ Function deployed successfully"
else
    echo "❌ Function deployment failed"
    echo "🔄 Restoring from backup..."
    cp -r "$BACKUP_DIR/$FUNCTION_NAME/"* "supabase/functions/$FUNCTION_NAME/"
    exit 1
fi

# Get function URL
FUNCTION_URL="https://$PROJECT_REF.supabase.co/functions/v1/$FUNCTION_NAME"
echo ""
echo "🔗 Function URL: $FUNCTION_URL"

# Test the function
echo ""
echo "🧪 Testing function accessibility..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$FUNCTION_URL" \
    -H "Content-Type: application/json" \
    -d '{"test": true}')

if [ "$HTTP_STATUS" = "400" ]; then
    echo "✅ Function is accessible (400 = missing svix headers, which is expected)"
elif [ "$HTTP_STATUS" = "405" ]; then
    echo "✅ Function is accessible (405 = method not allowed for non-POST)"
else
    echo "⚠️  Function returned status: $HTTP_STATUS"
    echo "   This might indicate an issue. Check the logs."
fi

# Show logs
echo ""
echo "📋 Recent function logs:"
supabase functions logs $FUNCTION_NAME --project-ref $PROJECT_REF | tail -20

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update your Clerk webhook endpoint URL to:"
echo "   $FUNCTION_URL"
echo "2. Test with a real webhook event from Clerk"
echo "3. Monitor logs: supabase functions logs $FUNCTION_NAME --follow"
echo ""
echo "🔧 Troubleshooting:"
echo "   - Check logs: supabase functions logs $FUNCTION_NAME"
echo "   - Verify secrets: supabase secrets list --project-ref $PROJECT_REF"
echo "   - Test endpoint: curl -X POST $FUNCTION_URL"
echo ""
echo "📚 Documentation: docs/WEBHOOK_TROUBLESHOOTING_GUIDE.md"
