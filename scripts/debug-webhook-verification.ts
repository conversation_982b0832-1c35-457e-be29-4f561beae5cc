/**
 * TalentHUB Webhook Verification Debug <PERSON>ript
 * 
 * This script helps debug Clerk webhook signature verification issues
 * by testing the verification process with sample data.
 */

import { Webhook } from 'svix'

// Test configuration
const TEST_WEBHOOK_SECRET = 'whsec_test123456789abcdef'
const TEST_PAYLOAD = JSON.stringify({
  type: 'user.created',
  data: {
    id: 'user_test123',
    email_addresses: [{ email_address: '<EMAIL>' }],
    first_name: 'Test',
    last_name: 'User'
  }
})

/**
 * Test webhook signature verification with different scenarios
 */
async function testWebhookVerification() {
  console.log('🧪 Testing Webhook Verification\n')
  
  // Test 1: Valid signature verification
  console.log('Test 1: Valid signature verification')
  try {
    const wh = new Webhook(TEST_WEBHOOK_SECRET)
    
    // Generate valid headers
    const timestamp = Math.floor(Date.now() / 1000).toString()
    const id = 'msg_test123'
    
    // Create signature manually for testing
    const crypto = require('crypto')
    const payload = `${timestamp}.${TEST_PAYLOAD}`
    const signature = crypto
      .createHmac('sha256', TEST_WEBHOOK_SECRET.replace('whsec_', ''))
      .update(payload)
      .digest('base64')
    
    const headers = {
      'svix-id': id,
      'svix-timestamp': timestamp,
      'svix-signature': `v1,${signature}`
    }
    
    console.log('Headers:', headers)
    console.log('Payload length:', TEST_PAYLOAD.length)
    
    const event = wh.verify(TEST_PAYLOAD, headers)
    console.log('✅ Verification successful:', event.type)
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // Test 2: Invalid signature
  console.log('Test 2: Invalid signature (should fail)')
  try {
    const wh = new Webhook(TEST_WEBHOOK_SECRET)
    
    const headers = {
      'svix-id': 'msg_test123',
      'svix-timestamp': Math.floor(Date.now() / 1000).toString(),
      'svix-signature': 'v1,invalid_signature'
    }
    
    const event = wh.verify(TEST_PAYLOAD, headers)
    console.log('❌ This should not succeed:', event.type)
    
  } catch (error) {
    console.log('✅ Correctly failed verification:', error.message)
  }
  
  console.log('\n' + '='.repeat(50) + '\n')
  
  // Test 3: Missing headers
  console.log('Test 3: Missing headers (should fail)')
  try {
    const wh = new Webhook(TEST_WEBHOOK_SECRET)
    
    const headers = {
      'svix-id': 'msg_test123',
      // Missing timestamp and signature
    }
    
    const event = wh.verify(TEST_PAYLOAD, headers as any)
    console.log('❌ This should not succeed:', event.type)
    
  } catch (error) {
    console.log('✅ Correctly failed verification:', error.message)
  }
}

/**
 * Test environment variable access patterns
 */
function testEnvironmentVariables() {
  console.log('\n🔧 Testing Environment Variables\n')
  
  // Node.js style
  console.log('Node.js process.env access:')
  console.log('- CLERK_WEBHOOK_SECRET exists:', !!process.env.CLERK_WEBHOOK_SECRET)
  console.log('- CLERK_WEBHOOK_SECRET length:', process.env.CLERK_WEBHOOK_SECRET?.length)
  console.log('- CLERK_WEBHOOK_SECRET starts with whsec_:', process.env.CLERK_WEBHOOK_SECRET?.startsWith('whsec_'))
  
  // Deno style (for reference)
  console.log('\nDeno Deno.env.get() access (for Edge Function reference):')
  console.log('- Would use: Deno.env.get("CLERK_WEBHOOK_SECRET")')
  console.log('- Make sure this is set in Supabase Dashboard > Settings > Edge Functions > Environment Variables')
}

/**
 * Generate sample webhook headers for testing
 */
function generateSampleHeaders(payload: string, secret: string) {
  console.log('\n🔨 Generating Sample Headers\n')
  
  const crypto = require('crypto')
  const timestamp = Math.floor(Date.now() / 1000).toString()
  const id = `msg_${Date.now()}`
  
  // Create signature
  const signaturePayload = `${timestamp}.${payload}`
  const signature = crypto
    .createHmac('sha256', secret.replace('whsec_', ''))
    .update(signaturePayload)
    .digest('base64')
  
  const headers = {
    'svix-id': id,
    'svix-timestamp': timestamp,
    'svix-signature': `v1,${signature}`
  }
  
  console.log('Sample headers for testing:')
  console.log(JSON.stringify(headers, null, 2))
  
  console.log('\nCurl command for testing:')
  console.log(`curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "svix-id: ${headers['svix-id']}" \\
  -H "svix-timestamp: ${headers['svix-timestamp']}" \\
  -H "svix-signature: ${headers['svix-signature']}" \\
  -d '${payload}' \\
  YOUR_EDGE_FUNCTION_URL`)
  
  return headers
}

/**
 * Main debug function
 */
async function main() {
  console.log('🚀 TalentHUB Webhook Verification Debug Tool\n')
  
  await testWebhookVerification()
  testEnvironmentVariables()
  generateSampleHeaders(TEST_PAYLOAD, TEST_WEBHOOK_SECRET)
  
  console.log('\n📋 Troubleshooting Checklist:')
  console.log('1. ✅ Webhook secret is properly formatted (starts with whsec_)')
  console.log('2. ✅ Environment variable is set in Supabase Dashboard')
  console.log('3. ✅ Headers are being passed correctly from Clerk')
  console.log('4. ✅ Body is read as raw text/bytes (not parsed JSON)')
  console.log('5. ✅ Svix library version is compatible')
  console.log('6. ✅ Edge Function has JWT verification disabled')
  
  console.log('\n🔗 Useful Links:')
  console.log('- Clerk Webhook Docs: https://clerk.com/docs/webhooks/sync-data')
  console.log('- Svix Verification: https://docs.svix.com/receiving/verifying-payloads')
  console.log('- Supabase Edge Functions: https://supabase.com/docs/guides/functions')
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

export { testWebhookVerification, testEnvironmentVariables, generateSampleHeaders }
