#!/bin/bash

# 🚀 TalentHUB Cloudflare Tunnel Quick Setup
# Run this script to set up your development webhook tunnel

echo "🌟 TalentHUB Cloudflare Tunnel Setup"
echo "=================================="

# Check if cloudflared is installed
if ! command -v cloudflared &> /dev/null; then
    echo "❌ cloudflared not found. Installing..."
    
    # Detect OS and install
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install cloudflared
        else
            echo "Please install Homebrew first or download cloudflared manually"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
        sudo dpkg -i cloudflared-linux-amd64.deb
        rm cloudflared-linux-amd64.deb
    else
        echo "Please install cloudflared manually for your OS"
        exit 1
    fi
fi

echo "✅ cloudflared is installed"

# Check if already authenticated
if [ ! -f ~/.cloudflared/cert.pem ]; then
    echo "🔐 Authenticating with Cloudflare..."
    echo "This will open your browser. Please authorize the tunnel."
    cloudflared tunnel login
fi

echo "✅ Authenticated with Cloudflare"

# Prompt for tunnel configuration
read -p "🌐 Enter your domain name (e.g., yourdomain.com): " DOMAIN
read -p "📡 Enter subdomain for development (e.g., dev-webhooks): " SUBDOMAIN

TUNNEL_NAME="talenthub-dev"
FULL_DOMAIN="${SUBDOMAIN}.${DOMAIN}"

# Create tunnel
echo "🛠️  Creating tunnel: $TUNNEL_NAME"
TUNNEL_OUTPUT=$(cloudflared tunnel create $TUNNEL_NAME 2>&1)
TUNNEL_ID=$(echo "$TUNNEL_OUTPUT" | grep -o '[a-f0-9\-]\{36\}' | head -1)

if [ -z "$TUNNEL_ID" ]; then
    echo "❌ Failed to create tunnel. Output:"
    echo "$TUNNEL_OUTPUT"
    exit 1
fi

echo "✅ Tunnel created with ID: $TUNNEL_ID"

# Create DNS record
echo "🌍 Creating DNS record for $FULL_DOMAIN"
cloudflared tunnel route dns $TUNNEL_NAME $FULL_DOMAIN

# Create config directory if it doesn't exist
mkdir -p config

# Create tunnel configuration
cat > config/cloudflare-tunnel.yml << EOF
tunnel: $TUNNEL_ID
credentials-file: $HOME/.cloudflared/$TUNNEL_ID.json

ingress:
  - hostname: $FULL_DOMAIN
    service: http://localhost:3000
  - service: http_status:404
EOF

echo "✅ Configuration created at config/cloudflare-tunnel.yml"

# Update .env.local
if [ -f .env.local ]; then
    # Backup existing file
    cp .env.local .env.local.backup
    
    # Update webhook URL
    sed -i.bak "s|WEBHOOK_URL=.*|WEBHOOK_URL=https://$FULL_DOMAIN/api/webhooks/clerk|" .env.local
    
    # Add Cloudflare tunnel URL if not present
    if ! grep -q "CLOUDFLARE_TUNNEL_URL" .env.local; then
        echo "" >> .env.local
        echo "# Cloudflare Tunnel Configuration" >> .env.local
        echo "CLOUDFLARE_TUNNEL_URL=https://$FULL_DOMAIN" >> .env.local
    fi
    
    rm .env.local.bak
    echo "✅ Updated .env.local with tunnel URL"
fi

# Create start script
cat > scripts/start-tunnel.sh << EOF
#!/bin/bash
echo "🚀 Starting TalentHUB Cloudflare Tunnel..."
cloudflared tunnel --config config/cloudflare-tunnel.yml run $TUNNEL_NAME
EOF

chmod +x scripts/start-tunnel.sh

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo "🌐 Your webhook URL: https://$FULL_DOMAIN/api/webhooks/clerk"
echo ""
echo "Next steps:"
echo "1. Start your Next.js app: npm run dev"
echo "2. Start the tunnel: ./scripts/start-tunnel.sh"
echo "3. Go to Clerk Dashboard → Webhooks"
echo "4. Add endpoint: https://$FULL_DOMAIN/api/webhooks/clerk"
echo "5. Copy the signing secret to CLERK_WEBHOOK_SECRET in .env.local"
echo ""
echo "🔗 Full setup guide: scripts/setup-cloudflare-tunnel.md"
