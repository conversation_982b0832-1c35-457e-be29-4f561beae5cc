# 🔄 Alternative: ngrok Setup (Backup Option)

If you prefer to use ngrok instead of Cloudflare Tunnel:

## Step 1: Install ngrok

### macOS
```bash
brew install ngrok/ngrok/ngrok
```

### Linux/Windows
Download from: https://ngrok.com/download

## Step 2: Sign up and get auth token

1. Sign up at https://ngrok.com
2. Get your auth token from the dashboard
3. Configure it:

```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

## Step 3: Start ngrok tunnel

### Free tier (random URL)
```bash
ngrok http 3000
```

### Paid tier (static domain - $8/month)
```bash
ngrok http 3000 --domain=your-static-domain.ngrok.app
```

## Step 4: Update .env.local

```env
# Replace with your ngrok URL
WEBHOOK_URL=https://abc123.ngrok.app/api/webhooks/clerk
```

## Pros of ngrok
- ✅ Industry standard
- ✅ Great debugging tools
- ✅ Official Clerk documentation support

## Cons of ngrok
- ❌ Random URLs on free tier
- ❌ $8/month for static domains
- ❌ Bandwidth limitations

## 🏆 Why Cloudflare Tunnel is Better

For TalentHUB's enterprise B2B SaaS:
- **$0 cost** vs $8/month for ngrok
- **Static domains** included free
- **Enterprise security** by default
- **Production ready** with same setup
- **No bandwidth limits**

Cloudflare Tunnel provides the same reliability as ngrok with zero cost and better production transition.
