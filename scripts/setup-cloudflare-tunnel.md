# 🚀 TalentHUB Cloudflare Tunnel Setup Guide

## Prerequisites
- Cloudflare account (free)
- Domain name added to Cloudflare (can be free domain or existing)

## Step 1: Install cloudflared

### macOS (Homebrew)
```bash
brew install cloudflared
```

### Linux
```bash
wget -q https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared-linux-amd64.deb
```

### Windows
Download from: https://github.com/cloudflare/cloudflared/releases/latest

## Step 2: Authenticate with <PERSON>flare

```bash
cloudflared tunnel login
```

This opens your browser to authorize the tunnel. Choose your domain when prompted.

## Step 3: Create a Tunnel

```bash
# Create the tunnel (replace 'talenthub-dev' with your preferred name)
cloudflared tunnel create talenthub-dev
```

This returns a tunnel ID like: `3de42678-313b-4801-bd71-1e4dda81880b`

## Step 4: Create DNS Record

```bash
# Replace with your tunnel ID and desired subdomain
cloudflared tunnel route dns talenthub-dev dev-webhooks.yourdomain.com
```

## Step 5: Create Configuration File

Create `config/cloudflare-tunnel.yml`:

```yaml
tunnel: [YOUR_TUNNEL_ID]
credentials-file: /Users/<USER>/.cloudflared/[YOUR_TUNNEL_ID].json

ingress:
  - hostname: dev-webhooks.yourdomain.com
    service: http://localhost:3000
  - service: http_status:404
```

## Step 6: Start the Tunnel

```bash
cloudflared tunnel --config config/cloudflare-tunnel.yml run talenthub-dev
```

## Step 7: Update Environment Variables

Add to `.env.local`:
```env
# Cloudflare Tunnel Configuration
CLOUDFLARE_TUNNEL_URL=https://dev-webhooks.yourdomain.com
CLERK_WEBHOOK_SECRET=[get_from_clerk_dashboard]
WEBHOOK_URL=https://dev-webhooks.yourdomain.com/api/webhooks/clerk
```

## Step 8: Configure Clerk Webhook

1. Go to Clerk Dashboard → Webhooks
2. Add Endpoint: `https://dev-webhooks.yourdomain.com/api/webhooks/clerk`
3. Select events: `user.created`, `user.updated`, `organizationMembership.created`, etc.
4. Copy the signing secret to your `.env.local`

## Automation (Optional)

Add to `package.json`:
```json
{
  "scripts": {
    "dev:tunnel": "concurrently \"npm run dev\" \"cloudflared tunnel --config config/cloudflare-tunnel.yml run talenthub-dev\"",
    "tunnel:start": "cloudflared tunnel --config config/cloudflare-tunnel.yml run talenthub-dev"
  }
}
```

Then install concurrently:
```bash
npm install --save-dev concurrently
```

Now you can run: `npm run dev:tunnel` to start both your app and tunnel together.

## Production Deployment

For production, simply:
1. Create a new tunnel: `cloudflared tunnel create talenthub-prod`
2. Route to production domain: `cloudflared tunnel route dns talenthub-prod webhooks.yourdomain.com`
3. Update production environment variables
4. Deploy your app with the tunnel running

## Benefits of This Setup

✅ **Static URL**: Always the same webhook URL  
✅ **Free**: No monthly costs  
✅ **Secure**: Enterprise-grade Cloudflare infrastructure  
✅ **Production-Ready**: Same setup scales to production  
✅ **Professional**: Custom subdomain on your domain  
✅ **Reliable**: 99.9%+ uptime guaranteed by Cloudflare
