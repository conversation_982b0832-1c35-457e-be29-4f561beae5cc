/**
 * Test Supabase connection independently
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('=== SUPABASE CONNECTION TEST ===');
console.log('URL exists:', !!supabaseUrl);
console.log('Service key exists:', !!supabaseServiceKey);

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test basic connection
async function testConnection() {
  try {
    console.log('Testing basic connection...');
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*', { count: 'exact' })
      .limit(1);
    
    if (error) {
      console.error('❌ Supabase query error:', error);
      return false;
    }
    
    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection failed:', error);
    return false;
  }
}

testConnection().then(success => {
  if (success) {
    console.log('✅ Database connection is working');
  } else {
    console.log('❌ Database connection failed');
  }
});
